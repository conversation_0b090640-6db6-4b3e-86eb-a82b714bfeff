#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Módulo de gerenciamento de categorias
"""

from datetime import datetime
import sqlite3

class CategoryManager:
    def __init__(self, db_manager):
        self.db_manager = db_manager
    
    def create_category(self, user_id, name, description="", category_type="expense", 
                       color="#007ACC", icon=""):
        """Cria uma nova categoria"""
        try:
            # Validações
            if not name or len(name.strip()) == 0:
                raise ValueError("Nome da categoria é obrigatório")
            
            if len(name) > 100:
                raise ValueError("Nome da categoria deve ter no máximo 100 caracteres")
            
            if category_type not in ['income', 'expense']:
                raise ValueError("Tipo de categoria deve ser 'income' ou 'expense'")
            
            # Verificar se já existe categoria com mesmo nome e tipo para o usuário
            if self.category_name_exists(user_id, name, category_type):
                raise ValueError("Já existe uma categoria com este nome e tipo")
            
            # Validar cor (formato hexadecimal)
            if not color.startswith('#') or len(color) != 7:
                color = "#007ACC"  # Cor padrão
            
            # Inserir categoria
            query = '''
                INSERT INTO categories (user_id, name, description, category_type, color, icon)
                VALUES (?, ?, ?, ?, ?, ?)
            '''
            
            self.db_manager.execute_query(query, (
                user_id, name.strip(), description.strip(), 
                category_type, color, icon.strip()
            ))
            
            return True
            
        except Exception as e:
            raise Exception(f"Erro ao criar categoria: {str(e)}")
    
    def category_name_exists(self, user_id, name, category_type, exclude_id=None):
        """Verifica se já existe categoria com o mesmo nome e tipo"""
        query = '''
            SELECT COUNT(*) FROM categories 
            WHERE user_id = ? AND name = ? AND category_type = ?
        '''
        params = [user_id, name.strip(), category_type]
        
        if exclude_id:
            query += " AND id != ?"
            params.append(exclude_id)
        
        result = self.db_manager.execute_query(query, params)
        return result[0][0] > 0
    
    def get_user_categories(self, user_id, category_type=None, include_inactive=False, include_default=True):
        """Retorna categorias do usuário"""
        query = '''
            SELECT id, name, description, category_type, color, icon, is_active, created_at
            FROM categories
            WHERE (user_id = ? OR (user_id IS NULL AND ? = TRUE))
        '''
        
        params = [user_id, include_default]
        
        if category_type:
            query += " AND category_type = ?"
            params.append(category_type)
        
        if not include_inactive:
            query += " AND is_active = TRUE"
        
        query += " ORDER BY category_type, name"
        
        return self.db_manager.execute_query(query, params)
    
    def get_category_by_id(self, category_id, user_id=None):
        """Retorna categoria específica"""
        if user_id:
            query = '''
                SELECT id, name, description, category_type, color, icon, is_active, created_at
                FROM categories
                WHERE id = ? AND (user_id = ? OR user_id IS NULL)
            '''
            params = (category_id, user_id)
        else:
            query = '''
                SELECT id, name, description, category_type, color, icon, is_active, created_at
                FROM categories
                WHERE id = ?
            '''
            params = (category_id,)
        
        result = self.db_manager.execute_query(query, params)
        return result[0] if result else None
    
    def update_category(self, category_id, user_id, name=None, description=None, 
                       color=None, icon=None):
        """Atualiza categoria (apenas categorias do usuário, não as padrão)"""
        try:
            # Verificar se categoria existe e pertence ao usuário
            category = self.get_category_by_id(category_id, user_id)
            if not category:
                raise ValueError("Categoria não encontrada")
            
            # Não permitir edição de categorias padrão (user_id IS NULL)
            query_check = "SELECT user_id FROM categories WHERE id = ?"
            result = self.db_manager.execute_query(query_check, (category_id,))
            if result and result[0]['user_id'] is None:
                raise ValueError("Não é possível editar categorias padrão do sistema")
            
            updates = []
            params = []
            
            # Validar e preparar atualizações
            if name is not None:
                if not name or len(name.strip()) == 0:
                    raise ValueError("Nome da categoria é obrigatório")
                if len(name) > 100:
                    raise ValueError("Nome da categoria deve ter no máximo 100 caracteres")
                if self.category_name_exists(user_id, name, category['category_type'], category_id):
                    raise ValueError("Já existe uma categoria com este nome e tipo")
                
                updates.append("name = ?")
                params.append(name.strip())
            
            if description is not None:
                updates.append("description = ?")
                params.append(description.strip())
            
            if color is not None:
                if not color.startswith('#') or len(color) != 7:
                    color = "#007ACC"  # Cor padrão
                updates.append("color = ?")
                params.append(color)
            
            if icon is not None:
                updates.append("icon = ?")
                params.append(icon.strip())
            
            if not updates:
                return True  # Nada para atualizar
            
            # Executar atualização
            query = f"UPDATE categories SET {', '.join(updates)} WHERE id = ? AND user_id = ?"
            params.extend([category_id, user_id])
            
            self.db_manager.execute_query(query, params)
            return True
            
        except Exception as e:
            raise Exception(f"Erro ao atualizar categoria: {str(e)}")
    
    def toggle_category_status(self, category_id, user_id):
        """Ativa/desativa categoria (apenas categorias do usuário)"""
        try:
            # Verificar se é categoria do usuário
            query_check = "SELECT user_id FROM categories WHERE id = ?"
            result = self.db_manager.execute_query(query_check, (category_id,))
            if not result or result[0]['user_id'] != user_id:
                raise ValueError("Categoria não encontrada ou não pertence ao usuário")
            
            query = "UPDATE categories SET is_active = NOT is_active WHERE id = ? AND user_id = ?"
            self.db_manager.execute_query(query, (category_id, user_id))
            
            return True
            
        except Exception as e:
            raise Exception(f"Erro ao alterar status da categoria: {str(e)}")
    
    def delete_category(self, category_id, user_id):
        """Exclui categoria (apenas se não tiver transações)"""
        try:
            # Verificar se é categoria do usuário
            query_check = "SELECT user_id FROM categories WHERE id = ?"
            result = self.db_manager.execute_query(query_check, (category_id,))
            if not result or result[0]['user_id'] != user_id:
                raise ValueError("Categoria não encontrada ou não pertence ao usuário")
            
            # Verificar se há transações associadas
            query = "SELECT COUNT(*) FROM transactions WHERE category_id = ?"
            result = self.db_manager.execute_query(query, (category_id,))
            
            if result[0][0] > 0:
                raise ValueError("Não é possível excluir categoria com transações. Desative-a em vez disso.")
            
            # Excluir categoria
            query = "DELETE FROM categories WHERE id = ? AND user_id = ?"
            self.db_manager.execute_query(query, (category_id, user_id))
            
            return True
            
        except Exception as e:
            raise Exception(f"Erro ao excluir categoria: {str(e)}")
    
    def get_category_usage_stats(self, user_id, category_id=None, period_start=None, period_end=None):
        """Retorna estatísticas de uso das categorias"""
        try:
            base_query = '''
                SELECT 
                    c.id, c.name, c.category_type, c.color,
                    COUNT(t.id) as transaction_count,
                    COALESCE(SUM(t.amount), 0) as total_amount,
                    COALESCE(SUM(CASE WHEN t.is_paid = TRUE THEN t.amount ELSE 0 END), 0) as paid_amount,
                    COALESCE(AVG(t.amount), 0) as avg_amount
                FROM categories c
                LEFT JOIN transactions t ON c.id = t.category_id AND t.user_id = ?
            '''
            
            params = [user_id]
            
            # Filtros
            if category_id:
                base_query += " AND c.id = ?"
                params.append(category_id)
            
            if period_start:
                base_query += " AND (t.transaction_date IS NULL OR t.transaction_date >= ?)"
                params.append(period_start)
            
            if period_end:
                base_query += " AND (t.transaction_date IS NULL OR t.transaction_date <= ?)"
                params.append(period_end)
            
            base_query += '''
                WHERE (c.user_id = ? OR c.user_id IS NULL) AND c.is_active = TRUE
                GROUP BY c.id, c.name, c.category_type, c.color
                ORDER BY total_amount DESC
            '''
            params.append(user_id)
            
            return self.db_manager.execute_query(base_query, params)
            
        except Exception as e:
            raise Exception(f"Erro ao obter estatísticas: {str(e)}")
    
    def get_categories_by_type_summary(self, user_id):
        """Retorna resumo das categorias por tipo"""
        try:
            query = '''
                SELECT 
                    category_type,
                    COUNT(*) as total_categories,
                    COUNT(CASE WHEN user_id = ? THEN 1 END) as user_categories,
                    COUNT(CASE WHEN user_id IS NULL THEN 1 END) as default_categories
                FROM categories
                WHERE (user_id = ? OR user_id IS NULL) AND is_active = TRUE
                GROUP BY category_type
            '''
            
            return self.db_manager.execute_query(query, (user_id, user_id))
            
        except Exception as e:
            raise Exception(f"Erro ao obter resumo por tipo: {str(e)}")
    
    def copy_default_categories_to_user(self, user_id):
        """Copia categorias padrão para o usuário (para personalização)"""
        try:
            # Buscar categorias padrão
            query = '''
                SELECT name, description, category_type, color, icon
                FROM categories
                WHERE user_id IS NULL AND is_active = TRUE
            '''
            
            default_categories = self.db_manager.execute_query(query)
            
            copied_count = 0
            for category in default_categories:
                # Verificar se usuário já tem categoria com mesmo nome e tipo
                if not self.category_name_exists(user_id, category['name'], category['category_type']):
                    self.create_category(
                        user_id=user_id,
                        name=category['name'],
                        description=category['description'],
                        category_type=category['category_type'],
                        color=category['color'],
                        icon=category['icon']
                    )
                    copied_count += 1
            
            return copied_count
            
        except Exception as e:
            raise Exception(f"Erro ao copiar categorias padrão: {str(e)}")
    
    def get_most_used_categories(self, user_id, limit=5, category_type=None):
        """Retorna categorias mais utilizadas"""
        try:
            query = '''
                SELECT 
                    c.id, c.name, c.category_type, c.color,
                    COUNT(t.id) as usage_count,
                    COALESCE(SUM(t.amount), 0) as total_amount
                FROM categories c
                JOIN transactions t ON c.id = t.category_id
                WHERE t.user_id = ?
            '''
            
            params = [user_id]
            
            if category_type:
                query += " AND c.category_type = ?"
                params.append(category_type)
            
            query += '''
                GROUP BY c.id, c.name, c.category_type, c.color
                ORDER BY usage_count DESC, total_amount DESC
                LIMIT ?
            '''
            params.append(limit)
            
            return self.db_manager.execute_query(query, params)
            
        except Exception as e:
            raise Exception(f"Erro ao obter categorias mais usadas: {str(e)}")
