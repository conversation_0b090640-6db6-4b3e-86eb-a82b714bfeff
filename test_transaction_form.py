#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Teste do formulário de transação
"""

import sys
import os
import tkinter as tk
from tkinter import messagebox

# Adicionar o diretório src ao path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from database import DatabaseManager
from gui.transaction_form_new import TransactionFormNew

def test_transaction_form():
    """Testa o formulário de transação"""
    print("🧪 TESTE DO FORMULÁRIO DE TRANSAÇÃO")
    print("=" * 50)
    
    try:
        # Inicializar banco de dados
        db_manager = DatabaseManager()
        
        # Verificar se existem dados necessários
        users = db_manager.execute_query("SELECT id, username FROM users WHERE username = 'admin'")
        if not users:
            print("❌ Usuário admin não encontrado!")
            return
        
        user_id = users[0]['id']
        print(f"✅ Usuário encontrado: {users[0]['username']} (ID: {user_id})")
        
        # Verificar carteiras
        wallets = db_manager.execute_query("SELECT COUNT(*) FROM wallets WHERE user_id = ?", (user_id,))
        wallet_count = wallets[0][0]
        print(f"✅ Carteiras disponíveis: {wallet_count}")
        
        # Verificar categorias
        categories = db_manager.execute_query("SELECT COUNT(*) FROM categories WHERE category_type = 'income'")
        category_count = categories[0][0]
        print(f"✅ Categorias de receita: {category_count}")
        
        # Contar transações antes
        count_before = db_manager.execute_query("SELECT COUNT(*) FROM transactions")[0][0]
        print(f"✅ Transações antes do teste: {count_before}")
        
        # Criar janela principal
        root = tk.Tk()
        root.withdraw()  # Esconder janela principal
        
        print("\n🔧 Testando criação de transação via formulário...")
        
        # Criar formulário de receita
        form = TransactionFormNew(root, db_manager, user_id, 'income')
        
        # Simular preenchimento do formulário
        if hasattr(form, 'description_entry'):
            form.description_entry.delete(0, tk.END)
            form.description_entry.insert(0, "Teste via formulário")
            print("   ✅ Descrição preenchida")
        
        if hasattr(form, 'amount_entry'):
            form.amount_entry.delete(0, tk.END)
            form.amount_entry.insert(0, "500.00")
            print("   ✅ Valor preenchido")
        
        # Verificar se combos foram carregados
        if hasattr(form, 'wallet_combo') and form.wallet_combo['values']:
            form.wallet_combo.current(0)
            print("   ✅ Carteira selecionada")
        
        if hasattr(form, 'category_combo') and form.category_combo['values']:
            form.category_combo.current(0)
            print("   ✅ Categoria selecionada")
        
        # Simular salvamento
        print("\n💾 Tentando salvar transação...")
        
        # Interceptar messagebox para capturar resultado
        original_showinfo = messagebox.showinfo
        original_showerror = messagebox.showerror
        
        save_success = False
        error_message = None
        
        def mock_showinfo(title, message):
            nonlocal save_success
            if "sucesso" in message.lower():
                save_success = True
            print(f"   📢 {title}: {message}")
        
        def mock_showerror(title, message):
            nonlocal error_message
            error_message = message
            print(f"   ❌ {title}: {message}")
        
        messagebox.showinfo = mock_showinfo
        messagebox.showerror = mock_showerror
        
        try:
            # Chamar método save
            form.save()
            
            # Restaurar messagebox
            messagebox.showinfo = original_showinfo
            messagebox.showerror = original_showerror
            
            # Verificar resultado
            if save_success:
                print("   ✅ Formulário reportou sucesso!")
                
                # Verificar se transação foi realmente criada
                count_after = db_manager.execute_query("SELECT COUNT(*) FROM transactions")[0][0]
                print(f"   📊 Transações depois: {count_after}")
                
                if count_after > count_before:
                    print("✅ TESTE PASSOU! Transação foi criada via formulário!")
                    
                    # Mostrar última transação
                    last_transaction = db_manager.execute_query("""
                        SELECT t.*, w.name as wallet_name, c.name as category_name 
                        FROM transactions t
                        JOIN wallets w ON t.wallet_id = w.id
                        JOIN categories c ON t.category_id = c.id
                        ORDER BY t.id DESC LIMIT 1
                    """)[0]
                    
                    print(f"\n📋 Transação criada via formulário:")
                    print(f"   ID: {last_transaction['id']}")
                    print(f"   Descrição: {last_transaction['description']}")
                    print(f"   Valor: R$ {last_transaction['amount']:,.2f}")
                    print(f"   Carteira: {last_transaction['wallet_name']}")
                    print(f"   Categoria: {last_transaction['category_name']}")
                    
                else:
                    print("❌ TESTE FALHOU! Formulário reportou sucesso mas transação não foi criada!")
                    
            elif error_message:
                print(f"❌ TESTE FALHOU! Erro no formulário: {error_message}")
            else:
                print("❌ TESTE FALHOU! Nenhuma resposta do formulário!")
                
        except Exception as e:
            messagebox.showinfo = original_showinfo
            messagebox.showerror = original_showerror
            print(f"❌ ERRO durante salvamento: {str(e)}")
            import traceback
            traceback.print_exc()
        
        # Fechar janela
        if hasattr(form, 'window') and form.window.winfo_exists():
            form.window.destroy()
        root.destroy()
        
    except Exception as e:
        print(f"❌ ERRO NO TESTE: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_transaction_form()
