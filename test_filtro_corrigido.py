#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Teste do filtro corrigido para parcelas
"""

import sys
import os
import tkinter as tk

# Adicionar o diretório src ao path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from database import DatabaseManager
from auth import AuthManager
from gui.main_window import MainWindow

def test_filtro_corrigido():
    """Testa se o filtro corrigido mostra todas as parcelas"""
    print("✅ TESTE DO FILTRO CORRIGIDO")
    print("=" * 50)
    
    try:
        # Inicializar banco de dados
        db_manager = DatabaseManager()
        db_manager.initialize_database()
        
        auth_manager = AuthManager(db_manager)
        
        # Fazer login
        user_data = auth_manager.authenticate_user('admin', 'admin123')
        if not user_data:
            print("❌ Falha no login!")
            return
        
        print(f"✅ Login realizado: {user_data['full_name']}")
        
        # Criar janela principal
        root = tk.Tk()
        root.title("Teste Filtro Corrigido")
        root.geometry("1200x800")
        
        # Função de logout mock
        def mock_logout():
            print("Logout chamado")
        
        # Criar MainWindow
        main_window = MainWindow(root, db_manager, auth_manager, user_data, mock_logout)
        
        print("✅ Janela principal criada com sucesso!")
        
        # Navegar para a aba de transações
        main_window.notebook.select(2)  # Aba de transações
        
        def test_default_filter():
            try:
                root.update_idletasks()
                
                # Verificar filtro padrão
                default_filter = main_window.period_combo.get()
                print(f"\n🔍 FILTRO PADRÃO: {default_filter}")
                
                # Carregar transações com filtro padrão
                main_window.load_transactions()
                
                # Contar transações na interface
                children = main_window.transactions_tree.get_children()
                print(f"📊 Transações visíveis com filtro '{default_filter}': {len(children)}")
                
                # Contar parcelas específicas de teste
                parcelas_visiveis = 0
                parcelas_teste = []
                
                for child in children:
                    values = main_window.transactions_tree.item(child)['values']
                    if len(values) > 1 and 'Teste 15 parcelas' in str(values[1]):
                        parcelas_visiveis += 1
                        # Extrair número da parcela da descrição
                        desc = str(values[1])
                        if 'Parcela' in desc:
                            try:
                                parcela_info = desc.split('Parcela ')[1].split('/')[0]
                                parcelas_teste.append(int(parcela_info))
                            except:
                                pass
                
                print(f"💳 Parcelas de '15 parcelas' visíveis: {parcelas_visiveis}")
                
                if parcelas_teste:
                    parcelas_teste.sort()
                    print(f"📋 Números das parcelas visíveis: {parcelas_teste}")
                    
                    if len(parcelas_teste) == 15:
                        print("🎉 TODAS AS 15 PARCELAS ESTÃO VISÍVEIS!")
                    else:
                        faltando = [i for i in range(1, 16) if i not in parcelas_teste]
                        print(f"⚠️  Parcelas faltando: {faltando}")
                
                # Testar filtro "Incluir Futuras"
                print(f"\n🔍 TESTANDO FILTRO 'Incluir Futuras':")
                main_window.period_combo.set("Incluir Futuras")
                main_window.load_transactions()
                
                children_futuras = main_window.transactions_tree.get_children()
                print(f"📊 Transações com 'Incluir Futuras': {len(children_futuras)}")
                
                parcelas_futuras = 0
                for child in children_futuras:
                    values = main_window.transactions_tree.item(child)['values']
                    if len(values) > 1 and 'Teste 15 parcelas' in str(values[1]):
                        parcelas_futuras += 1
                
                print(f"💳 Parcelas de '15 parcelas' com filtro futuras: {parcelas_futuras}")
                
                # Testar filtro "Este Mês" para comparação
                print(f"\n🔍 TESTANDO FILTRO 'Este Mês' (para comparação):")
                main_window.period_combo.set("Este Mês")
                main_window.load_transactions()
                
                children_mes = main_window.transactions_tree.get_children()
                print(f"📊 Transações com 'Este Mês': {len(children_mes)}")
                
                parcelas_mes = 0
                for child in children_mes:
                    values = main_window.transactions_tree.item(child)['values']
                    if len(values) > 1 and 'Teste 15 parcelas' in str(values[1]):
                        parcelas_mes += 1
                
                print(f"💳 Parcelas de '15 parcelas' com filtro mês: {parcelas_mes}")
                
                # Resumo
                print(f"\n📊 RESUMO DOS TESTES:")
                print(f"   Filtro 'Todos': {parcelas_visiveis} parcelas")
                print(f"   Filtro 'Incluir Futuras': {parcelas_futuras} parcelas")
                print(f"   Filtro 'Este Mês': {parcelas_mes} parcelas")
                print(f"   Esperado: 15 parcelas")
                
                if parcelas_visiveis == 15:
                    print(f"\n🎉 PROBLEMA RESOLVIDO!")
                    print(f"   ✅ Filtro padrão agora mostra todas as parcelas")
                    print(f"   ✅ Usuário verá todas as 15 parcelas por padrão")
                else:
                    print(f"\n⚠️  PROBLEMA AINDA EXISTE:")
                    print(f"   ❌ Filtro padrão não mostra todas as parcelas")
                    print(f"   💡 Usuário precisa mudar filtro manualmente")
                
            except Exception as e:
                print(f"❌ Erro ao testar filtro: {str(e)}")
                import traceback
                traceback.print_exc()
        
        # Aguardar renderização e testar
        root.after(1000, test_default_filter)
        
        print("\n✅ TESTE DO FILTRO CORRIGIDO INICIADO!")
        
        # Executar por tempo limitado
        root.after(8000, root.quit)
        root.mainloop()
        root.destroy()
        
    except Exception as e:
        print(f"❌ ERRO GERAL: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_filtro_corrigido()
