#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Teste individual dos formulários
"""

import sys
import os
import tkinter as tk

# Adicionar o diretório src ao path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from database import DatabaseManager

def test_individual_forms():
    """Testa cada formulário individualmente"""
    print("🧪 TESTE INDIVIDUAL DOS FORMULÁRIOS")
    print("=" * 60)
    
    try:
        # Inicializar banco de dados
        db_manager = DatabaseManager()
        
        # Verificar usuário admin
        users = db_manager.execute_query("SELECT id FROM users WHERE username = 'admin'")
        if not users:
            print("❌ Usuário admin não encontrado!")
            return
        
        user_id = users[0]['id']
        print(f"✅ Usuário admin encontrado (ID: {user_id})")
        
        # <PERSON>riar janela principal
        root = tk.Tk()
        root.title("Teste Individual")
        root.geometry("400x300")
        
        print("\n1️⃣ TESTANDO WALLET DIALOG...")
        try:
            from gui.wallet_dialog import WalletDialog
            print("   ✅ Import WalletDialog - OK")
            
            # Criar e destruir rapidamente
            dialog = WalletDialog(root, db_manager, user_id)
            dialog.window.destroy()
            print("   ✅ WalletDialog criado e destruído - OK")
            
        except Exception as e:
            print(f"   ❌ Erro WalletDialog: {str(e)}")
        
        print("\n2️⃣ TESTANDO TRANSACTION FORM NEW...")
        try:
            from gui.transaction_form_new import TransactionFormNew
            print("   ✅ Import TransactionFormNew - OK")
            
            # Criar e destruir rapidamente
            form = TransactionFormNew(root, db_manager, user_id, 'income')
            form.window.destroy()
            print("   ✅ TransactionFormNew (income) criado e destruído - OK")
            
            form2 = TransactionFormNew(root, db_manager, user_id, 'expense')
            form2.window.destroy()
            print("   ✅ TransactionFormNew (expense) criado e destruído - OK")
            
        except Exception as e:
            print(f"   ❌ Erro TransactionFormNew: {str(e)}")
            import traceback
            traceback.print_exc()
        
        root.destroy()
        
        print("\n✅ TESTE INDIVIDUAL CONCLUÍDO!")
        print("Se todos os testes passaram, os formulários estão funcionando.")
        
    except Exception as e:
        print(f"❌ ERRO GERAL: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_individual_forms()
