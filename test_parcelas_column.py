#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Teste da coluna de parcelas na aba de transações
"""

import sys
import os
import tkinter as tk

# Adicionar o diretório src ao path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from database import DatabaseManager
from auth import AuthManager
from gui.main_window import MainWindow

def test_parcelas_column():
    """Testa se a coluna de parcelas está sendo exibida"""
    print("💳 TESTE DA COLUNA DE PARCELAS")
    print("=" * 50)
    
    try:
        # Inicializar banco de dados
        db_manager = DatabaseManager()
        db_manager.initialize_database()
        
        auth_manager = AuthManager(db_manager)
        
        # Fazer login
        user_data = auth_manager.authenticate_user('admin', 'admin123')
        if not user_data:
            print("❌ Falha no login!")
            return
        
        print(f"✅ Login realizado: {user_data['full_name']}")
        
        # Criar janela principal
        root = tk.Tk()
        root.title("Teste Coluna Parcelas")
        root.geometry("1200x800")
        
        # Função de logout mock
        def mock_logout():
            print("Logout chamado")
        
        # Criar MainWindow
        main_window = MainWindow(root, db_manager, auth_manager, user_data, mock_logout)
        
        print("✅ Janela principal criada com sucesso!")
        
        # Navegar para a aba de transações
        main_window.notebook.select(2)  # Aba de transações
        
        # Verificar colunas da tabela de transações
        def check_columns():
            try:
                root.update_idletasks()
                
                # Obter colunas da treeview
                columns = main_window.transactions_tree['columns']
                print(f"📊 Colunas encontradas: {list(columns)}")
                
                # Verificar se a coluna 'Parcelas' está presente
                if 'Parcelas' in columns:
                    print("✅ Coluna 'Parcelas' encontrada!")
                    
                    # Verificar posição da coluna
                    column_index = list(columns).index('Parcelas')
                    print(f"📍 Posição da coluna 'Parcelas': {column_index + 1}")
                    
                    # Verificar largura da coluna
                    column_width = main_window.transactions_tree.column('Parcelas', 'width')
                    print(f"📏 Largura da coluna 'Parcelas': {column_width}px")
                    
                else:
                    print("❌ Coluna 'Parcelas' NÃO encontrada!")
                
                # Verificar headers das colunas
                print("\n📋 Headers das colunas:")
                for i, col in enumerate(columns):
                    header = main_window.transactions_tree.heading(col, 'text')
                    width = main_window.transactions_tree.column(col, 'width')
                    print(f"   {i+1}. {col}: '{header}' ({width}px)")
                
                # Carregar transações para verificar dados
                print("\n🔄 Carregando transações...")
                main_window.load_transactions()
                
                # Verificar se há dados na tabela
                children = main_window.transactions_tree.get_children()
                print(f"📊 Transações carregadas: {len(children)}")
                
                if children:
                    # Verificar primeira transação
                    first_item = children[0]
                    values = main_window.transactions_tree.item(first_item)['values']
                    
                    print(f"\n📝 Primeira transação:")
                    for i, (col, value) in enumerate(zip(columns, values)):
                        print(f"   {col}: {value}")
                        
                    # Verificar especificamente a coluna de parcelas
                    if len(values) > 5:  # Parcelas deve estar na posição 5
                        parcelas_value = values[5]
                        print(f"\n💳 Valor da coluna Parcelas: '{parcelas_value}'")
                        
                        if 'x' in str(parcelas_value):
                            print("✅ Formato de parcelas correto (ex: 1x, 2x, etc.)")
                        else:
                            print("⚠️  Formato de parcelas pode estar incorreto")
                
                else:
                    print("⚠️  Nenhuma transação encontrada para verificar dados")
                
            except Exception as e:
                print(f"❌ Erro ao verificar colunas: {str(e)}")
                import traceback
                traceback.print_exc()
        
        # Aguardar renderização e verificar
        root.after(1000, check_columns)
        
        print("\n✅ TESTE DA COLUNA DE PARCELAS INICIADO!")
        print("🔍 Verificando colunas em 1 segundo...")
        
        # Executar por tempo limitado
        root.after(5000, root.quit)
        root.mainloop()
        root.destroy()
        
    except Exception as e:
        print(f"❌ ERRO GERAL: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_parcelas_column()
