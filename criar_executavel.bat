@echo off
chcp 65001 >nul
title Criador de Executável - Gestão de Contas

cls
echo.
echo ████████████████████████████████████████████████████████████
echo    🎯 CRIADOR DE EXECUTÁVEL - GESTÃO DE CONTAS
echo    Sistema Completo com Ícone Personalizado
echo ████████████████████████████████████████████████████████████
echo.

echo 🔍 Verificando Python...
py --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Python não encontrado!
    echo.
    echo 💡 Instale o Python em: https://python.org
    echo    Certifique-se de marcar "Add to PATH"
    echo.
    pause
    exit /b 1
)

echo ✅ Python encontrado!
echo.

echo 🚀 Iniciando criação do executável...
echo    Este processo pode demorar alguns minutos...
echo.

py criar_executavel_final.py

echo.
echo ✅ Processo concluído!
echo.

if exist "GestaoContas_Executavel\GestaoContas.exe" (
    echo 🎉 SUCESSO! Executável criado em: GestaoContas_Executavel\
    echo.
    echo 🚀 Para testar, execute:
    echo    GestaoContas_Executavel\GestaoContas.exe
    echo.
    echo 🔑 Login padrão:
    echo    Usuário: admin
    echo    Senha: admin123
    echo.

    set /p test="Deseja testar o executável agora? (s/n): "
    if /i "%test%"=="s" (
        echo.
        echo 🚀 Iniciando aplicação...
        start "" "GestaoContas_Executavel\GestaoContas.exe"
    )
) else (
    echo ❌ Erro na criação do executável
    echo    Verifique as mensagens acima para mais detalhes
)

echo.
pause
