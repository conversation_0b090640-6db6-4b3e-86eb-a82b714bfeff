@echo off
chcp 65001 >nul
title 🚗 Sistema de Gestão de Contas

cls
echo.
echo ████████████████████████████████████████████████████████████████████
echo █                                                                  █
echo █            🚗 SISTEMA DE GESTÃO DE CONTAS                       █
echo █            Versão 2.0 - Com Gerenciamento de Veículos           █
echo █                                                                  █
echo ████████████████████████████████████████████████████████████████████
echo.
echo 🔑 CREDENCIAIS DE LOGIN:
echo    👤 Usuário: admin
echo    🔐 Senha: admin123
echo.
echo 📋 FUNCIONALIDADES:
echo    ✅ Controle financeiro completo
echo    ✅ Gerenciamento de carteiras  
echo    ✅ Transações e relatórios
echo    ✅ Cadastro de veículos
echo    ✅ Controle de manutenção
echo    ✅ Registro de combustível
echo.
echo ⚡ INICIANDO APLICAÇÃO...
echo.

REM Verificar qual versão executar
if exist "dist\GestaoContas.exe" (
    echo 🚀 Executando versão otimizada...
    echo.
    cd dist
    start "" "GestaoContas.exe"
    cd ..
    echo ✅ Aplicação iniciada!
    echo 💡 Procure a janela na barra de tarefas
) else (
    echo 🐍 Executando versão Python...
    echo.
    py executar_terminal.py
)

echo.
echo 🏁 Script finalizado.
echo 💡 A aplicação continua rodando em segundo plano.
echo.
timeout /t 3 >nul
