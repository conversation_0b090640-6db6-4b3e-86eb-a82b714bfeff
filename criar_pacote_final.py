#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Cria pacote final para download
"""

import zipfile
import shutil
from pathlib import Path

def criar_pacote_completo():
    """Cria pacote completo para download"""
    print("Criando pacote completo para download...")
    
    # Criar diretório de distribuição
    dist_dir = Path("GestaoContasCompleto_v1.0.0")
    if dist_dir.exists():
        shutil.rmtree(dist_dir)
    
    dist_dir.mkdir()
    
    # Copiar executável
    exe_source = Path("dist/GestaoContasCompleto.exe")
    if exe_source.exists():
        shutil.copy2(exe_source, dist_dir / "GestaoContasCompleto.exe")
        print(f"✓ Executável copiado ({exe_source.stat().st_size / (1024*1024):.1f} MB)")
    else:
        print("✗ Executável não encontrado")
        return False
    
    # Criar diretórios necessários
    (dist_dir / "data").mkdir()
    (dist_dir / "backups").mkdir()
    print("✓ Diretórios criados")
    
    # Copiar documentação
    docs = ["README.md", "INSTALACAO.md", "SOLUCAO_PROBLEMAS.md", "VERSOES_DISPONIVEIS.md"]
    for doc in docs:
        if Path(doc).exists():
            shutil.copy2(doc, dist_dir / doc)
            print(f"✓ {doc} copiado")
    
    # Criar arquivo de instruções principais
    instructions = """🎉 SISTEMA DE GESTÃO DE CONTAS - VERSÃO COMPLETA v1.0.0

═══════════════════════════════════════════════════════════════

🚀 COMO USAR:

1. Execute: GestaoContasCompleto.exe
2. Login: admin / admin123
3. Pronto! Sistema funcionando!

═══════════════════════════════════════════════════════════════

✅ FUNCIONALIDADES COMPLETAS:

📊 DASHBOARD
- Resumo financeiro em tempo real
- Transações recentes
- Alertas importantes

💰 CARTEIRAS
- Criar, editar e excluir carteiras
- Múltiplos tipos (Corrente, Poupança, Crédito, Dinheiro)
- Controle de saldo automático

💸 TRANSAÇÕES
- Receitas e despesas completas
- Categorização automática
- Controle de vencimentos
- Status de pagamento

📈 RELATÓRIOS
- Relatório por categoria
- Fluxo de caixa mensal
- Contas a vencer

⚙️ CONFIGURAÇÕES
- Gerenciamento de categorias
- Backup e restauração
- Configurações do sistema

🛡️ ADMINISTRAÇÃO (apenas admin)
- Gerenciamento de usuários
- Logs do sistema
- Estatísticas

🚨 SISTEMA DE ALERTAS
- Contas vencendo
- Contas em atraso
- Saldo baixo/negativo

═══════════════════════════════════════════════════════════════

📁 ESTRUTURA:

GestaoContasCompleto.exe  ← APLICAÇÃO PRINCIPAL
data/                     ← Banco de dados (criado automaticamente)
backups/                  ← Backups automáticos
README.md                 ← Documentação completa
INSTALACAO.md            ← Guia de instalação
SOLUCAO_PROBLEMAS.md     ← Solução de problemas

═══════════════════════════════════════════════════════════════

🔧 PROBLEMAS?

1. Execute como administrador
2. Verifique antivírus
3. Consulte SOLUCAO_PROBLEMAS.md
4. Delete pasta 'data' se houver erro de banco

═══════════════════════════════════════════════════════════════

💡 DICAS:

• Altere a senha padrão após primeiro acesso
• Faça backups regulares (Menu → Arquivo → Backup)
• Use categorias para organizar melhor
• Configure alertas de vencimento

═══════════════════════════════════════════════════════════════

🎯 SISTEMA 100% FUNCIONAL E COMPLETO!

Desenvolvido em Python com Tkinter
© 2024 - Sistema de Gestão Financeira

═══════════════════════════════════════════════════════════════"""
    
    with open(dist_dir / "🚀 LEIA-ME PRIMEIRO.txt", 'w', encoding='utf-8') as f:
        f.write(instructions)
    
    print("✓ Instruções principais criadas")
    
    # Criar arquivo ZIP
    zip_path = "GestaoContasCompleto_v1.0.0.zip"
    
    with zipfile.ZipFile(zip_path, 'w', zipfile.ZIP_DEFLATED) as zipf:
        for file_path in dist_dir.rglob('*'):
            if file_path.is_file():
                arcname = file_path.relative_to(dist_dir.parent)
                zipf.write(file_path, arcname)
                print(f"  + {arcname}")
    
    zip_size = Path(zip_path).stat().st_size / (1024 * 1024)
    print(f"✓ Pacote ZIP criado: {zip_path} ({zip_size:.1f} MB)")
    
    return True

def criar_pacote_simples():
    """Cria pacote simples para download"""
    print("\nCriando pacote simples para download...")
    
    # Criar diretório de distribuição
    dist_dir = Path("GestaoContasSimples_v1.0.0")
    if dist_dir.exists():
        shutil.rmtree(dist_dir)
    
    dist_dir.mkdir()
    
    # Copiar executável simples
    exe_source = Path("dist/GestaoContasSimples.exe")
    if exe_source.exists():
        shutil.copy2(exe_source, dist_dir / "GestaoContasSimples.exe")
        print(f"✓ Executável simples copiado ({exe_source.stat().st_size / (1024*1024):.1f} MB)")
    else:
        print("✗ Executável simples não encontrado")
        return False
    
    # Criar diretórios necessários
    (dist_dir / "data").mkdir()
    (dist_dir / "backups").mkdir()
    
    # Criar instruções simples
    simple_instructions = """🎯 SISTEMA DE GESTÃO DE CONTAS - VERSÃO SIMPLES v1.0.0

COMO USAR:
1. Execute: GestaoContasSimples.exe
2. Login: admin / admin123
3. Pronto!

FUNCIONALIDADES:
✓ Dashboard básico
✓ Visualização de carteiras
✓ Visualização de transações
✓ Backup básico

Para funcionalidades completas, use a versão completa.

© 2024 - Sistema de Gestão Financeira"""
    
    with open(dist_dir / "LEIA-ME.txt", 'w', encoding='utf-8') as f:
        f.write(simple_instructions)
    
    # Criar arquivo ZIP
    zip_path = "GestaoContasSimples_v1.0.0.zip"
    
    with zipfile.ZipFile(zip_path, 'w', zipfile.ZIP_DEFLATED) as zipf:
        for file_path in dist_dir.rglob('*'):
            if file_path.is_file():
                arcname = file_path.relative_to(dist_dir.parent)
                zipf.write(file_path, arcname)
    
    zip_size = Path(zip_path).stat().st_size / (1024 * 1024)
    print(f"✓ Pacote ZIP simples criado: {zip_path} ({zip_size:.1f} MB)")
    
    return True

def main():
    """Função principal"""
    print("CRIADOR DE PACOTES FINAIS")
    print("=" * 50)
    
    success_count = 0
    
    if criar_pacote_completo():
        success_count += 1
    
    if criar_pacote_simples():
        success_count += 1
    
    print("\n" + "=" * 50)
    print(f"✓ {success_count} pacote(s) criado(s) com sucesso!")
    
    if success_count > 0:
        print("\n📦 ARQUIVOS PARA DOWNLOAD:")
        
        if Path("GestaoContasCompleto_v1.0.0.zip").exists():
            size = Path("GestaoContasCompleto_v1.0.0.zip").stat().st_size / (1024*1024)
            print(f"  • GestaoContasCompleto_v1.0.0.zip ({size:.1f} MB) - VERSÃO COMPLETA")
        
        if Path("GestaoContasSimples_v1.0.0.zip").exists():
            size = Path("GestaoContasSimples_v1.0.0.zip").stat().st_size / (1024*1024)
            print(f"  • GestaoContasSimples_v1.0.0.zip ({size:.1f} MB) - VERSÃO SIMPLES")
        
        print("\n🎯 RECOMENDAÇÃO:")
        print("  Use GestaoContasCompleto_v1.0.0.zip para todas as funcionalidades")
        
        print("\n🚀 COMO USAR:")
        print("  1. Baixe o arquivo ZIP")
        print("  2. Extraia em uma pasta")
        print("  3. Execute o arquivo .exe")
        print("  4. Login: admin/admin123")
    
    print("\n" + "=" * 50)

if __name__ == "__main__":
    main()
