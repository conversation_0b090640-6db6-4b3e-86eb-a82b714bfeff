@echo off
chcp 65001 >nul
title Teste do Executável - Gestão de Contas

cls
echo.
echo ████████████████████████████████████████████████████████████
echo    🧪 TESTE DO EXECUTÁVEL - GESTÃO DE CONTAS
echo    Verificação e Execução do Sistema
echo ████████████████████████████████████████████████████████████
echo.

echo 🔍 Verificando arquivos...

if not exist "GestaoContas_Executavel\GestaoContas.exe" (
    echo ❌ Executável não encontrado!
    echo.
    echo 💡 Execute primeiro: CRIAR_EXECUTAVEL.bat
    echo.
    pause
    exit /b 1
)

echo ✅ Executável encontrado: GestaoContas_Executavel\GestaoContas.exe

if exist "GestaoContas_Executavel\gestao_contas.ico" (
    echo ✅ Ícone encontrado: gestao_contas.ico
) else (
    echo ⚠️ Ícone não encontrado
)

if exist "GestaoContas_Executavel\LEIA-ME.txt" (
    echo ✅ Instruções encontradas: LEIA-ME.txt
) else (
    echo ⚠️ Instruções não encontradas
)

echo.
echo 📁 Verificando estrutura de pastas...

if exist "GestaoContas_Executavel\data" (
    echo ✅ Pasta data/ criada
) else (
    echo ❌ Pasta data/ não encontrada
)

if exist "GestaoContas_Executavel\backups" (
    echo ✅ Pasta backups/ criada
) else (
    echo ❌ Pasta backups/ não encontrada
)

if exist "GestaoContas_Executavel\logs" (
    echo ✅ Pasta logs/ criada
) else (
    echo ❌ Pasta logs/ não encontrada
)

echo.
echo 📊 Informações do executável:

for %%F in ("GestaoContas_Executavel\GestaoContas.exe") do (
    set size=%%~zF
    set /a sizeMB=!size!/1024/1024
    echo    Tamanho: !sizeMB! MB
)

echo.
echo 🚀 EXECUTANDO APLICAÇÃO...
echo.
echo 💡 Credenciais de login:
echo    👤 Usuário: admin
echo    🔑 Senha: admin123
echo.
echo 📋 Funcionalidades disponíveis:
echo    • 💰 Gestão financeira completa
echo    • 🚗 Gerenciamento de veículos
echo    • 👥 Sistema de usuários
echo    • 📊 Relatórios avançados
echo    • 🔄 Backup automático
echo.

echo ⏳ Iniciando aplicação...
cd "GestaoContas_Executavel"
start "" "GestaoContas.exe"

echo.
echo ✅ Aplicação iniciada!
echo.
echo 🔍 Verifique se:
echo    • A janela da aplicação abriu corretamente
echo    • O ícone aparece na barra de tarefas
echo    • O login funciona com as credenciais fornecidas
echo    • Todas as funcionalidades estão acessíveis
echo.
echo 📝 Em caso de problemas:
echo    • Execute como administrador
echo    • Verifique se o antivírus não está bloqueando
echo    • Consulte o arquivo LEIA-ME.txt
echo.

pause
