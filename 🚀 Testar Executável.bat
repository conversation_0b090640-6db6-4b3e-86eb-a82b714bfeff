@echo off
chcp 65001 >nul
title Teste do Executável - Gestão de Contas

echo.
echo ========================================
echo    🚀 TESTE DO EXECUTÁVEL
echo    Sistema de Gestão de Contas v2.0
echo ========================================
echo.

echo 🔍 Verificando arquivos...

if not exist "GestaoContas_v2.0\GestaoContas.exe" (
    echo ❌ Executável não encontrado!
    echo.
    echo 💡 Execute primeiro: "🎨 Criar Executável com Ícone.bat"
    echo.
    pause
    exit /b 1
)

echo ✅ Executável encontrado!

if exist "GestaoContas_v2.0\app_icon.ico" (
    echo ✅ Ícone encontrado!
) else (
    echo ⚠️ Ícone não encontrado
)

echo.
echo 🚀 Executando aplicação...
echo.
echo 💡 Credenciais de login:
echo    Usuário: admin
echo    Senha: admin123
echo.

cd "GestaoContas_v2.0"
start "" "GestaoContas.exe"

echo ✅ Aplicação iniciada!
echo.
echo 📋 Verifique se:
echo   • A janela da aplicação abriu
echo   • O ícone aparece na barra de tarefas
echo   • O login funciona corretamente
echo.

pause
