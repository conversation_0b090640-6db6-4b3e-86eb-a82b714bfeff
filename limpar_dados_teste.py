#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Script para limpar dados automáticos de teste
"""

import sys
import os
import tkinter as tk
from tkinter import messagebox

# Adicionar o diretório src ao path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from database import DatabaseManager
from auth import AuthManager

def limpar_dados_teste():
    """Remove dados automáticos de teste do banco de dados"""
    print("🧹 LIMPEZA DE DADOS DE TESTE")
    print("=" * 50)
    
    try:
        # Inicializar banco de dados
        db_manager = DatabaseManager()
        db_manager.initialize_database()
        
        auth_manager = AuthManager(db_manager)
        
        # Fazer login
        user_data = auth_manager.authenticate_user('admin', 'admin123')
        if not user_data:
            print("❌ Falha no login!")
            return
        
        print(f"✅ Login realizado: {user_data['full_name']}")
        user_id = user_data['id']
        
        # Criar interface para confirmação
        root = tk.Tk()
        root.title("Limpeza de Dados de Teste")
        root.geometry("600x500")
        root.configure(bg='#f0f0f0')
        
        # Frame principal
        main_frame = tk.Frame(root, bg='#f0f0f0', padx=20, pady=20)
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # Título
        title_label = tk.Label(
            main_frame,
            text="🧹 Limpeza de Dados de Teste",
            font=("Arial", 16, "bold"),
            bg='#f0f0f0',
            fg='#2c3e50'
        )
        title_label.pack(pady=(0, 20))
        
        # Verificar dados de teste existentes
        print("\n🔍 VERIFICANDO DADOS DE TESTE...")
        
        # Contar transações de teste
        test_transactions = db_manager.execute_query("""
            SELECT COUNT(*) FROM transactions 
            WHERE user_id = ? AND (
                description LIKE '%teste%' OR 
                description LIKE '%Teste%' OR
                description LIKE '%TESTE%' OR
                description LIKE '%Compra à vista%' OR
                description LIKE '%Compra parcelada%' OR
                description LIKE '%Compra em 12x%' OR
                description LIKE '%Teste 15 parcelas%'
            )
        """, (user_id,))[0][0]
        
        # Contar categorias de teste
        test_categories = db_manager.execute_query("""
            SELECT COUNT(*) FROM categories 
            WHERE user_id = ? AND (
                name LIKE '%teste%' OR 
                name LIKE '%Teste%' OR
                name LIKE '%TESTE%' OR
                description LIKE '%teste%'
            )
        """, (user_id,))[0][0]
        
        # Contar carteiras de teste (se houver)
        test_wallets = db_manager.execute_query("""
            SELECT COUNT(*) FROM wallets 
            WHERE user_id = ? AND (
                name LIKE '%teste%' OR 
                name LIKE '%Teste%' OR
                name LIKE '%TESTE%'
            )
        """, (user_id,))[0][0]
        
        print(f"   📊 Transações de teste: {test_transactions}")
        print(f"   🏷️  Categorias de teste: {test_categories}")
        print(f"   💳 Carteiras de teste: {test_wallets}")
        
        # Mostrar informações na interface
        info_text = tk.Text(
            main_frame,
            height=15,
            width=70,
            font=("Consolas", 10),
            bg='white',
            relief=tk.SOLID,
            bd=1
        )
        info_text.pack(fill=tk.BOTH, expand=True, pady=(0, 20))
        
        # Adicionar informações detalhadas
        info_text.insert(tk.END, "🔍 DADOS DE TESTE ENCONTRADOS:\n\n")
        info_text.insert(tk.END, f"📊 Transações de teste: {test_transactions}\n")
        info_text.insert(tk.END, f"🏷️  Categorias de teste: {test_categories}\n")
        info_text.insert(tk.END, f"💳 Carteiras de teste: {test_wallets}\n\n")
        
        if test_transactions > 0:
            info_text.insert(tk.END, "📋 TRANSAÇÕES QUE SERÃO REMOVIDAS:\n")
            
            # Listar transações de teste
            test_trans_details = db_manager.execute_query("""
                SELECT description, amount, transaction_date, installments, installment_number
                FROM transactions 
                WHERE user_id = ? AND (
                    description LIKE '%teste%' OR 
                    description LIKE '%Teste%' OR
                    description LIKE '%TESTE%' OR
                    description LIKE '%Compra à vista%' OR
                    description LIKE '%Compra parcelada%' OR
                    description LIKE '%Compra em 12x%' OR
                    description LIKE '%Teste 15 parcelas%'
                )
                ORDER BY description, installment_number
                LIMIT 50
            """, (user_id,))
            
            for trans in test_trans_details:
                if trans['installments'] > 1:
                    info_text.insert(tk.END, 
                        f"   • {trans['description']} - R$ {trans['amount']:.2f} "
                        f"({trans['installment_number']}/{trans['installments']})\n")
                else:
                    info_text.insert(tk.END, 
                        f"   • {trans['description']} - R$ {trans['amount']:.2f}\n")
            
            if len(test_trans_details) == 50:
                info_text.insert(tk.END, "   ... (e mais transações)\n")
        
        if test_categories > 0:
            info_text.insert(tk.END, "\n🏷️  CATEGORIAS QUE SERÃO REMOVIDAS:\n")
            
            test_cat_details = db_manager.execute_query("""
                SELECT name, category_type FROM categories 
                WHERE user_id = ? AND (
                    name LIKE '%teste%' OR 
                    name LIKE '%Teste%' OR
                    name LIKE '%TESTE%' OR
                    description LIKE '%teste%'
                )
            """, (user_id,))
            
            for cat in test_cat_details:
                tipo = "Receita" if cat['category_type'] == 'income' else "Despesa"
                info_text.insert(tk.END, f"   • {cat['name']} ({tipo})\n")
        
        info_text.insert(tk.END, "\n⚠️  ATENÇÃO: Esta ação não pode ser desfeita!\n")
        info_text.insert(tk.END, "✅ Dados reais do usuário serão preservados.\n")
        
        # Desabilitar edição
        info_text.configure(state=tk.DISABLED)
        
        # Função para executar limpeza
        def executar_limpeza():
            try:
                if messagebox.askyesno(
                    "Confirmar Limpeza",
                    f"Tem certeza que deseja remover:\n\n"
                    f"• {test_transactions} transações de teste\n"
                    f"• {test_categories} categorias de teste\n"
                    f"• {test_wallets} carteiras de teste\n\n"
                    f"Esta ação não pode ser desfeita!"
                ):
                    print("\n🧹 EXECUTANDO LIMPEZA...")
                    
                    # Remover transações de teste
                    if test_transactions > 0:
                        db_manager.execute_query("""
                            DELETE FROM transactions 
                            WHERE user_id = ? AND (
                                description LIKE '%teste%' OR 
                                description LIKE '%Teste%' OR
                                description LIKE '%TESTE%' OR
                                description LIKE '%Compra à vista%' OR
                                description LIKE '%Compra parcelada%' OR
                                description LIKE '%Compra em 12x%' OR
                                description LIKE '%Teste 15 parcelas%'
                            )
                        """, (user_id,))
                        print(f"   ✅ {test_transactions} transações de teste removidas")
                    
                    # Remover categorias de teste (apenas se não estiverem em uso)
                    if test_categories > 0:
                        # Verificar quais categorias não estão em uso
                        unused_test_categories = db_manager.execute_query("""
                            SELECT c.id, c.name FROM categories c
                            WHERE c.user_id = ? AND (
                                c.name LIKE '%teste%' OR 
                                c.name LIKE '%Teste%' OR
                                c.name LIKE '%TESTE%' OR
                                c.description LIKE '%teste%'
                            ) AND c.id NOT IN (
                                SELECT DISTINCT category_id FROM transactions WHERE user_id = ?
                            )
                        """, (user_id, user_id))
                        
                        for cat in unused_test_categories:
                            db_manager.execute_query("DELETE FROM categories WHERE id = ?", (cat['id'],))
                        
                        print(f"   ✅ {len(unused_test_categories)} categorias de teste removidas")
                        
                        if len(unused_test_categories) < test_categories:
                            remaining = test_categories - len(unused_test_categories)
                            print(f"   ⚠️  {remaining} categorias de teste mantidas (em uso)")
                    
                    # Remover carteiras de teste (apenas se não estiverem em uso)
                    if test_wallets > 0:
                        unused_test_wallets = db_manager.execute_query("""
                            SELECT w.id, w.name FROM wallets w
                            WHERE w.user_id = ? AND (
                                w.name LIKE '%teste%' OR 
                                w.name LIKE '%Teste%' OR
                                w.name LIKE '%TESTE%'
                            ) AND w.id NOT IN (
                                SELECT DISTINCT wallet_id FROM transactions WHERE user_id = ?
                            )
                        """, (user_id, user_id))
                        
                        for wallet in unused_test_wallets:
                            db_manager.execute_query("DELETE FROM wallets WHERE id = ?", (wallet['id'],))
                        
                        print(f"   ✅ {len(unused_test_wallets)} carteiras de teste removidas")
                    
                    messagebox.showinfo(
                        "Limpeza Concluída",
                        f"Limpeza concluída com sucesso!\n\n"
                        f"• {test_transactions} transações removidas\n"
                        f"• Categorias e carteiras não utilizadas removidas\n\n"
                        f"Reinicie a aplicação para ver as mudanças."
                    )
                    
                    print("\n✅ LIMPEZA CONCLUÍDA COM SUCESSO!")
                    root.destroy()
                
            except Exception as e:
                messagebox.showerror("Erro", f"Erro durante a limpeza: {str(e)}")
                print(f"❌ Erro durante limpeza: {str(e)}")
        
        # Botões
        button_frame = tk.Frame(main_frame, bg='#f0f0f0')
        button_frame.pack(fill=tk.X)
        
        if test_transactions > 0 or test_categories > 0 or test_wallets > 0:
            clean_btn = tk.Button(
                button_frame,
                text="🧹 Executar Limpeza",
                command=executar_limpeza,
                font=("Arial", 12, "bold"),
                bg='#e74c3c',
                fg='white',
                padx=20,
                pady=10,
                cursor='hand2'
            )
            clean_btn.pack(side=tk.LEFT, padx=(0, 10))
        else:
            no_data_label = tk.Label(
                button_frame,
                text="✅ Nenhum dado de teste encontrado!",
                font=("Arial", 12, "bold"),
                bg='#f0f0f0',
                fg='#27ae60'
            )
            no_data_label.pack(side=tk.LEFT)
        
        cancel_btn = tk.Button(
            button_frame,
            text="❌ Cancelar",
            command=root.destroy,
            font=("Arial", 12, "bold"),
            bg='#95a5a6',
            fg='white',
            padx=20,
            pady=10,
            cursor='hand2'
        )
        cancel_btn.pack(side=tk.RIGHT)
        
        # Executar interface
        root.mainloop()
        
    except Exception as e:
        print(f"❌ ERRO GERAL: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    limpar_dados_teste()
