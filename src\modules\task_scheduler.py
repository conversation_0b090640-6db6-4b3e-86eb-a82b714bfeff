#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Agendador de Tarefas em Background
Gerencia execução de tarefas automáticas sem bloquear a interface
"""

import threading
import time
import queue
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Callable, Any, Optional
from dataclasses import dataclass
from enum import Enum

class TaskPriority(Enum):
    LOW = 1
    NORMAL = 2
    HIGH = 3
    CRITICAL = 4

class TaskStatus(Enum):
    PENDING = "pending"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"

@dataclass
class ScheduledTask:
    """Representa uma tarefa agendada"""
    id: str
    name: str
    function: Callable
    args: tuple = ()
    kwargs: dict = None
    priority: TaskPriority = TaskPriority.NORMAL
    scheduled_time: datetime = None
    interval_seconds: Optional[int] = None  # Para tarefas recorrentes
    max_retries: int = 3
    retry_count: int = 0
    status: TaskStatus = TaskStatus.PENDING
    created_at: datetime = None
    started_at: datetime = None
    completed_at: datetime = None
    error_message: str = None
    result: Any = None

    def __post_init__(self):
        if self.kwargs is None:
            self.kwargs = {}
        if self.created_at is None:
            self.created_at = datetime.now()
        if self.scheduled_time is None:
            self.scheduled_time = datetime.now()

class TaskScheduler:
    """Agendador de tarefas em background com suporte a prioridades e recorrência"""
    
    def __init__(self, max_workers: int = 3):
        self.max_workers = max_workers
        self.task_queue = queue.PriorityQueue()
        self.workers = []
        self.tasks = {}  # ID -> ScheduledTask
        self.running_tasks = {}  # ID -> ScheduledTask
        self.completed_tasks = {}  # ID -> ScheduledTask (últimas 100)
        
        self.is_running = False
        self.stop_event = threading.Event()
        
        # Logger
        self.logger = logging.getLogger("TaskScheduler")
        
        # Thread para verificar tarefas agendadas
        self.scheduler_thread = None
        
        # Callbacks
        self.task_callbacks = {
            'on_task_start': [],
            'on_task_complete': [],
            'on_task_error': []
        }
    
    def start(self):
        """Inicia o agendador de tarefas"""
        if self.is_running:
            self.logger.warning("Agendador já está rodando")
            return
        
        self.is_running = True
        self.stop_event.clear()
        
        # Iniciar workers
        for i in range(self.max_workers):
            worker = threading.Thread(target=self._worker_loop, args=(i,), daemon=True)
            worker.start()
            self.workers.append(worker)
        
        # Iniciar thread do agendador
        self.scheduler_thread = threading.Thread(target=self._scheduler_loop, daemon=True)
        self.scheduler_thread.start()
        
        self.logger.info(f"Agendador iniciado com {self.max_workers} workers")
    
    def stop(self):
        """Para o agendador de tarefas"""
        if not self.is_running:
            return
        
        self.is_running = False
        self.stop_event.set()
        
        # Cancelar tarefas pendentes
        while not self.task_queue.empty():
            try:
                _, task = self.task_queue.get_nowait()
                task.status = TaskStatus.CANCELLED
            except queue.Empty:
                break
        
        # Aguardar workers terminarem
        for worker in self.workers:
            if worker.is_alive():
                worker.join(timeout=5)
        
        # Aguardar scheduler terminar
        if self.scheduler_thread and self.scheduler_thread.is_alive():
            self.scheduler_thread.join(timeout=5)
        
        self.logger.info("Agendador parado")
    
    def schedule_task(self, task: ScheduledTask) -> str:
        """Agenda uma tarefa para execução"""
        self.tasks[task.id] = task
        
        # Se é para executar agora, adicionar à fila
        if task.scheduled_time <= datetime.now():
            priority_value = 10 - task.priority.value  # Inverter para queue.PriorityQueue
            self.task_queue.put((priority_value, task))
        
        self.logger.info(f"Tarefa agendada: {task.name} (ID: {task.id})")
        return task.id
    
    def schedule_function(self, name: str, function: Callable, 
                         scheduled_time: datetime = None,
                         interval_seconds: int = None,
                         priority: TaskPriority = TaskPriority.NORMAL,
                         args: tuple = (), kwargs: dict = None) -> str:
        """Agenda uma função para execução"""
        task_id = f"{name}_{datetime.now().strftime('%Y%m%d_%H%M%S_%f')}"
        
        task = ScheduledTask(
            id=task_id,
            name=name,
            function=function,
            args=args,
            kwargs=kwargs or {},
            priority=priority,
            scheduled_time=scheduled_time or datetime.now(),
            interval_seconds=interval_seconds
        )
        
        return self.schedule_task(task)
    
    def schedule_recurring_task(self, name: str, function: Callable,
                               interval_seconds: int,
                               priority: TaskPriority = TaskPriority.NORMAL,
                               args: tuple = (), kwargs: dict = None) -> str:
        """Agenda uma tarefa recorrente"""
        return self.schedule_function(
            name=name,
            function=function,
            scheduled_time=datetime.now(),
            interval_seconds=interval_seconds,
            priority=priority,
            args=args,
            kwargs=kwargs
        )
    
    def cancel_task(self, task_id: str) -> bool:
        """Cancela uma tarefa"""
        if task_id in self.tasks:
            task = self.tasks[task_id]
            if task.status == TaskStatus.PENDING:
                task.status = TaskStatus.CANCELLED
                self.logger.info(f"Tarefa cancelada: {task.name} (ID: {task_id})")
                return True
        return False
    
    def get_task_status(self, task_id: str) -> Optional[ScheduledTask]:
        """Retorna o status de uma tarefa"""
        return self.tasks.get(task_id) or self.completed_tasks.get(task_id)
    
    def get_all_tasks(self) -> Dict[str, ScheduledTask]:
        """Retorna todas as tarefas (ativas e completadas)"""
        all_tasks = {}
        all_tasks.update(self.tasks)
        all_tasks.update(self.completed_tasks)
        return all_tasks
    
    def get_running_tasks(self) -> Dict[str, ScheduledTask]:
        """Retorna tarefas em execução"""
        return self.running_tasks.copy()
    
    def add_callback(self, event: str, callback: Callable):
        """Adiciona callback para eventos de tarefas"""
        if event in self.task_callbacks:
            self.task_callbacks[event].append(callback)
    
    def _scheduler_loop(self):
        """Loop principal do agendador"""
        while not self.stop_event.is_set():
            try:
                current_time = datetime.now()
                
                # Verificar tarefas agendadas que estão prontas
                ready_tasks = []
                for task_id, task in list(self.tasks.items()):
                    if (task.status == TaskStatus.PENDING and 
                        task.scheduled_time <= current_time):
                        ready_tasks.append(task)
                
                # Adicionar tarefas prontas à fila
                for task in ready_tasks:
                    priority_value = 10 - task.priority.value
                    self.task_queue.put((priority_value, task))
                
                # Aguardar próxima verificação
                self.stop_event.wait(1)  # Verificar a cada segundo
                
            except Exception as e:
                self.logger.error(f"Erro no loop do agendador: {e}")
                time.sleep(5)
    
    def _worker_loop(self, worker_id: int):
        """Loop do worker para executar tarefas"""
        self.logger.info(f"Worker {worker_id} iniciado")
        
        while not self.stop_event.is_set():
            try:
                # Pegar próxima tarefa da fila (timeout de 1 segundo)
                try:
                    priority, task = self.task_queue.get(timeout=1)
                except queue.Empty:
                    continue
                
                # Executar tarefa
                self._execute_task(task, worker_id)
                
                # Marcar tarefa como concluída na fila
                self.task_queue.task_done()
                
            except Exception as e:
                self.logger.error(f"Erro no worker {worker_id}: {e}")
        
        self.logger.info(f"Worker {worker_id} parado")
    
    def _execute_task(self, task: ScheduledTask, worker_id: int):
        """Executa uma tarefa específica"""
        task.status = TaskStatus.RUNNING
        task.started_at = datetime.now()
        self.running_tasks[task.id] = task
        
        # Callback de início
        for callback in self.task_callbacks['on_task_start']:
            try:
                callback(task)
            except Exception as e:
                self.logger.error(f"Erro no callback de início: {e}")
        
        self.logger.info(f"Worker {worker_id} executando: {task.name}")
        
        try:
            # Executar função da tarefa
            result = task.function(*task.args, **task.kwargs)
            
            # Tarefa completada com sucesso
            task.status = TaskStatus.COMPLETED
            task.completed_at = datetime.now()
            task.result = result
            
            # Callback de sucesso
            for callback in self.task_callbacks['on_task_complete']:
                try:
                    callback(task)
                except Exception as e:
                    self.logger.error(f"Erro no callback de sucesso: {e}")
            
            self.logger.info(f"Tarefa completada: {task.name}")
            
            # Se é tarefa recorrente, reagendar
            if task.interval_seconds:
                self._reschedule_recurring_task(task)
            
        except Exception as e:
            # Tarefa falhou
            task.status = TaskStatus.FAILED
            task.completed_at = datetime.now()
            task.error_message = str(e)
            task.retry_count += 1
            
            self.logger.error(f"Erro na tarefa {task.name}: {e}")
            
            # Callback de erro
            for callback in self.task_callbacks['on_task_error']:
                try:
                    callback(task, e)
                except Exception as callback_error:
                    self.logger.error(f"Erro no callback de erro: {callback_error}")
            
            # Tentar novamente se ainda há tentativas
            if task.retry_count < task.max_retries:
                task.status = TaskStatus.PENDING
                task.scheduled_time = datetime.now() + timedelta(minutes=task.retry_count * 2)
                self.logger.info(f"Reagendando tarefa {task.name} (tentativa {task.retry_count + 1})")
        
        finally:
            # Remover da lista de tarefas em execução
            if task.id in self.running_tasks:
                del self.running_tasks[task.id]
            
            # Mover para tarefas completadas se não é recorrente
            if not task.interval_seconds or task.status == TaskStatus.FAILED:
                if task.id in self.tasks:
                    del self.tasks[task.id]
                
                # Manter apenas as últimas 100 tarefas completadas
                self.completed_tasks[task.id] = task
                if len(self.completed_tasks) > 100:
                    oldest_id = min(self.completed_tasks.keys(), 
                                  key=lambda x: self.completed_tasks[x].completed_at)
                    del self.completed_tasks[oldest_id]
    
    def _reschedule_recurring_task(self, task: ScheduledTask):
        """Reagenda uma tarefa recorrente"""
        # Criar nova instância da tarefa
        new_task_id = f"{task.name}_{datetime.now().strftime('%Y%m%d_%H%M%S_%f')}"
        new_task = ScheduledTask(
            id=new_task_id,
            name=task.name,
            function=task.function,
            args=task.args,
            kwargs=task.kwargs,
            priority=task.priority,
            scheduled_time=datetime.now() + timedelta(seconds=task.interval_seconds),
            interval_seconds=task.interval_seconds,
            max_retries=task.max_retries
        )
        
        self.schedule_task(new_task)
