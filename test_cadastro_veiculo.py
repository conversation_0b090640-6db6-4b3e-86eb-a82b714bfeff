#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Teste específico do cadastro de veículo
"""

import sys
import os
import tkinter as tk
from tkinter import messagebox

# Adicionar o diretório src ao path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from database import DatabaseManager
from auth import AuthManager
from gui.vehicle_form import VehicleForm

def test_cadastro_veiculo():
    """Testa especificamente o cadastro de veículo"""
    print("🚗 TESTE ESPECÍFICO DO CADASTRO DE VEÍCULO")
    print("=" * 60)
    
    try:
        # Inicializar banco de dados
        db_manager = DatabaseManager()
        db_manager.initialize_database()
        
        auth_manager = AuthManager(db_manager)
        
        # Fazer login
        user_data = auth_manager.authenticate_user('admin', 'admin123')
        if not user_data:
            print("❌ Falha no login!")
            return
        
        print(f"✅ Login realizado: {user_data['full_name']}")
        user_id = user_data['id']
        
        # Contar veículos antes
        vehicles_before = db_manager.execute_query("SELECT COUNT(*) FROM vehicles WHERE user_id = ?", (user_id,))[0][0]
        print(f"📊 Veículos antes: {vehicles_before}")
        
        # Criar janela principal
        root = tk.Tk()
        root.title("Teste Cadastro Veículo")
        root.geometry("800x600")
        root.withdraw()  # Ocultar janela principal
        
        print("✅ Janela principal criada")
        
        # Variável para controlar se o callback foi chamado
        callback_called = False
        
        def callback_test():
            nonlocal callback_called
            callback_called = True
            print("✅ Callback chamado - veículo salvo!")
        
        # Criar formulário de veículo
        print("\n🔧 Criando formulário de novo veículo...")
        
        try:
            vehicle_form = VehicleForm(
                parent=root,
                db_manager=db_manager,
                user_id=user_id,
                vehicle_data=None,  # Novo veículo
                callback=callback_test
            )
            
            print("✅ Formulário de veículo criado!")
            
            # Função para testar cadastro automaticamente
            def test_auto_cadastro():
                try:
                    print("\n🧪 INICIANDO TESTE AUTOMÁTICO DE CADASTRO...")
                    
                    # Preencher campos obrigatórios
                    vehicle_form.name_var.set("Carro Teste Automático")
                    vehicle_form.brand_var.set("Honda")
                    vehicle_form.model_var.set("Civic")
                    vehicle_form.year_var.set("2021")
                    
                    # Campos opcionais
                    vehicle_form.license_plate_var.set("ABC-1234")
                    vehicle_form.color_var.set("Prata")
                    vehicle_form.fuel_type_var.set("flex")
                    vehicle_form.mileage_var.set("50000")
                    
                    print("   ✅ Campos preenchidos:")
                    print(f"      Nome: {vehicle_form.name_var.get()}")
                    print(f"      Marca: {vehicle_form.brand_var.get()}")
                    print(f"      Modelo: {vehicle_form.model_var.get()}")
                    print(f"      Ano: {vehicle_form.year_var.get()}")
                    print(f"      Placa: {vehicle_form.license_plate_var.get()}")
                    
                    # Mock do messagebox para evitar diálogos
                    original_showinfo = messagebox.showinfo
                    original_showerror = messagebox.showerror
                    
                    def mock_showinfo(title, message):
                        print(f"   📢 INFO: {title} - {message}")
                        return True
                    
                    def mock_showerror(title, message):
                        print(f"   ❌ ERRO: {title} - {message}")
                        return True
                    
                    messagebox.showinfo = mock_showinfo
                    messagebox.showerror = mock_showerror
                    
                    # Tentar salvar
                    print("\n   🔄 Executando método save()...")
                    vehicle_form.save()
                    
                    # Restaurar messagebox
                    messagebox.showinfo = original_showinfo
                    messagebox.showerror = original_showerror
                    
                    # Verificar se foi salvo
                    vehicles_after = db_manager.execute_query("SELECT COUNT(*) FROM vehicles WHERE user_id = ?", (user_id,))[0][0]
                    print(f"\n📊 Veículos depois: {vehicles_after}")
                    print(f"📈 Veículos criados: {vehicles_after - vehicles_before}")
                    
                    if vehicles_after > vehicles_before:
                        print("🎉 CADASTRO REALIZADO COM SUCESSO!")
                        
                        # Buscar o veículo criado
                        new_vehicle = db_manager.execute_query("""
                            SELECT * FROM vehicles 
                            WHERE user_id = ? AND name = 'Carro Teste Automático'
                            ORDER BY created_at DESC LIMIT 1
                        """, (user_id,))
                        
                        if new_vehicle:
                            vehicle = new_vehicle[0]
                            print(f"\n📋 DADOS DO VEÍCULO CRIADO:")
                            print(f"   ID: {vehicle['id']}")
                            print(f"   Nome: {vehicle['name']}")
                            print(f"   Marca: {vehicle['brand']}")
                            print(f"   Modelo: {vehicle['model']}")
                            print(f"   Ano: {vehicle['year']}")
                            print(f"   Placa: {vehicle['license_plate']}")
                            print(f"   Cor: {vehicle['color']}")
                            print(f"   Combustível: {vehicle['fuel_type']}")
                            print(f"   Quilometragem: {vehicle['mileage']}")
                    else:
                        print("❌ CADASTRO FALHOU - Nenhum veículo foi criado!")
                    
                    if callback_called:
                        print("✅ Callback foi chamado corretamente")
                    else:
                        print("⚠️  Callback não foi chamado")
                    
                except Exception as e:
                    print(f"❌ Erro durante teste automático: {str(e)}")
                    import traceback
                    traceback.print_exc()
                
                # Fechar formulário
                try:
                    vehicle_form.window.destroy()
                except:
                    pass
            
            # Aguardar renderização e testar
            root.after(1000, test_auto_cadastro)
            
            # Fechar após teste
            root.after(5000, root.quit)
            
        except Exception as e:
            print(f"❌ Erro ao criar formulário: {str(e)}")
            import traceback
            traceback.print_exc()
        
        print("\n✅ TESTE AUTOMÁTICO DE CADASTRO INICIADO!")
        print("🔍 Executando cadastro em 1 segundo...")
        
        # Executar por tempo limitado
        root.mainloop()
        root.destroy()
        
        print(f"\n📊 RESULTADO FINAL:")
        vehicles_final = db_manager.execute_query("SELECT COUNT(*) FROM vehicles WHERE user_id = ?", (user_id,))[0][0]
        print(f"   Veículos no banco: {vehicles_final}")
        print(f"   Callback chamado: {'Sim' if callback_called else 'Não'}")
        
        if vehicles_final > vehicles_before:
            print(f"🎉 TESTE CONCLUÍDO COM SUCESSO!")
            print(f"   ✅ Formulário funcionando")
            print(f"   ✅ Cadastro funcionando")
            print(f"   ✅ Banco de dados funcionando")
        else:
            print(f"❌ TESTE FALHOU!")
            print(f"   ⚠️  Nenhum veículo foi cadastrado")
        
    except Exception as e:
        print(f"❌ ERRO GERAL: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_cadastro_veiculo()
