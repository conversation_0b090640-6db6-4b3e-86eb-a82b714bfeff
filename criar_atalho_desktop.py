#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Script para criar atalho na área de trabalho
"""

import os
import sys
from pathlib import Path

def get_desktop_path():
    """Obtém o caminho da área de trabalho"""
    try:
        # Método 1: Variável de ambiente USERPROFILE
        userprofile = os.environ.get('USERPROFILE', '')
        if userprofile:
            desktop_paths = [
                Path(userprofile) / "Desktop",
                Path(userprofile) / "Área de Trabalho",
                Path(userprofile) / "OneDrive" / "Desktop",
                Path(userprofile) / "OneDrive" / "Área de Trabalho"
            ]

            for path in desktop_paths:
                if path.exists():
                    return path

        # Método 2: Path.home()
        home_desktop_paths = [
            Path.home() / "Desktop",
            Path.home() / "Área de Trabalho"
        ]

        for path in home_desktop_paths:
            if path.exists():
                return path

        return None
    except:
        return None

def find_executable():
    """Procura pelo executável do sistema"""
    possible_names = [
        "GestaoContasFormulariosCompactos.exe",
        "GestaoContasCompletoFinal.exe",
        "GestaoContasAutoRefresh.exe",
        "GestaoContasFormulariosMelhorados.exe",
        "GestaoContasFinalMelhorado.exe"
    ]

    for name in possible_names:
        exe_path = Path.cwd() / name
        if exe_path.exists():
            return exe_path

    return None

def create_desktop_shortcut():
    """Cria atalho na área de trabalho"""
    try:
        # Tentar método avançado primeiro
        return create_lnk_shortcut()
    except:
        # Fallback para método simples
        return create_bat_shortcut()

def create_lnk_shortcut():
    """Cria atalho .lnk usando COM"""
    try:
        import win32com.client

        desktop_path = get_desktop_path()
        if not desktop_path:
            raise Exception("Área de trabalho não encontrada")

        exe_path = find_executable()
        if not exe_path:
            raise Exception("Executável não encontrado")

        # Criar atalho .lnk
        shell = win32com.client.Dispatch('WScript.Shell')
        shortcut_path = desktop_path / "Sistema de Gestão Financeira.lnk"
        shortcut = shell.CreateShortCut(str(shortcut_path))
        shortcut.Targetpath = str(exe_path)
        shortcut.WorkingDirectory = str(exe_path.parent)
        shortcut.IconLocation = str(exe_path)
        shortcut.Description = "Sistema de Gestão Financeira - Controle suas finanças"
        shortcut.save()

        print(f"✅ Atalho .lnk criado: {shortcut_path}")
        return True

    except ImportError:
        raise Exception("win32com não disponível")
    except Exception as e:
        raise Exception(f"Erro ao criar .lnk: {str(e)}")

def create_bat_shortcut():
    """Cria atalho .bat simples"""
    try:
        desktop_path = get_desktop_path()
        if not desktop_path:
            print("❌ Não foi possível encontrar a área de trabalho")
            return False

        exe_path = find_executable()
        if not exe_path:
            print("❌ Executável não encontrado")
            print("Procure por um dos seguintes arquivos:")
            print("  • GestaoContasFormulariosCompactos.exe")
            print("  • GestaoContasCompletoFinal.exe")
            print("  • GestaoContasAutoRefresh.exe")
            return False

        # Criar arquivo .bat
        bat_content = f'''@echo off
title Sistema de Gestao Financeira
cd /d "{exe_path.parent}"
start "" "{exe_path.name}"
'''

        bat_path = desktop_path / "Sistema de Gestão Financeira.bat"

        with open(bat_path, 'w', encoding='utf-8') as f:
            f.write(bat_content)

        print(f"✅ Atalho .bat criado: {bat_path}")
        print(f"📁 Executável encontrado: {exe_path.name}")
        return True

    except Exception as e:
        print(f"❌ Erro ao criar atalho: {str(e)}")
        return False



def main():
    """Função principal"""
    print("CRIADOR DE ATALHO NA ÁREA DE TRABALHO")
    print("Sistema de Gestão Financeira")
    print("=" * 50)
    
    if create_desktop_shortcut():
        print("\n🎉 Atalho criado com sucesso!")
        print("📍 Procure por 'Sistema de Gestão Financeira' na área de trabalho")
    else:
        print("\n❌ Falha ao criar atalho")
    
    input("\nPressione Enter para sair...")

if __name__ == "__main__":
    main()
