#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Teste específico dos botões do formulário de nova carteira
"""

import sys
import os
import tkinter as tk

# Adicionar o diretório src ao path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from database import DatabaseManager
from auth import AuthManager
from gui.wallet_form_new import WalletFormNew

def test_wallet_form_buttons():
    """Testa especificamente os botões do formulário de carteira"""
    print("🔘 TESTE DOS BOTÕES DO FORMULÁRIO DE CARTEIRA")
    print("=" * 60)
    
    try:
        # Inicializar banco de dados
        db_manager = DatabaseManager()
        db_manager.initialize_database()
        
        auth_manager = AuthManager(db_manager)
        
        # Fazer login
        user_data = auth_manager.authenticate_user('admin', 'admin123')
        if not user_data:
            print("❌ Falha no login!")
            return
        
        print(f"✅ Login realizado: {user_data['full_name']}")
        
        # Criar janela principal
        root = tk.Tk()
        root.title("Teste Botões Formulário Carteira")
        root.geometry("1000x700")
        
        print("✅ Janela principal criada")
        
        # Testar formulário de nova carteira
        print("\n🔧 Criando formulário de nova carteira...")
        try:
            form = WalletFormNew(
                root, 
                db_manager, 
                user_data['id']
            )
            
            print("✅ Formulário de carteira criado!")
            print(f"📐 Geometria da janela: {form.window.geometry()}")
            
            # Verificar botões após renderização
            def check_form_buttons():
                try:
                    form.window.update_idletasks()
                    
                    # Obter dimensões da janela do formulário
                    form_width = form.window.winfo_width()
                    form_height = form.window.winfo_height()
                    
                    print(f"📏 Dimensões da janela do formulário: {form_width}x{form_height}")
                    
                    # Procurar botões especificamente no formulário
                    form_buttons = []
                    def find_form_buttons(widget, level=0):
                        indent = "  " * level
                        if isinstance(widget, tk.Button):
                            try:
                                button_info = {
                                    'text': widget['text'],
                                    'width': widget.winfo_width(),
                                    'height': widget.winfo_height(),
                                    'x': widget.winfo_x(),
                                    'y': widget.winfo_y(),
                                    'font': widget['font'],
                                    'padx': widget['padx'],
                                    'pady': widget['pady']
                                }
                                form_buttons.append(button_info)
                                print(f"{indent}🔍 Botão tk.Button: '{button_info['text']}'")
                            except Exception as e:
                                print(f"{indent}❌ Erro ao obter info do botão: {e}")
                        
                        try:
                            for child in widget.winfo_children():
                                find_form_buttons(child, level + 1)
                        except:
                            pass
                    
                    find_form_buttons(form.window)
                    
                    print(f"\n📊 Botões do formulário encontrados: {len(form_buttons)}")
                    for i, btn in enumerate(form_buttons):
                        print(f"\n   {i+1}. Botão: '{btn['text']}'")
                        print(f"      Dimensões: {btn['width']}x{btn['height']}")
                        print(f"      Posição: ({btn['x']}, {btn['y']})")
                        print(f"      Font: {btn['font']}")
                        print(f"      Padding: X={btn['padx']}, Y={btn['pady']}")
                        
                        # Análise do texto
                        text_length = len(btn['text'])
                        text_without_emoji = btn['text'].replace('❌', '').replace('💳', '').strip()
                        text_chars = len(text_without_emoji)
                        
                        print(f"      Texto: {text_length} chars total, {text_chars} chars sem emoji")
                        
                        # Verificar se pode estar cortado
                        min_width_needed = text_chars * 10 + 60  # 10px por char + padding
                        
                        if btn['width'] >= min_width_needed:
                            print(f"      ✅ Largura adequada ({btn['width']}px >= {min_width_needed}px)")
                        else:
                            print(f"      ⚠️  Pode estar cortado ({btn['width']}px < {min_width_needed}px)")
                        
                        # Verificar se está dentro da janela
                        if btn['x'] + btn['width'] > form_width:
                            print(f"      ⚠️  Botão pode estar fora da área visível!")
                        else:
                            print(f"      ✅ Botão está dentro da área visível")
                    
                    # Verificar textos esperados
                    expected_texts = ['❌ Cancelar', '💳 Criar Carteira']
                    found_texts = [btn['text'] for btn in form_buttons]
                    
                    print(f"\n🎯 VERIFICAÇÃO DE TEXTOS:")
                    for expected in expected_texts:
                        if expected in found_texts:
                            print(f"   ✅ '{expected}' - Encontrado")
                        else:
                            print(f"   ❌ '{expected}' - NÃO encontrado")
                    
                except Exception as e:
                    print(f"❌ Erro ao verificar botões do formulário: {str(e)}")
                    import traceback
                    traceback.print_exc()
            
            # Aguardar renderização e verificar
            root.after(1500, check_form_buttons)
            
            # Fechar após teste
            root.after(5000, form.window.destroy)
            
        except Exception as e:
            print(f"❌ Erro ao criar formulário: {str(e)}")
            import traceback
            traceback.print_exc()
        
        print("\n✅ TESTE DOS BOTÕES DO FORMULÁRIO INICIADO!")
        print("🔍 Verificando botões em 1.5 segundos...")
        
        # Executar por tempo limitado
        root.after(6000, root.quit)
        root.mainloop()
        root.destroy()
        
    except Exception as e:
        print(f"❌ ERRO GERAL: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_wallet_form_buttons()
