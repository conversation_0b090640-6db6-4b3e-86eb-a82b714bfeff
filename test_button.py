#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Teste específico para verificar se o botão aparece
"""

import sys
import tkinter as tk
sys.path.append('src')

from database import DatabaseManager
from auth import AuthManager
from modules.auto_update_manager import AutoUpdateManager
from gui.main_window import MainWindow

def test_button():
    print("Testando criação do botão...")
    
    # Criar componentes
    db_manager = DatabaseManager("data/test_button.db")
    db_manager.initialize_database()
    
    auth_manager = AuthManager(db_manager)
    
    # Criar usuário admin se não existir
    if not auth_manager.user_exists('admin'):
        auth_manager.create_user(
            username='admin',
            password='admin123',
            email='<EMAIL>',
            is_admin=True,
            full_name='Administrador do Sistema'
        )
    
    # Criar auto_update_manager
    auto_update_manager = AutoUpdateManager(db_manager)
    print(f"auto_update_manager criado: {auto_update_manager is not None}")
    
    # Criar janela principal
    root = tk.Tk()
    root.withdraw()  # Esconder inicialmente
    
    # Dados do usuário
    user_data = {
        'id': 1,
        'username': 'admin',
        'full_name': 'Administrador do Sistema',
        'is_admin': True
    }
    
    def on_logout():
        root.quit()
    
    # Criar MainWindow com auto_update_manager
    print("Criando MainWindow...")
    main_window = MainWindow(
        root, 
        db_manager, 
        auth_manager, 
        user_data, 
        on_logout,
        auto_update_manager  # Passando o auto_update_manager
    )
    
    print("MainWindow criado!")
    print(f"main_window.auto_update_manager é None? {main_window.auto_update_manager is None}")
    
    # Mostrar janela
    root.deiconify()
    root.mainloop()

if __name__ == "__main__":
    test_button()
