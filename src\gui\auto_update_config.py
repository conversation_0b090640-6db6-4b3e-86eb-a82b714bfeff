#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Interface de Configuração de Atualizações Automáticas
"""

import tkinter as tk
from tkinter import ttk, messagebox
import json
from pathlib import Path

class AutoUpdateConfigWindow:
    """Janela de configuração das atualizações automáticas"""
    
    def __init__(self, parent, auto_update_manager, callback=None):
        self.parent = parent
        self.auto_update_manager = auto_update_manager
        self.callback = callback
        
        # Criar janela
        self.window = tk.Toplevel(parent)
        self.window.title("Configurações de Atualizações Automáticas")
        self.window.geometry("800x700")
        self.window.resizable(True, True)
        
        # Centralizar janela
        self.center_window()
        
        # Configurar janela
        self.window.transient(parent)
        self.window.grab_set()
        
        # Variáveis
        self.setup_variables()
        
        # Criar interface
        self.create_widgets()
        
        # Carregar configurações atuais
        self.load_current_config()
    
    def center_window(self):
        """Centraliza a janela na tela"""
        self.window.update_idletasks()
        width = self.window.winfo_width()
        height = self.window.winfo_height()
        x = (self.window.winfo_screenwidth() // 2) - (width // 2)
        y = (self.window.winfo_screenheight() // 2) - (height // 2)
        self.window.geometry(f"{width}x{height}+{x}+{y}")
    
    def setup_variables(self):
        """Configura variáveis da interface"""
        self.system_enabled_var = tk.BooleanVar()
        self.update_interval_var = tk.IntVar()
        
        # Variáveis para cada tarefa
        self.task_vars = {}
        self.interval_vars = {}
        
        task_names = [
            'fuel_efficiency_update',
            'maintenance_reminders', 
            'insurance_expiry_check',
            'financial_calculations',
            'data_cleanup'
        ]
        
        for task_name in task_names:
            self.task_vars[task_name] = tk.BooleanVar()
            self.interval_vars[task_name] = tk.IntVar()
    
    def create_widgets(self):
        """Cria os widgets da interface"""
        # Frame principal
        main_frame = ttk.Frame(self.window, padding="10")
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # Título
        title_label = ttk.Label(main_frame, text="Configurações de Atualizações Automáticas", 
                               font=('Arial', 14, 'bold'))
        title_label.pack(pady=(0, 20))
        
        # Notebook para abas
        notebook = ttk.Notebook(main_frame)
        notebook.pack(fill=tk.BOTH, expand=True, pady=(0, 20))
        
        # Aba Geral
        self.create_general_tab(notebook)
        
        # Aba Tarefas
        self.create_tasks_tab(notebook)
        
        # Aba Status
        self.create_status_tab(notebook)
        
        # Botões
        self.create_buttons(main_frame)
    
    def create_general_tab(self, notebook):
        """Cria aba de configurações gerais"""
        general_frame = ttk.Frame(notebook, padding="10")
        notebook.add(general_frame, text="Geral")
        
        # Sistema habilitado
        system_frame = ttk.LabelFrame(general_frame, text="Sistema", padding="10")
        system_frame.pack(fill=tk.X, pady=(0, 10))
        
        ttk.Checkbutton(system_frame, text="Habilitar atualizações automáticas",
                       variable=self.system_enabled_var).pack(anchor=tk.W)
        
        # Intervalo de verificação
        interval_frame = ttk.LabelFrame(general_frame, text="Intervalo de Verificação", padding="10")
        interval_frame.pack(fill=tk.X, pady=(0, 10))
        
        ttk.Label(interval_frame, text="Verificar atualizações a cada:").pack(anchor=tk.W)
        
        interval_inner_frame = ttk.Frame(interval_frame)
        interval_inner_frame.pack(fill=tk.X, pady=(5, 0))
        
        ttk.Spinbox(interval_inner_frame, from_=60, to=3600, width=10,
                   textvariable=self.update_interval_var).pack(side=tk.LEFT)
        ttk.Label(interval_inner_frame, text="segundos").pack(side=tk.LEFT, padx=(5, 0))
        
        # Informações
        info_frame = ttk.LabelFrame(general_frame, text="Informações", padding="10")
        info_frame.pack(fill=tk.BOTH, expand=True)
        
        info_text = """
Configurações das Atualizações Automáticas:

• Sistema Habilitado: Liga/desliga todo o sistema de atualizações
• Intervalo de Verificação: Frequência com que o sistema verifica se há tarefas para executar
• Tarefas Individuais: Cada tarefa pode ser habilitada/desabilitada individualmente
• Intervalos por Tarefa: Cada tarefa tem seu próprio intervalo de execução

Recomendações:
- Mantenha o intervalo de verificação entre 300-600 segundos (5-10 minutos)
- Tarefas críticas devem ter intervalos menores
- Limpeza de dados pode ter intervalos maiores (semanal)
        """
        
        info_label = ttk.Label(info_frame, text=info_text, justify=tk.LEFT)
        info_label.pack(anchor=tk.W)
    
    def create_tasks_tab(self, notebook):
        """Cria aba de configuração de tarefas"""
        tasks_frame = ttk.Frame(notebook, padding="10")
        notebook.add(tasks_frame, text="Tarefas")
        
        # Scroll para a lista de tarefas
        canvas = tk.Canvas(tasks_frame)
        scrollbar = ttk.Scrollbar(tasks_frame, orient="vertical", command=canvas.yview)
        scrollable_frame = ttk.Frame(canvas)
        
        scrollable_frame.bind(
            "<Configure>",
            lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
        )
        
        canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar.set)
        
        # Definir tarefas
        tasks_config = {
            'fuel_efficiency_update': {
                'name': 'Atualização de Eficiência de Combustível',
                'description': 'Recalcula automaticamente a eficiência de combustível dos veículos',
                'default_interval': 60
            },
            'maintenance_reminders': {
                'name': 'Lembretes de Manutenção',
                'description': 'Verifica e cria lembretes de manutenção baseados em data e quilometragem',
                'default_interval': 1440
            },
            'insurance_expiry_check': {
                'name': 'Verificação de Vencimento de Seguros',
                'description': 'Monitora vencimento de seguros e documentos de veículos',
                'default_interval': 1440
            },
            'financial_calculations': {
                'name': 'Cálculos Financeiros',
                'description': 'Atualiza saldos de carteiras e estatísticas financeiras',
                'default_interval': 30
            },
            'data_cleanup': {
                'name': 'Limpeza de Dados',
                'description': 'Remove dados antigos e otimiza o banco de dados',
                'default_interval': 10080
            }
        }
        
        # Criar configuração para cada tarefa
        for task_id, task_info in tasks_config.items():
            self.create_task_config(scrollable_frame, task_id, task_info)
        
        # Pack canvas e scrollbar
        canvas.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")
    
    def create_task_config(self, parent, task_id, task_info):
        """Cria configuração para uma tarefa específica"""
        task_frame = ttk.LabelFrame(parent, text=task_info['name'], padding="10")
        task_frame.pack(fill=tk.X, pady=(0, 10))
        
        # Checkbox para habilitar/desabilitar
        ttk.Checkbutton(task_frame, text="Habilitada",
                       variable=self.task_vars[task_id]).pack(anchor=tk.W)
        
        # Descrição
        desc_label = ttk.Label(task_frame, text=task_info['description'], 
                              font=('Arial', 9), foreground='gray')
        desc_label.pack(anchor=tk.W, pady=(5, 10))
        
        # Intervalo
        interval_frame = ttk.Frame(task_frame)
        interval_frame.pack(fill=tk.X)
        
        ttk.Label(interval_frame, text="Executar a cada:").pack(side=tk.LEFT)
        
        ttk.Spinbox(interval_frame, from_=1, to=43200, width=10,
                   textvariable=self.interval_vars[task_id]).pack(side=tk.LEFT, padx=(5, 5))
        
        ttk.Label(interval_frame, text="minutos").pack(side=tk.LEFT)
    
    def create_status_tab(self, notebook):
        """Cria aba de status das tarefas"""
        status_frame = ttk.Frame(notebook, padding="10")
        notebook.add(status_frame, text="Status")
        
        # Botão para atualizar status
        refresh_btn = ttk.Button(status_frame, text="Atualizar Status", 
                                command=self.refresh_status)
        refresh_btn.pack(pady=(0, 10))
        
        # Treeview para mostrar status
        columns = ('Tarefa', 'Status', 'Última Execução', 'Próxima Execução')
        self.status_tree = ttk.Treeview(status_frame, columns=columns, show='headings', height=10)
        
        # Configurar colunas
        for col in columns:
            self.status_tree.heading(col, text=col)
            self.status_tree.column(col, width=150)
        
        # Scrollbar para treeview
        status_scroll = ttk.Scrollbar(status_frame, orient=tk.VERTICAL, command=self.status_tree.yview)
        self.status_tree.configure(yscrollcommand=status_scroll.set)
        
        # Pack treeview e scrollbar
        self.status_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        status_scroll.pack(side=tk.RIGHT, fill=tk.Y)
        
        # Botões de ação
        action_frame = ttk.Frame(status_frame)
        action_frame.pack(fill=tk.X, pady=(10, 0))
        
        ttk.Button(action_frame, text="Executar Tarefa Selecionada",
                  command=self.execute_selected_task).pack(side=tk.LEFT, padx=(0, 10))
        
        ttk.Button(action_frame, text="Parar Sistema",
                  command=self.stop_system).pack(side=tk.LEFT, padx=(0, 10))
        
        ttk.Button(action_frame, text="Iniciar Sistema",
                  command=self.start_system).pack(side=tk.LEFT)
    
    def create_buttons(self, parent):
        """Cria botões da janela"""
        button_frame = ttk.Frame(parent)
        button_frame.pack(fill=tk.X)
        
        ttk.Button(button_frame, text="Cancelar", command=self.cancel).pack(side=tk.RIGHT, padx=(10, 0))
        ttk.Button(button_frame, text="Aplicar", command=self.apply_config).pack(side=tk.RIGHT, padx=(10, 0))
        ttk.Button(button_frame, text="Salvar", command=self.save_config).pack(side=tk.RIGHT)
    
    def load_current_config(self):
        """Carrega configurações atuais"""
        config = self.auto_update_manager.config
        
        # Configurações gerais
        self.system_enabled_var.set(config.get('enabled', True))
        self.update_interval_var.set(config.get('update_interval_seconds', 300))
        
        # Configurações de tarefas
        tasks_config = config.get('tasks', {})
        for task_id in self.task_vars.keys():
            task_config = tasks_config.get(task_id, {})
            self.task_vars[task_id].set(task_config.get('enabled', True))
            self.interval_vars[task_id].set(task_config.get('interval_minutes', 60))
    
    def apply_config(self):
        """Aplica configurações sem salvar"""
        try:
            # Atualizar configurações do manager
            self.auto_update_manager.config['enabled'] = self.system_enabled_var.get()
            self.auto_update_manager.config['update_interval_seconds'] = self.update_interval_var.get()
            
            # Atualizar configurações de tarefas
            for task_id in self.task_vars.keys():
                if task_id not in self.auto_update_manager.config['tasks']:
                    self.auto_update_manager.config['tasks'][task_id] = {}
                
                self.auto_update_manager.config['tasks'][task_id]['enabled'] = self.task_vars[task_id].get()
                self.auto_update_manager.config['tasks'][task_id]['interval_minutes'] = self.interval_vars[task_id].get()
                
                # Atualizar tarefa no manager se existir
                if task_id in self.auto_update_manager.tasks:
                    self.auto_update_manager.tasks[task_id]['enabled'] = self.task_vars[task_id].get()
                    self.auto_update_manager.tasks[task_id]['interval_minutes'] = self.interval_vars[task_id].get()
            
            messagebox.showinfo("Sucesso", "Configurações aplicadas com sucesso!")
            
        except Exception as e:
            messagebox.showerror("Erro", f"Erro ao aplicar configurações: {str(e)}")
    
    def save_config(self):
        """Salva configurações"""
        try:
            self.apply_config()
            self.auto_update_manager.save_config()
            
            if self.callback:
                self.callback()
            
            messagebox.showinfo("Sucesso", "Configurações salvas com sucesso!")
            self.window.destroy()
            
        except Exception as e:
            messagebox.showerror("Erro", f"Erro ao salvar configurações: {str(e)}")
    
    def refresh_status(self):
        """Atualiza status das tarefas"""
        # Limpar árvore
        for item in self.status_tree.get_children():
            self.status_tree.delete(item)
        
        # Obter status
        status = self.auto_update_manager.get_task_status()
        
        # Adicionar informações à árvore
        for task_name, task_info in status['tasks'].items():
            status_text = "Habilitada" if task_info['enabled'] else "Desabilitada"
            last_run = task_info['last_run'] or "Nunca"
            if last_run != "Nunca":
                last_run = last_run.split('T')[0] + " " + last_run.split('T')[1][:8]
            
            next_run = "N/A"
            if task_info['enabled'] and task_info['last_run']:
                # Calcular próxima execução (aproximada)
                next_run = "Em breve"
            
            self.status_tree.insert('', 'end', values=(
                task_name,
                status_text,
                last_run,
                next_run
            ))
    
    def execute_selected_task(self):
        """Executa tarefa selecionada"""
        selection = self.status_tree.selection()
        if not selection:
            messagebox.showwarning("Aviso", "Selecione uma tarefa para executar")
            return
        
        item = self.status_tree.item(selection[0])
        task_name = item['values'][0]
        
        try:
            success = self.auto_update_manager.force_run_task(task_name)
            if success:
                messagebox.showinfo("Sucesso", f"Tarefa '{task_name}' executada com sucesso!")
                self.refresh_status()
            else:
                messagebox.showerror("Erro", f"Falha ao executar tarefa '{task_name}'")
        except Exception as e:
            messagebox.showerror("Erro", f"Erro ao executar tarefa: {str(e)}")
    
    def stop_system(self):
        """Para o sistema de atualizações"""
        try:
            self.auto_update_manager.stop()
            messagebox.showinfo("Sucesso", "Sistema de atualizações parado")
            self.refresh_status()
        except Exception as e:
            messagebox.showerror("Erro", f"Erro ao parar sistema: {str(e)}")
    
    def start_system(self):
        """Inicia o sistema de atualizações"""
        try:
            self.auto_update_manager.start()
            messagebox.showinfo("Sucesso", "Sistema de atualizações iniciado")
            self.refresh_status()
        except Exception as e:
            messagebox.showerror("Erro", f"Erro ao iniciar sistema: {str(e)}")
    
    def cancel(self):
        """Cancela e fecha a janela"""
        self.window.destroy()
