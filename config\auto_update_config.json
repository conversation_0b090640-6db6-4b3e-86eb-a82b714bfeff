{"enabled": true, "update_interval_seconds": 300, "version": "1.0.0", "last_updated": "2024-01-16", "settings": {"max_retries": 3, "retry_delay_minutes": 5, "log_level": "INFO", "enable_notifications": true}, "tasks": {"fuel_efficiency_update": {"enabled": true, "interval_minutes": 60, "description": "Atualiza cálculos de eficiência de combustível", "priority": "normal", "max_execution_time_minutes": 10, "dependencies": []}, "maintenance_reminders": {"enabled": true, "interval_minutes": 1440, "description": "Verifica lembretes de manutenção baseados em data e quilometragem", "priority": "high", "max_execution_time_minutes": 15, "dependencies": [], "settings": {"days_ahead_warning": 30, "mileage_threshold_km": 1000}}, "insurance_expiry_check": {"enabled": true, "interval_minutes": 1440, "description": "Verifica vencimento de seguros e documentos de veículos", "priority": "high", "max_execution_time_minutes": 5, "dependencies": [], "settings": {"days_ahead_warning": 30, "critical_days": 7}}, "financial_calculations": {"enabled": true, "interval_minutes": 5, "description": "Atualiza cálculos financeiros, saldos e transações recorrentes", "priority": "normal", "max_execution_time_minutes": 20, "dependencies": [], "settings": {"process_recurring": true, "update_balances": true, "check_overdue": true, "budget_alert_threshold": 1.2}}, "data_cleanup": {"enabled": true, "interval_minutes": 10080, "description": "Limpeza de dados antigos e otimização do banco de dados", "priority": "low", "max_execution_time_minutes": 30, "dependencies": [], "settings": {"log_retention_days": 90, "old_transaction_days": 365, "vacuum_database": true, "remove_duplicates": true}}}, "notifications": {"maintenance_alerts": {"enabled": true, "channels": ["ui", "log"]}, "insurance_expiry": {"enabled": true, "channels": ["ui", "log"]}, "financial_alerts": {"enabled": true, "channels": ["log"]}, "system_errors": {"enabled": true, "channels": ["ui", "log"]}}}