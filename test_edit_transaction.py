#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Teste específico para edição de transações
"""

import sys
import os
import tkinter as tk

# Adicionar o diretório src ao path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from database import DatabaseManager
from auth import AuthManager
from gui.main_window import MainWindow

def test_edit_transaction():
    """Testa especificamente a edição de transações"""
    print("🔧 TESTE ESPECÍFICO - EDIÇÃO DE TRANSAÇÕES")
    print("=" * 60)
    
    try:
        # Inicializar banco de dados
        db_manager = DatabaseManager()
        db_manager.initialize_database()
        
        auth_manager = AuthManager(db_manager)
        
        # Fazer login
        user_data = auth_manager.authenticate_user('admin', 'admin123')
        if not user_data:
            print("❌ Falha no login!")
            return
        
        print(f"✅ Login realizado: {user_data['full_name']}")
        
        # Criar janela principal
        root = tk.Tk()
        root.title("Teste Edição Transação")
        root.geometry("800x600")
        
        # Função de logout mock
        def mock_logout():
            print("Logout chamado")
        
        # Criar MainWindow
        main_window = MainWindow(root, db_manager, auth_manager, user_data, mock_logout)
        
        print("✅ Janela principal criada com sucesso!")
        
        # Carregar transações
        main_window.load_transactions()
        children = main_window.transactions_tree.get_children()
        print(f"✅ {len(children)} transações carregadas")
        
        if children:
            # Selecionar primeira transação
            main_window.transactions_tree.selection_set(children[0])
            
            # Obter informações da transação selecionada
            item = children[0]
            tags = main_window.transactions_tree.item(item)['tags']
            values = main_window.transactions_tree.item(item)['values']
            
            print(f"📋 Transação selecionada:")
            print(f"   Descrição: {values[1]}")
            print(f"   Valor: {values[4]}")
            print(f"   Tags: {tags}")
            
            # Encontrar ID da transação
            transaction_id = None
            for tag in tags:
                if isinstance(tag, int):
                    transaction_id = tag
                    break
                elif isinstance(tag, str) and tag.isdigit():
                    transaction_id = int(tag)
                    break
            
            print(f"🔍 ID da transação: {transaction_id}")
            
            # Testar carregamento direto dos dados da transação
            print("\n🔧 Testando carregamento de dados da transação...")
            try:
                from src.modules.transaction_manager import TransactionManager
                transaction_manager = TransactionManager(db_manager)
                
                transaction_data = transaction_manager.get_transaction_by_id(transaction_id, user_data['id'])
                print(f"✅ Dados da transação obtidos: {transaction_data is not None}")
                
                if transaction_data:
                    print(f"   Descrição: {transaction_data['description']}")
                    print(f"   Valor: {transaction_data['amount']}")
                    print(f"   Carteira ID: {transaction_data['wallet_id']}")
                    print(f"   Categoria ID: {transaction_data['category_id']}")
                
            except Exception as e:
                print(f"❌ Erro ao obter dados da transação: {str(e)}")
                import traceback
                traceback.print_exc()
            
            # Testar criação do formulário de edição
            print("\n🔧 Testando criação do formulário de edição...")
            try:
                from src.gui.transaction_form_new import TransactionFormNew
                
                # Criar formulário em modo de edição
                form = TransactionFormNew(
                    root, 
                    db_manager, 
                    user_data['id'], 
                    transaction_data['transaction_type'],
                    transaction_id
                )
                
                print("✅ Formulário de edição criado com sucesso!")
                
                # Fechar formulário imediatamente
                form.window.destroy()
                
            except Exception as e:
                print(f"❌ Erro ao criar formulário de edição: {str(e)}")
                import traceback
                traceback.print_exc()
        
        else:
            print("⚠️  Nenhuma transação disponível para teste")
        
        print("\n✅ TESTE DE EDIÇÃO CONCLUÍDO!")
        
        # Fechar janela
        root.destroy()
        
    except Exception as e:
        print(f"❌ ERRO GERAL: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_edit_transaction()
