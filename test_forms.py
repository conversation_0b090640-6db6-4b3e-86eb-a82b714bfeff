#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Teste dos formulários de carteira e transação
"""

import sys
import os
import tkinter as tk
from tkinter import messagebox

# Adicionar o diretório src ao path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from database import DatabaseManager

def test_wallet_dialog():
    """Testa o diálogo de carteira"""
    print("🧪 TESTE DO DIÁLOGO DE CARTEIRA")
    print("=" * 50)
    
    try:
        # Inicializar banco de dados
        db_manager = DatabaseManager()
        
        # Verificar usuário admin
        users = db_manager.execute_query("SELECT id FROM users WHERE username = 'admin'")
        if not users:
            print("❌ Usuário admin não encontrado!")
            return
        
        user_id = users[0]['id']
        print(f"✅ Usuário admin encontrado (ID: {user_id})")
        
        # Criar janela principal
        root = tk.Tk()
        root.withdraw()  # Esconder janela principal
        
        print("🔧 Testando WalletDialog...")
        
        # Tentar importar e criar WalletDialog
        from gui.wallet_dialog import WalletDialog
        dialog = WalletDialog(root, db_manager, user_id)
        
        print("✅ WalletDialog criado com sucesso!")
        print("   (Feche a janela para continuar o teste)")
        
        # Aguardar fechamento da janela
        root.wait_window(dialog.window)
        
        print(f"📊 Resultado: {dialog.result}")
        
        root.destroy()
        
    except Exception as e:
        print(f"❌ ERRO: {str(e)}")
        import traceback
        traceback.print_exc()

def test_transaction_form():
    """Testa o formulário de transação"""
    print("\n🧪 TESTE DO FORMULÁRIO DE TRANSAÇÃO")
    print("=" * 50)
    
    try:
        # Inicializar banco de dados
        db_manager = DatabaseManager()
        
        # Verificar usuário admin
        users = db_manager.execute_query("SELECT id FROM users WHERE username = 'admin'")
        if not users:
            print("❌ Usuário admin não encontrado!")
            return
        
        user_id = users[0]['id']
        print(f"✅ Usuário admin encontrado (ID: {user_id})")
        
        # Criar janela principal
        root = tk.Tk()
        root.withdraw()  # Esconder janela principal
        
        print("🔧 Testando TransactionFormNew (Receita)...")
        
        # Tentar importar e criar TransactionFormNew
        from gui.transaction_form_new import TransactionFormNew
        form = TransactionFormNew(root, db_manager, user_id, 'income')
        
        print("✅ TransactionFormNew (Receita) criado com sucesso!")
        print("   (Feche a janela para continuar o teste)")
        
        # Aguardar fechamento da janela
        root.wait_window(form.window)
        
        print(f"📊 Resultado: {form.result}")
        
        print("\n🔧 Testando TransactionFormNew (Despesa)...")
        
        # Testar formulário de despesa
        form2 = TransactionFormNew(root, db_manager, user_id, 'expense')
        
        print("✅ TransactionFormNew (Despesa) criado com sucesso!")
        print("   (Feche a janela para finalizar o teste)")
        
        # Aguardar fechamento da janela
        root.wait_window(form2.window)
        
        print(f"📊 Resultado: {form2.result}")
        
        root.destroy()
        
    except Exception as e:
        print(f"❌ ERRO: {str(e)}")
        import traceback
        traceback.print_exc()

def main():
    """Função principal"""
    print("🧪 TESTE DOS FORMULÁRIOS")
    print("=" * 70)
    
    # Testar diálogo de carteira
    test_wallet_dialog()
    
    # Testar formulário de transação
    test_transaction_form()
    
    print("\n✅ TESTES CONCLUÍDOS!")

if __name__ == "__main__":
    main()
