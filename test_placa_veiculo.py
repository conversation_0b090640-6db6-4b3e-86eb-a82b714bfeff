#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Teste da correção do erro de placa única
"""

import sys
import os

# Adicionar o diretório src ao path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from database import DatabaseManager
from auth import AuthManager

def test_placa_veiculo():
    """Testa a correção do erro de constraint única na placa"""
    print("🚗 TESTE DA CORREÇÃO DE PLACA ÚNICA")
    print("=" * 60)
    
    try:
        # Inicializar banco de dados
        db_manager = DatabaseManager()
        db_manager.initialize_database()
        
        auth_manager = AuthManager(db_manager)
        
        # Fazer login
        user_data = auth_manager.authenticate_user('admin', 'admin123')
        if not user_data:
            print("❌ Falha no login!")
            return
        
        print(f"✅ Login realizado: {user_data['full_name']}")
        user_id = user_data['id']
        
        # Limpar veículos de teste anteriores
        db_manager.execute_query("DELETE FROM vehicles WHERE user_id = ? AND name LIKE '%Teste%'", (user_id,))
        print("🧹 Veículos de teste anteriores removidos")
        
        # Teste 1: Cadastrar veículo sem placa
        print("\n🧪 TESTE 1: Veículo sem placa")
        try:
            vehicle_data_1 = {
                'name': 'Carro Teste 1',
                'brand': 'Toyota',
                'model': 'Corolla',
                'year': 2020,
                'license_plate': '',  # Placa vazia
                'color': 'Branco'
            }
            
            db_manager.add_vehicle(user_id, vehicle_data_1)
            print("   ✅ Veículo sem placa cadastrado com sucesso")
            
        except Exception as e:
            print(f"   ❌ Erro ao cadastrar veículo sem placa: {str(e)}")
        
        # Teste 2: Cadastrar outro veículo sem placa (deve funcionar)
        print("\n🧪 TESTE 2: Segundo veículo sem placa")
        try:
            vehicle_data_2 = {
                'name': 'Carro Teste 2',
                'brand': 'Honda',
                'model': 'Civic',
                'year': 2021,
                'license_plate': None,  # Placa None
                'color': 'Prata'
            }
            
            db_manager.add_vehicle(user_id, vehicle_data_2)
            print("   ✅ Segundo veículo sem placa cadastrado com sucesso")
            
        except Exception as e:
            print(f"   ❌ Erro ao cadastrar segundo veículo sem placa: {str(e)}")
        
        # Teste 3: Cadastrar veículo com placa
        print("\n🧪 TESTE 3: Veículo com placa")
        try:
            vehicle_data_3 = {
                'name': 'Carro Teste 3',
                'brand': 'Ford',
                'model': 'Focus',
                'year': 2019,
                'license_plate': 'ABC-1234',
                'color': 'Azul'
            }
            
            db_manager.add_vehicle(user_id, vehicle_data_3)
            print("   ✅ Veículo com placa 'ABC-1234' cadastrado com sucesso")
            
        except Exception as e:
            print(f"   ❌ Erro ao cadastrar veículo com placa: {str(e)}")
        
        # Teste 4: Tentar cadastrar veículo com placa duplicada (deve falhar)
        print("\n🧪 TESTE 4: Veículo com placa duplicada (deve falhar)")
        try:
            vehicle_data_4 = {
                'name': 'Carro Teste 4',
                'brand': 'Chevrolet',
                'model': 'Onix',
                'year': 2022,
                'license_plate': 'ABC-1234',  # Placa duplicada
                'color': 'Vermelho'
            }
            
            db_manager.add_vehicle(user_id, vehicle_data_4)
            print("   ❌ ERRO: Veículo com placa duplicada foi cadastrado (não deveria)")
            
        except ValueError as e:
            print(f"   ✅ Erro esperado capturado: {str(e)}")
        except Exception as e:
            print(f"   ⚠️  Erro inesperado: {str(e)}")
        
        # Teste 5: Cadastrar veículo com placa em minúscula (deve normalizar)
        print("\n🧪 TESTE 5: Placa em minúscula (deve normalizar)")
        try:
            vehicle_data_5 = {
                'name': 'Carro Teste 5',
                'brand': 'Volkswagen',
                'model': 'Gol',
                'year': 2018,
                'license_plate': 'def-5678',  # Minúscula
                'color': 'Verde'
            }
            
            db_manager.add_vehicle(user_id, vehicle_data_5)
            print("   ✅ Veículo com placa em minúscula cadastrado")
            
            # Verificar se foi normalizada
            vehicles = db_manager.execute_query(
                "SELECT license_plate FROM vehicles WHERE name = 'Carro Teste 5'", ()
            )
            if vehicles and vehicles[0]['license_plate'] == 'DEF-5678':
                print("   ✅ Placa normalizada para maiúscula: DEF-5678")
            else:
                print(f"   ⚠️  Placa não normalizada: {vehicles[0]['license_plate'] if vehicles else 'N/A'}")
            
        except Exception as e:
            print(f"   ❌ Erro ao cadastrar veículo com placa minúscula: {str(e)}")
        
        # Teste 6: Tentar cadastrar com placa normalizada duplicada
        print("\n🧪 TESTE 6: Placa normalizada duplicada (deve falhar)")
        try:
            vehicle_data_6 = {
                'name': 'Carro Teste 6',
                'brand': 'Fiat',
                'model': 'Uno',
                'year': 2017,
                'license_plate': 'DEF-5678',  # Maiúscula da anterior
                'color': 'Amarelo'
            }
            
            db_manager.add_vehicle(user_id, vehicle_data_6)
            print("   ❌ ERRO: Placa normalizada duplicada foi aceita")
            
        except ValueError as e:
            print(f"   ✅ Erro esperado capturado: {str(e)}")
        except Exception as e:
            print(f"   ⚠️  Erro inesperado: {str(e)}")
        
        # Verificar resultados finais
        print("\n📊 VERIFICAÇÃO FINAL:")
        vehicles = db_manager.execute_query(
            "SELECT name, license_plate FROM vehicles WHERE user_id = ? AND name LIKE '%Teste%' ORDER BY name", 
            (user_id,)
        )
        
        print(f"   Veículos de teste cadastrados: {len(vehicles)}")
        for vehicle in vehicles:
            placa = vehicle['license_plate'] or '(sem placa)'
            print(f"   - {vehicle['name']}: {placa}")
        
        # Resumo dos testes
        print(f"\n📋 RESUMO DOS TESTES:")
        print(f"   ✅ Veículos sem placa: Permitidos")
        print(f"   ✅ Múltiplos veículos sem placa: Permitidos")
        print(f"   ✅ Veículo com placa única: Permitido")
        print(f"   ✅ Placa duplicada: Bloqueada corretamente")
        print(f"   ✅ Normalização de placa: Funcionando")
        print(f"   ✅ Detecção de duplicata normalizada: Funcionando")
        
        if len(vehicles) == 4:  # Esperamos 4 veículos (2 sem placa + 2 com placas únicas)
            print(f"\n🎉 TODOS OS TESTES PASSARAM!")
            print(f"   ✅ Erro de constraint única corrigido")
            print(f"   ✅ Validação de placa implementada")
            print(f"   ✅ Normalização funcionando")
        else:
            print(f"\n⚠️  Alguns testes podem ter falhado")
            print(f"   Esperado: 4 veículos, Encontrado: {len(vehicles)}")
        
    except Exception as e:
        print(f"❌ ERRO GERAL: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_placa_veiculo()
