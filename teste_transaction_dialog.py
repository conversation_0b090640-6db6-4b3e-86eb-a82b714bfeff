#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Teste do diálogo de transação
"""

import tkinter as tk
from tkinter import ttk
import sys
import os

# Adicionar o diretório src ao path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from modules.database_manager import DatabaseManager
from gui.transaction_dialog import TransactionDialog

def test_transaction_dialog():
    """Testa o diálogo de transação"""
    # Criar janela principal
    root = tk.Tk()
    root.title("Teste Transaction Dialog")
    root.geometry("400x300")
    
    # Inicializar banco de dados
    db_manager = DatabaseManager()
    
    # Botão para testar nova receita
    def test_income():
        dialog = TransactionDialog(root, db_manager, 1, 'income')
        root.wait_window(dialog.window)
        print(f"Resultado: {dialog.result}")
    
    # Botão para testar nova despesa
    def test_expense():
        dialog = TransactionDialog(root, db_manager, 1, 'expense')
        root.wait_window(dialog.window)
        print(f"Resultado: {dialog.result}")
    
    # Interface de teste
    ttk.Label(root, text="Teste do Diálogo de Transação", 
             font=("Arial", 14, "bold")).pack(pady=20)
    
    ttk.Button(root, text="Testar Nova Receita", 
              command=test_income).pack(pady=10)
    
    ttk.Button(root, text="Testar Nova Despesa", 
              command=test_expense).pack(pady=10)
    
    ttk.Button(root, text="Sair", 
              command=root.quit).pack(pady=10)
    
    root.mainloop()

if __name__ == "__main__":
    test_transaction_dialog()
