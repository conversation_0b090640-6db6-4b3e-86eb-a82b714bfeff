#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Verificar estrutura da tabela users
"""

import sqlite3
from src.database import DatabaseManager

def main():
    print("Verificando estrutura da tabela users...")
    
    db_manager = DatabaseManager()
    conn = db_manager.get_connection()
    cursor = conn.cursor()
    
    # Verificar estrutura da tabela
    cursor.execute("PRAGMA table_info(users)")
    columns = cursor.fetchall()
    
    print("\nColunas da tabela users:")
    for col in columns:
        print(f"  - {col[1]} ({col[2]}) - {'NOT NULL' if col[3] else 'NULL'}")
    
    # Verificar dados existentes
    cursor.execute("SELECT * FROM users")
    users = cursor.fetchall()
    
    print(f"\nUsuários existentes: {len(users)}")
    for user in users:
        print(f"  - {user}")
    
    conn.close()

if __name__ == "__main__":
    main()
