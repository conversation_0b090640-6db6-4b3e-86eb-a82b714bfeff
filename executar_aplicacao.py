#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Script para executar a aplicação de Gestão de Contas
com as novas funcionalidades de veículos
"""

import sys
import os
from pathlib import Path

def main():
    """Executa a aplicação principal"""
    
    print("=" * 60)
    print("    SISTEMA DE GESTÃO DE CONTAS")
    print("    Versão 2.0 - Com Gerenciamento de Veículos")
    print("=" * 60)
    print()
    
    print("Funcionalidades disponíveis:")
    print("✓ Controle financeiro completo")
    print("✓ Gerenciamento de carteiras")
    print("✓ Transações (receitas e despesas)")
    print("✓ Relatórios financeiros")
    print("✓ NOVO: Cadastro de veículos")
    print("✓ NOVO: Controle de manutenção")
    print("✓ NOVO: Registro de combustível")
    print("✓ NOVO: Estatísticas de consumo")
    print()
    
    print("Credenciais padrão:")
    print("Usuário: admin")
    print("Senha: admin123")
    print()
    
    print("Iniciando aplicação...")
    print("-" * 60)
    
    try:
        # Importar e executar a aplicação principal
        from main import main as run_main
        run_main()
        
    except KeyboardInterrupt:
        print("\nAplicação encerrada pelo usuário.")
        sys.exit(0)
        
    except Exception as e:
        print(f"\nErro ao executar aplicação: {str(e)}")
        print("\nVerifique se todas as dependências estão instaladas:")
        print("py -m pip install -r requirements.txt")
        sys.exit(1)

if __name__ == "__main__":
    main()
