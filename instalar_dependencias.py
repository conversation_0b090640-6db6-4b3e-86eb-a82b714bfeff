#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Script para instalar dependências do Sistema de Gestão de Contas
"""

import subprocess
import sys
import os

def check_pip():
    """Verifica se pip está disponível"""
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "--version"], 
                            stdout=subprocess.DEVNULL, stderr=subprocess.DEVNULL)
        return True
    except subprocess.CalledProcessError:
        return False

def install_package(package):
    """Instala um pacote usando pip"""
    try:
        print(f"Instalando {package}...")
        subprocess.check_call([sys.executable, "-m", "pip", "install", package])
        print(f"✓ {package} instalado com sucesso!")
        return True
    except subprocess.CalledProcessError as e:
        print(f"✗ Erro ao instalar {package}: {e}")
        return False

def check_package(package):
    """Verifica se um pacote está instalado"""
    try:
        __import__(package)
        return True
    except ImportError:
        return False

def main():
    """Função principal"""
    print("Sistema de Gestão de Contas - Instalador de Dependências")
    print("=" * 60)
    
    # Verificar pip
    if not check_pip():
        print("✗ pip não encontrado. Instale o Python com pip primeiro.")
        input("Pressione Enter para sair...")
        return
    
    print("✓ pip encontrado")
    
    # Lista de dependências
    dependencies = [
        ("bcrypt", "Criptografia avançada de senhas"),
        ("pillow", "Manipulação de imagens (opcional)"),
    ]
    
    print("\nVerificando dependências...")
    
    # Verificar quais estão instaladas
    to_install = []
    for package, description in dependencies:
        if check_package(package):
            print(f"✓ {package} já instalado ({description})")
        else:
            print(f"✗ {package} não encontrado ({description})")
            to_install.append((package, description))
    
    if not to_install:
        print("\n✓ Todas as dependências já estão instaladas!")
        print("\nVocê pode executar:")
        print("- python main.py (versão completa)")
        print("- python main_sem_dependencias.py (versão sem dependências)")
        input("\nPressione Enter para sair...")
        return
    
    print(f"\n{len(to_install)} dependência(s) precisam ser instaladas:")
    for package, description in to_install:
        print(f"  • {package} - {description}")
    
    # Perguntar se deseja instalar
    try:
        response = input("\nDeseja instalar as dependências? (s/n): ").strip().lower()
        if response not in ['s', 'sim', 'y', 'yes']:
            print("\nInstalação cancelada.")
            print("\nVocê ainda pode usar a versão sem dependências:")
            print("python main_sem_dependencias.py")
            input("\nPressione Enter para sair...")
            return
    except KeyboardInterrupt:
        print("\n\nInstalação cancelada pelo usuário.")
        return
    
    # Instalar dependências
    print("\nInstalando dependências...")
    success_count = 0
    
    for package, description in to_install:
        if install_package(package):
            success_count += 1
    
    print(f"\n{success_count}/{len(to_install)} dependências instaladas com sucesso!")
    
    if success_count == len(to_install):
        print("\n🎉 Todas as dependências foram instaladas!")
        print("\nAgora você pode executar:")
        print("- python main.py (versão completa)")
        print("- python run.py (verificação e execução)")
        print("- python main_sem_dependencias.py (versão sem dependências)")
    else:
        print("\n⚠️  Algumas dependências falharam na instalação.")
        print("\nVocê ainda pode usar:")
        print("- python main_sem_dependencias.py (versão sem dependências)")
        print("- Ou instalar manualmente: pip install bcrypt pillow")
    
    input("\nPressione Enter para sair...")

if __name__ == "__main__":
    main()
