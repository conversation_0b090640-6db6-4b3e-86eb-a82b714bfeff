# 🔄 Sistema de Atualizações Automáticas

## 📋 Visão Geral

O Sistema de Atualizações Automáticas é uma funcionalidade avançada que executa tarefas de manutenção e atualização de dados em segundo plano, sem interferir na experiência do usuário. O sistema monitora e atualiza automaticamente:

- ⛽ **Eficiência de Combustível**: Recalcula automaticamente o consumo dos veículos
- 🔧 **Lembretes de Manutenção**: Cria alertas baseados em data e quilometragem
- 🛡️ **Vencimento de Seguros**: Monitora documentos e seguros de veículos
- 💰 **Cálculos Financeiros**: Atualiza saldos, estatísticas e transações recorrentes
- 🧹 **Limpeza de Dados**: Remove dados antigos e otimiza o banco de dados

## 🚀 Como Funciona

### Arquitetura do Sistema

```
┌─────────────────────────────────────────────────────────────┐
│                    APLICAÇÃO PRINCIPAL                      │
├─────────────────────────────────────────────────────────────┤
│  ┌─────────────────┐    ┌─────────────────────────────────┐ │
│  │ AutoUpdateManager│    │     TaskScheduler               │ │
│  │                 │    │                                 │ │
│  │ • Configurações │    │ • Execução em Background       │ │
│  │ • Tarefas       │    │ • Controle de Prioridades      │ │
│  │ • Monitoramento │    │ • Retry Logic                  │ │
│  └─────────────────┘    └─────────────────────────────────┘ │
├─────────────────────────────────────────────────────────────┤
│                    BANCO DE DADOS                           │
└─────────────────────────────────────────────────────────────┘
```

### Fluxo de Execução

1. **Inicialização**: Sistema inicia junto com a aplicação
2. **Agendamento**: Tarefas são agendadas conforme intervalos configurados
3. **Execução**: Tarefas executam em threads separadas
4. **Monitoramento**: Logs e status são registrados
5. **Repetição**: Ciclo se repete automaticamente

## ⚙️ Configuração

### Acessando as Configurações

1. Abra a aplicação
2. Vá para **Configurações** → **Sistema**
3. Clique em **⚙️ Atualizações Automáticas**

### Interface de Configuração

A interface possui 3 abas principais:

#### 📊 Aba Geral
- **Sistema Habilitado**: Liga/desliga todo o sistema
- **Intervalo de Verificação**: Frequência de verificação (recomendado: 300-600 segundos)

#### 📋 Aba Tarefas
Configure cada tarefa individualmente:

| Tarefa | Descrição | Intervalo Recomendado |
|--------|-----------|----------------------|
| **Eficiência de Combustível** | Recalcula consumo dos veículos | 60 minutos |
| **Lembretes de Manutenção** | Verifica manutenções pendentes | 1440 minutos (24h) |
| **Vencimento de Seguros** | Monitora documentos | 1440 minutos (24h) |
| **Cálculos Financeiros** | Atualiza saldos e estatísticas | 30 minutos |
| **Limpeza de Dados** | Remove dados antigos | 10080 minutos (7 dias) |

#### 📈 Aba Status
- Visualize status em tempo real
- Execute tarefas manualmente
- Inicie/pare o sistema

## 🔧 Tarefas Automáticas Detalhadas

### ⛽ Atualização de Eficiência de Combustível

**O que faz:**
- Recalcula automaticamente a eficiência (km/l) de todos os veículos
- Atualiza registros sem eficiência calculada
- Corrige cálculos baseados em novos abastecimentos

**Quando executa:**
- A cada 60 minutos (configurável)
- Automaticamente após novos registros de combustível

**Benefícios:**
- Estatísticas sempre atualizadas
- Detecção automática de problemas de consumo
- Histórico preciso de eficiência

### 🔧 Lembretes de Manutenção

**O que faz:**
- Verifica manutenções vencidas por data
- Monitora quilometragem para manutenções programadas
- Cria lembretes automáticos

**Critérios de Alerta:**
- **Por Data**: Manutenção vencida ou vencendo em até 30 dias
- **Por Quilometragem**: Veículo atingiu ou passou da quilometragem programada

**Tipos de Lembrete:**
- 🔴 **Vencido**: Manutenção já passou da data/quilometragem
- 🟡 **Próximo**: Vence em até 7 dias ou 1000 km
- 🟢 **Programado**: Agendado para o futuro

### 🛡️ Verificação de Vencimento de Seguros

**O que faz:**
- Monitora vencimento de seguros de veículos
- Verifica documentos importantes
- Cria alertas preventivos

**Níveis de Alerta:**
- **30 dias antes**: Aviso de vencimento próximo
- **7 dias antes**: Alerta de vencimento em breve
- **Vencido**: Documento já vencido

### 💰 Cálculos Financeiros

**O que faz:**
- Atualiza saldos de carteiras automaticamente
- Processa transações recorrentes
- Calcula estatísticas mensais
- Verifica contas vencidas
- Monitora orçamentos

**Funcionalidades:**
- **Transações Recorrentes**: Cria automaticamente transações mensais, semanais ou anuais
- **Alertas de Orçamento**: Avisa quando gastos excedem a média
- **Contas Vencidas**: Lista e alerta sobre pagamentos em atraso

### 🧹 Limpeza de Dados

**O que faz:**
- Remove logs antigos (>90 dias)
- Limpa transações não pagas muito antigas (>1 ano)
- Remove registros duplicados de combustível
- Otimiza banco de dados (VACUUM)

## 📊 Monitoramento e Logs

### Arquivos de Log

Os logs são salvos em `logs/auto_updates.log` e incluem:

```
2024-01-15 10:30:00 - AutoUpdateManager - INFO - Sistema iniciado
2024-01-15 10:30:15 - AutoUpdateManager - INFO - Executando: fuel_efficiency_update
2024-01-15 10:30:16 - AutoUpdateManager - INFO - Eficiência atualizada para 15 registros
2024-01-15 10:30:16 - AutoUpdateManager - INFO - Tarefa concluída: fuel_efficiency_update
```

### Níveis de Log

- **INFO**: Operações normais
- **WARNING**: Alertas e situações que requerem atenção
- **ERROR**: Erros que não impedem o funcionamento
- **DEBUG**: Informações detalhadas para diagnóstico

## 🛠️ Solução de Problemas

### Sistema Não Inicia

**Possíveis Causas:**
- Sistema desabilitado nas configurações
- Erro na configuração JSON
- Problemas de permissão de arquivo

**Soluções:**
1. Verifique se está habilitado em Configurações
2. Reinicie a aplicação
3. Verifique logs em `logs/auto_updates.log`

### Tarefas Não Executam

**Verificações:**
1. Tarefa está habilitada?
2. Intervalo configurado corretamente?
3. Sistema está rodando?

**Diagnóstico:**
- Use a aba "Status" para verificar última execução
- Execute tarefa manualmente para testar
- Verifique logs para erros específicos

### Performance Lenta

**Otimizações:**
- Aumente intervalos de tarefas menos críticas
- Desabilite tarefas desnecessárias
- Execute limpeza de dados manualmente

## 🔒 Segurança e Backup

### Backup Automático

O sistema **NÃO** interfere com:
- Dados do usuário
- Configurações principais
- Backups existentes

### Recuperação

Em caso de problemas:
1. Pare o sistema via interface
2. Restaure backup se necessário
3. Reinicie com configurações padrão

## 📱 Integração com Interface

### Indicadores Visuais

- 🟢 **Verde**: Sistema funcionando normalmente
- 🟡 **Amarelo**: Alertas ou avisos
- 🔴 **Vermelho**: Erros ou sistema parado

### Notificações

O sistema integra com:
- Alertas na tela principal
- Lembretes de manutenção
- Avisos de vencimento

## 🚀 Próximas Funcionalidades

### Em Desenvolvimento
- [ ] Notificações push
- [ ] Relatórios de performance
- [ ] Backup automático agendado
- [ ] Integração com calendário
- [ ] API para tarefas customizadas

### Sugestões de Melhoria
- Configuração de horários específicos
- Alertas por email/SMS
- Dashboard de monitoramento
- Histórico de execuções

## 📞 Suporte

Para problemas ou dúvidas:
1. Consulte os logs em `logs/auto_updates.log`
2. Use a função de teste: `py test_auto_updates.py`
3. Verifique configurações na interface
4. Reinicie o sistema se necessário

---

**Versão**: 1.0.0  
**Última Atualização**: Janeiro 2024  
**Compatibilidade**: Sistema de Gestão de Contas v2.0+
