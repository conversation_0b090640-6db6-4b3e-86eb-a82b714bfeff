#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Diálogo para criação e edição de carteiras
"""

import tkinter as tk
from tkinter import ttk, messagebox
from src.modules.wallet_manager import WalletManager

class WalletDialog:
    def __init__(self, parent, db_manager, user_id, wallet_id=None):
        self.parent = parent
        self.db_manager = db_manager
        self.user_id = user_id
        self.wallet_id = wallet_id
        self.wallet_manager = WalletManager(db_manager)
        self.result = False
        
        # Criar janela
        self.window = tk.Toplevel(parent)
        self.window.title("Nova Carteira" if not wallet_id else "Editar Carteira")
        self.window.geometry("450x400")
        self.window.resizable(False, False)
        
        # Centralizar janela
        self.center_window()
        
        # Configurar janela
        self.window.transient(parent)
        self.window.grab_set()
        
        # Criar interface
        self.create_widgets()
        
        # Carregar dados se for edição
        if wallet_id:
            self.load_wallet_data()
        
        # Focar no primeiro campo
        self.name_entry.focus()
    
    def center_window(self):
        """Centraliza a janela na tela"""
        self.window.update_idletasks()
        width = self.window.winfo_width()
        height = self.window.winfo_height()
        x = (self.window.winfo_screenwidth() // 2) - (width // 2)
        y = (self.window.winfo_screenheight() // 2) - (height // 2)
        self.window.geometry(f'{width}x{height}+{x}+{y}')
    
    def create_widgets(self):
        """Cria os widgets da interface"""
        # Frame principal
        main_frame = ttk.Frame(self.window, padding="20")
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # Título
        title_text = "Nova Carteira" if not self.wallet_id else "Editar Carteira"
        title_label = ttk.Label(main_frame, text=title_text, 
                               font=("Arial", 14, "bold"))
        title_label.pack(pady=(0, 20))
        
        # Frame do formulário
        form_frame = ttk.Frame(main_frame)
        form_frame.pack(fill=tk.X, pady=(0, 20))
        
        # Nome da carteira
        ttk.Label(form_frame, text="Nome da Carteira:").pack(anchor=tk.W, pady=(0, 5))
        self.name_entry = ttk.Entry(form_frame, font=("Arial", 11))
        self.name_entry.pack(fill=tk.X, pady=(0, 15))
        
        # Descrição
        ttk.Label(form_frame, text="Descrição:").pack(anchor=tk.W, pady=(0, 5))
        self.description_entry = ttk.Entry(form_frame, font=("Arial", 11))
        self.description_entry.pack(fill=tk.X, pady=(0, 15))
        
        # Tipo de carteira
        ttk.Label(form_frame, text="Tipo de Carteira:").pack(anchor=tk.W, pady=(0, 5))
        self.type_combo = ttk.Combobox(form_frame, state="readonly", font=("Arial", 11))
        self.type_combo['values'] = [
            'Conta Corrente',
            'Poupança', 
            'Cartão de Crédito',
            'Dinheiro'
        ]
        self.type_combo.current(0)
        self.type_combo.pack(fill=tk.X, pady=(0, 15))
        
        # Saldo inicial
        ttk.Label(form_frame, text="Saldo Inicial (R$):").pack(anchor=tk.W, pady=(0, 5))
        self.balance_entry = ttk.Entry(form_frame, font=("Arial", 11))
        self.balance_entry.pack(fill=tk.X, pady=(0, 15))
        self.balance_entry.insert(0, "0,00")
        
        # Frame dos botões
        button_frame = ttk.Frame(main_frame)
        button_frame.pack(fill=tk.X, pady=(10, 0))
        
        # Botão cancelar
        cancel_btn = ttk.Button(button_frame, text="Cancelar", command=self.cancel)
        cancel_btn.pack(side=tk.LEFT, padx=(0, 10))
        
        # Botão salvar
        save_text = "Criar Carteira" if not self.wallet_id else "Salvar Alterações"
        save_btn = ttk.Button(button_frame, text=save_text, command=self.save)
        save_btn.pack(side=tk.RIGHT)
        
        # Bind Enter para salvar
        self.window.bind('<Return>', lambda e: self.save())
        self.window.bind('<Escape>', lambda e: self.cancel())
    
    def load_wallet_data(self):
        """Carrega dados da carteira para edição"""
        try:
            wallet = self.wallet_manager.get_wallet_by_id(self.wallet_id, self.user_id)
            if wallet:
                self.name_entry.insert(0, wallet['name'])
                self.description_entry.insert(0, wallet['description'] or '')
                
                # Mapear tipo de carteira
                type_mapping = {
                    'checking': 'Conta Corrente',
                    'savings': 'Poupança',
                    'credit': 'Cartão de Crédito',
                    'cash': 'Dinheiro'
                }
                
                wallet_type = type_mapping.get(wallet['wallet_type'], 'Conta Corrente')
                self.type_combo.set(wallet_type)
                
                # Saldo inicial
                self.balance_entry.delete(0, tk.END)
                self.balance_entry.insert(0, f"{wallet['initial_balance']:,.2f}".replace('.', ','))
                
        except Exception as e:
            messagebox.showerror("Erro", f"Erro ao carregar dados da carteira: {str(e)}")
    
    def save(self):
        """Salva a carteira"""
        try:
            # Validar campos
            name = self.name_entry.get().strip()
            description = self.description_entry.get().strip()
            wallet_type_display = self.type_combo.get()
            balance_text = self.balance_entry.get().replace(',', '.')
            
            if not name:
                messagebox.showerror("Erro", "Nome da carteira é obrigatório")
                self.name_entry.focus()
                return
            
            try:
                initial_balance = float(balance_text)
            except ValueError:
                messagebox.showerror("Erro", "Saldo inicial deve ser um número válido")
                self.balance_entry.focus()
                return
            
            if initial_balance < 0:
                messagebox.showerror("Erro", "Saldo inicial não pode ser negativo")
                self.balance_entry.focus()
                return
            
            # Mapear tipo de carteira
            type_mapping = {
                'Conta Corrente': 'checking',
                'Poupança': 'savings',
                'Cartão de Crédito': 'credit',
                'Dinheiro': 'cash'
            }
            
            wallet_type = type_mapping.get(wallet_type_display, 'checking')
            
            # Salvar carteira
            if self.wallet_id:
                # Editar carteira existente
                self.wallet_manager.update_wallet(
                    wallet_id=self.wallet_id,
                    user_id=self.user_id,
                    name=name,
                    description=description,
                    initial_balance=initial_balance,
                    wallet_type=wallet_type
                )
                messagebox.showinfo("Sucesso", "Carteira atualizada com sucesso!")
            else:
                # Criar nova carteira
                self.wallet_manager.create_wallet(
                    user_id=self.user_id,
                    name=name,
                    description=description,
                    initial_balance=initial_balance,
                    wallet_type=wallet_type
                )
                messagebox.showinfo("Sucesso", "Carteira criada com sucesso!")
            
            self.result = True
            self.window.destroy()
            
        except Exception as e:
            messagebox.showerror("Erro", str(e))
    
    def cancel(self):
        """Cancela a operação"""
        self.result = False
        self.window.destroy()
