#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Teste da aba de configurações
"""

import sys
import os
import tkinter as tk

# Adicionar o diretório src ao path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from database import DatabaseManager
from auth import AuthManager
from gui.main_window import MainWindow

def test_settings_tab():
    """Testa a aba de configurações"""
    print("⚙️ TESTE DA ABA DE CONFIGURAÇÕES")
    print("=" * 60)
    
    try:
        # Inicializar banco de dados
        db_manager = DatabaseManager()
        db_manager.initialize_database()
        
        auth_manager = AuthManager(db_manager)
        
        # Fazer login
        user_data = auth_manager.authenticate_user('admin', 'admin123')
        if not user_data:
            print("❌ Falha no login!")
            return
        
        print(f"✅ Login realizado: {user_data['full_name']}")
        
        # <PERSON><PERSON>r janela principal
        root = tk.Tk()
        root.title("Teste Aba Configurações")
        root.geometry("1200x800")
        
        # Função de logout mock
        def mock_logout():
            print("Logout chamado")
        
        # Criar MainWindow
        main_window = MainWindow(root, db_manager, auth_manager, user_data, mock_logout)
        
        print("✅ Janela principal criada com sucesso!")
        
        # Navegar para a aba de configurações
        main_window.notebook.select(4)  # Aba de configurações
        
        # Testar gerenciamento de categorias
        print("\n🔧 Testando gerenciamento de categorias...")
        try:
            main_window.show_categories_manager()
            print("✅ Gerenciamento de categorias funcionou!")
        except Exception as e:
            print(f"❌ Erro no gerenciamento de categorias: {str(e)}")
        
        # Testar importação de categorias padrão
        print("\n🔧 Testando importação de categorias padrão...")
        try:
            # Mock para evitar diálogo real
            import tkinter.messagebox
            original_askyesno = tkinter.messagebox.askyesno
            tkinter.messagebox.askyesno = lambda title, message: False  # Simular "Não"
            
            main_window.import_default_categories()
            print("✅ Importação de categorias padrão funcionou!")
            
            # Restaurar função original
            tkinter.messagebox.askyesno = original_askyesno
            
        except Exception as e:
            print(f"❌ Erro na importação de categorias: {str(e)}")
            tkinter.messagebox.askyesno = original_askyesno
        
        # Testar alteração de senha
        print("\n🔧 Testando alteração de senha...")
        try:
            main_window.show_change_password()
            print("✅ Alteração de senha funcionou!")
        except Exception as e:
            print(f"❌ Erro na alteração de senha: {str(e)}")
        
        # Testar edição de perfil
        print("\n🔧 Testando edição de perfil...")
        try:
            main_window.show_edit_profile()
            print("✅ Edição de perfil funcionou!")
        except Exception as e:
            print(f"❌ Erro na edição de perfil: {str(e)}")
        
        # Testar backup
        print("\n🔧 Testando backup...")
        try:
            # Mock para evitar diálogo de arquivo
            import tkinter.filedialog
            original_asksaveasfilename = tkinter.filedialog.asksaveasfilename
            tkinter.filedialog.asksaveasfilename = lambda **kwargs: None
            
            main_window.backup_database()
            print("✅ Backup funcionou!")
            
            # Restaurar função original
            tkinter.filedialog.asksaveasfilename = original_asksaveasfilename
            
        except Exception as e:
            print(f"❌ Erro no backup: {str(e)}")
            tkinter.filedialog.asksaveasfilename = original_asksaveasfilename
        
        # Testar restauração
        print("\n🔧 Testando restauração...")
        try:
            # Mock para evitar diálogo
            import tkinter.messagebox
            original_askyesno = tkinter.messagebox.askyesno
            tkinter.messagebox.askyesno = lambda title, message: False  # Simular "Não"
            
            main_window.restore_database()
            print("✅ Restauração funcionou!")
            
            # Restaurar função original
            tkinter.messagebox.askyesno = original_askyesno
            
        except Exception as e:
            print(f"❌ Erro na restauração: {str(e)}")
            tkinter.messagebox.askyesno = original_askyesno
        
        print("\n✅ TODOS OS TESTES CONCLUÍDOS!")
        print("💡 Se não houve erros, a aba de configurações está funcionando.")
        
        # Fechar janela após um tempo
        root.after(3000, root.destroy)  # Fechar após 3 segundos
        root.mainloop()
        
    except Exception as e:
        print(f"❌ ERRO GERAL: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_settings_tab()
