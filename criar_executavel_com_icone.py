#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Cria executável da aplicação Gestão de Contas com ícone personalizado
"""

import subprocess
import sys
import shutil
import os
from pathlib import Path
import time

def check_dependencies():
    """Verifica e instala dependências necessárias"""
    dependencies = ['pyinstaller']
    
    for dep in dependencies:
        try:
            __import__(dep)
            print(f"✓ {dep} encontrado")
        except ImportError:
            print(f"{dep} não encontrado. Instalando...")
            try:
                subprocess.check_call([sys.executable, "-m", "pip", "install", dep])
                print(f"✓ {dep} instalado com sucesso")
            except:
                print(f"✗ Erro ao instalar {dep}")
                return False
    
    return True

def create_icon_if_needed():
    """Cria ícone se não existir"""
    icon_path = Path("app_icon.ico")
    
    if icon_path.exists():
        print("✓ Ícone encontrado: app_icon.ico")
        return str(icon_path)
    
    print("Ícone não encontrado. Criando...")
    try:
        # Executar script de criação de ícone
        result = subprocess.run([sys.executable, "criar_icone.py"], 
                              capture_output=True, text=True, timeout=30)
        
        if icon_path.exists():
            print("✓ Ícone criado com sucesso")
            return str(icon_path)
        else:
            print("⚠️ Não foi possível criar ícone. Usando padrão do sistema.")
            return None
            
    except Exception as e:
        print(f"⚠️ Erro ao criar ícone: {e}")
        return None

def clean_build_files():
    """Limpa arquivos de build anteriores"""
    try:
        # Remover diretórios de build
        for dir_name in ['build', 'dist', '__pycache__']:
            if Path(dir_name).exists():
                shutil.rmtree(dir_name)
                print(f"✓ Removido: {dir_name}")
        
        # Remover arquivos .spec
        for spec_file in Path('.').glob('*.spec'):
            spec_file.unlink()
            print(f"✓ Removido: {spec_file}")
            
    except Exception as e:
        print(f"⚠️ Aviso ao limpar arquivos: {e}")

def create_executable():
    """Cria o executável com ícone"""
    print("\nCriando executável GestaoContas.exe...")
    
    try:
        # Verificar se existe ícone
        icon_path = create_icon_if_needed()
        
        # Comando base para criar executável
        cmd = [
            sys.executable, "-m", "PyInstaller",
            "--onefile",
            "--windowed",
            "--name=GestaoContas",
            "--clean"
        ]
        
        # Adicionar ícone se disponível
        if icon_path and Path(icon_path).exists():
            cmd.extend(["--icon", icon_path])
            print(f"✓ Usando ícone: {icon_path}")
        else:
            print("⚠️ Criando executável sem ícone personalizado")
        
        # Adicionar arquivo principal
        cmd.append("executar_terminal.py")
        
        print("Executando PyInstaller...")
        print(f"Comando: {' '.join(cmd)}")
        
        result = subprocess.run(cmd, capture_output=True, text=True)
        
        if result.returncode == 0:
            exe_path = Path("dist/GestaoContas.exe")
            if exe_path.exists():
                size_mb = exe_path.stat().st_size / (1024 * 1024)
                print(f"✓ Executável criado: {exe_path}")
                print(f"✓ Tamanho: {size_mb:.1f} MB")
                return True
            else:
                print("✗ Executável não encontrado após criação")
                return False
        else:
            print("✗ Erro ao criar executável:")
            print("STDOUT:", result.stdout)
            print("STDERR:", result.stderr)
            return False
            
    except Exception as e:
        print(f"✗ Erro: {str(e)}")
        return False

def create_distribution_package():
    """Cria pacote de distribuição completo"""
    print("\nCriando pacote de distribuição...")
    
    try:
        # Aguardar um pouco para liberar arquivos
        time.sleep(2)
        
        # Criar diretório de distribuição
        dist_dir = Path("GestaoContas_v2.0")
        if dist_dir.exists():
            shutil.rmtree(dist_dir)
        
        dist_dir.mkdir()
        
        # Copiar executável
        exe_source = Path("dist/GestaoContas.exe")
        if exe_source.exists():
            shutil.copy2(exe_source, dist_dir / "GestaoContas.exe")
            print("✓ Executável copiado")
        else:
            print("✗ Executável não encontrado")
            return False
        
        # Criar diretórios necessários
        (dist_dir / "data").mkdir()
        (dist_dir / "backups").mkdir()
        (dist_dir / "logs").mkdir()
        print("✓ Diretórios criados")
        
        # Copiar ícone se existir
        icon_path = Path("app_icon.ico")
        if icon_path.exists():
            shutil.copy2(icon_path, dist_dir / "app_icon.ico")
            print("✓ Ícone copiado")
        
        # Criar README
        readme_content = """# GESTÃO DE CONTAS v2.0

## 🚀 Como Usar

1. Execute: **GestaoContas.exe**
2. Login padrão:
   - Usuário: **admin**
   - Senha: **admin123**

## 📋 Funcionalidades

✅ **Controle Financeiro Completo**
- Gestão de carteiras (conta corrente, poupança, cartão, dinheiro)
- Transações com parcelas automáticas
- Controle de vencimentos
- Relatórios financeiros avançados

✅ **Gerenciamento de Veículos**
- Cadastro de veículos
- Controle de manutenção
- Registro de combustível
- Estatísticas de consumo

✅ **Sistema de Usuários**
- Múltiplos usuários
- Controle de permissões
- Administração avançada

✅ **Recursos Avançados**
- Backup e restauração automática
- Categorias personalizáveis
- Interface moderna e intuitiva
- Atualizações automáticas

## 📁 Estrutura de Pastas

- **data/**: Banco de dados da aplicação
- **backups/**: Backups automáticos
- **logs/**: Logs do sistema

## 🆘 Suporte

Em caso de problemas:
1. Verifique se as pastas data/, backups/ e logs/ existem
2. Execute como administrador se necessário
3. Verifique se o Windows Defender não está bloqueando

---
**Gestão de Contas v2.0** - Sistema completo de gestão financeira e veicular
"""
        
        with open(dist_dir / "README.txt", 'w', encoding='utf-8') as f:
            f.write(readme_content)
        print("✓ README criado")
        
        print("✓ Pacote criado com sucesso!")
        return True
        
    except Exception as e:
        print(f"✗ Erro ao criar pacote: {str(e)}")
        return False

def main():
    """Função principal"""
    print("CRIADOR DE EXECUTÁVEL COM ÍCONE")
    print("Sistema de Gestão de Contas v2.0")
    print("=" * 60)
    
    # Limpar arquivos anteriores
    print("Limpando arquivos de build anteriores...")
    clean_build_files()
    
    # Verificar dependências
    print("\nVerificando dependências...")
    if not check_dependencies():
        print("\n✗ Não foi possível instalar todas as dependências")
        print("Alternativa: Use 'python executar_terminal.py'")
        input("Pressione Enter para sair...")
        return
    
    # Criar executável
    if not create_executable():
        print("\n✗ Falha na criação do executável")
        print("Alternativa: Use 'python executar_terminal.py'")
        input("Pressione Enter para sair...")
        return
    
    # Criar pacote de distribuição
    if not create_distribution_package():
        print("\n✗ Falha na criação do pacote")
        input("Pressione Enter para sair...")
        return
    
    print("\n" + "=" * 60)
    print("🎉 EXECUTÁVEL COM ÍCONE CRIADO COM SUCESSO!")
    print("\n📦 ARQUIVOS GERADOS:")
    print("  • dist/GestaoContas.exe (executável)")
    print("  • GestaoContas_v2.0/ (pasta completa)")
    print("  • app_icon.ico (ícone da aplicação)")
    
    print("\n🚀 COMO USAR:")
    print("  1. Acesse: GestaoContas_v2.0/")
    print("  2. Execute: GestaoContas.exe")
    print("  3. Login: admin/admin123")
    print("  4. Aproveite o sistema completo!")
    
    print("\n✨ RECURSOS INCLUÍDOS:")
    print("  • 🎨 Ícone personalizado")
    print("  • 💰 Gestão financeira completa")
    print("  • 🚗 Gerenciamento de veículos")
    print("  • 👥 Sistema de usuários")
    print("  • 📊 Relatórios avançados")
    print("  • 🔄 Backup automático")
    print("  • 📱 Interface moderna")
    
    print("\n🎯 EXECUTÁVEL PRONTO PARA DISTRIBUIÇÃO!")
    print("=" * 60)
    
    input("\nPressione Enter para sair...")

if __name__ == "__main__":
    main()
