#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Arquivo de compatibilidade para TransactionDialog
Redireciona para TransactionFormNew
"""

from .transaction_form_new import TransactionFormNew

class TransactionDialog:
    """Classe de compatibilidade que redireciona para TransactionFormNew"""
    
    def __init__(self, parent, db_manager, user_id, transaction_type):
        """Inicializa o diálogo de transação"""
        self.parent = parent
        self.db_manager = db_manager
        self.user_id = user_id
        self.transaction_type = transaction_type
        
        # Criar o formulário novo
        self.form = TransactionFormNew(parent, db_manager, user_id, transaction_type)
        
        # Aguardar fechamento da janela
        parent.wait_window(self.form.window)
        
        # Transferir resultado
        self.result = self.form.result
        
        # Compatibilidade com interface antiga
        self.window = None  # Janela já foi fechada
