#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Execução direta e simples da aplicação
"""

import tkinter as tk
from tkinter import messagebox
import sys
import os

def main():
    """Executa a aplicação de forma direta"""
    
    print("Iniciando Sistema de Gestão de Contas...")
    
    try:
        # Criar janela de teste primeiro
        root = tk.Tk()
        root.title("Sistema de Gestão de Contas - Iniciando...")
        root.geometry("500x300")
        
        # Centralizar janela
        root.update_idletasks()
        x = (root.winfo_screenwidth() // 2) - (500 // 2)
        y = (root.winfo_screenheight() // 2) - (300 // 2)
        root.geometry(f"500x300+{x}+{y}")
        
        # Trazer para frente
        root.lift()
        root.attributes('-topmost', True)
        root.after_idle(root.attributes, '-topmost', False)
        
        frame = tk.Frame(root, padx=30, pady=30)
        frame.pack(fill=tk.BOTH, expand=True)
        
        tk.Label(frame, text="🚗 Sistema de Gestão de Contas", 
                font=('Arial', 16, 'bold')).pack(pady=10)
        
        tk.Label(frame, text="Versão 2.0 - Com Gerenciamento de Veículos", 
                font=('Arial', 12)).pack(pady=5)
        
        tk.Label(frame, text="Clique no botão abaixo para iniciar:", 
                font=('Arial', 10)).pack(pady=20)
        
        def iniciar_aplicacao():
            try:
                root.destroy()
                
                # Importar e executar aplicação principal
                from src.database import DatabaseManager
                from src.auth import AuthManager
                from src.gui.login_window import LoginWindow
                from src.gui.main_window import MainWindow
                
                # Inicializar componentes
                db_manager = DatabaseManager()
                db_manager.initialize_database()
                
                auth_manager = AuthManager(db_manager)
                
                # Criar usuário admin se não existir
                if not auth_manager.user_exists('admin'):
                    auth_manager.create_user(
                        username='admin',
                        password='admin123',
                        email='<EMAIL>',
                        is_admin=True,
                        full_name='Administrador do Sistema'
                    )
                
                # Criar janela principal
                main_root = tk.Tk()
                main_root.withdraw()  # Esconder inicialmente
                
                def on_login_success(user_data):
                    main_root.deiconify()
                    MainWindow(main_root, db_manager, auth_manager, user_data, 
                              lambda: main_root.quit())
                
                # Mostrar login
                LoginWindow(main_root, auth_manager, on_login_success)
                
                # Iniciar loop principal
                main_root.mainloop()
                
            except Exception as e:
                messagebox.showerror("Erro", f"Erro ao iniciar aplicação:\n{str(e)}")
                import traceback
                traceback.print_exc()
        
        tk.Button(frame, text="🚀 INICIAR APLICAÇÃO", 
                 command=iniciar_aplicacao,
                 font=('Arial', 12, 'bold'),
                 bg='#007ACC', fg='white',
                 padx=20, pady=10).pack(pady=20)
        
        tk.Label(frame, text="Credenciais padrão: admin / admin123", 
                font=('Arial', 9), fg='gray').pack(pady=10)
        
        # Mostrar janela
        root.mainloop()
        
    except Exception as e:
        print(f"Erro: {e}")
        import traceback
        traceback.print_exc()
        
        # Tentar mostrar erro em messagebox
        try:
            root = tk.Tk()
            root.withdraw()
            messagebox.showerror("Erro", f"Erro ao iniciar:\n{str(e)}")
        except:
            pass

if __name__ == "__main__":
    main()
