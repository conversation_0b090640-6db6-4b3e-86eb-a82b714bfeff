# 🚗 Sistema de Gestão de Contas - Como Executar

## 🎯 Opções Disponíveis

### 1. 📦 **EXECUTÁVEL (RECOMENDADO)**
O executável já foi criado e está pronto para uso!

**Localização:** `dist/GestaoContas.exe`

**Como usar:**
```bash
# Opção 1: Clique duplo no arquivo
dist/GestaoContas.exe

# Opção 2: Execute pelo terminal
cd dist
GestaoContas.exe

# Opção 3: Use o script automático
executar.bat
```

### 2. 🐍 **Python Direto**
Se preferir executar via Python:

```bash
# Opção 1: Script otimizado
py executar_terminal.py

# Opção 2: Script principal
py main.py
```

### 3. 🔨 **Recriar Executável**
Para gerar um novo executável:

```bash
# Opção 1: Script Python
py criar_executavel_simples.py

# Opção 2: Sc<PERSON>t Batch
criar_executavel.bat
```

## 🔑 Credenciais de Login

**Usuário:** `admin`  
**Senha:** `admin123`

## 📋 Funcionalidades Disponíveis

- ✅ **Controle Financeiro Completo**
  - Receitas e despesas
  - Categorização automática
  - Relatórios detalhados

- ✅ **Gerenciamento de Carteiras**
  - Múltiplas carteiras
  - Transferências entre carteiras
  - Saldos em tempo real

- ✅ **Sistema de Veículos** (NOVO!)
  - Cadastro de veículos
  - Controle de manutenção
  - Registro de combustível
  - Estatísticas de consumo

- ✅ **Relatórios e Análises**
  - Gráficos interativos
  - Exportação de dados
  - Filtros avançados

## 🚨 Solução de Problemas

### Problema: Antivírus bloqueia o executável
**Solução:**
1. Adicione exceção no antivírus para a pasta do projeto
2. Execute como administrador
3. Use a versão Python como alternativa

### Problema: "Python não encontrado"
**Solução:**
```bash
# Use 'py' em vez de 'python'
py executar_terminal.py

# Ou instale Python do Microsoft Store
```

### Problema: Erro de permissão
**Solução:**
1. Execute como administrador
2. Verifique permissões da pasta
3. Mova para uma pasta com permissões completas

### Problema: Banco de dados corrompido
**Solução:**
```bash
# Delete a pasta data e execute novamente
rmdir /s data
py executar_terminal.py
```

## 📁 Estrutura de Arquivos

```
📦 Gestao de Contas Python/
├── 🚀 executar.bat                    # Script principal de execução
├── 🔨 criar_executavel.bat            # Recriar executável
├── 🐍 executar_terminal.py            # Versão Python
├── 📁 dist/
│   ├── 🎯 GestaoContas.exe           # EXECUTÁVEL PRINCIPAL
│   └── 📖 README_EXECUTAVEL.md       # Documentação
├── 📁 src/                           # Código fonte
├── 📁 data/                          # Banco de dados
└── 📋 requirements.txt               # Dependências
```

## 🎯 Recomendação de Uso

**Para usuários finais:**
1. Use `executar.bat` - detecta automaticamente a melhor opção
2. Ou execute diretamente `dist/GestaoContas.exe`

**Para desenvolvedores:**
1. Use `py executar_terminal.py` para desenvolvimento
2. Use `criar_executavel_simples.py` para gerar novos executáveis

## 📞 Informações Técnicas

- **Tamanho do executável:** ~24 MB
- **Plataforma:** Windows 10+
- **Dependências:** Incluídas no executável
- **Banco de dados:** SQLite (criado automaticamente)

## 🔄 Atualizações

Para atualizar o sistema:
1. Baixe a nova versão
2. Execute `criar_executavel_simples.py`
3. Seus dados serão preservados na pasta `data/`

---

**💡 Dica:** Para melhor experiência, crie um atalho do executável na área de trabalho!

**📧 Suporte:** Em caso de problemas, verifique este arquivo primeiro.
