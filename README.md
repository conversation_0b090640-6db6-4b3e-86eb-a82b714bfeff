# Sistema de Gestão de Contas

Um sistema completo de gestão financeira pessoal desenvolvido em Python com interface gráfica Tkinter.

## 🚀 Funcionalidades

### ✅ Implementadas
- **Sistema de Autenticação**
  - Login e registro de usuários
  - Controle de acesso (usuário comum/administrador)
  - Criptografia de senhas

- **Gestão de Carteiras**
  - Múltiplas carteiras/contas
  - Diferentes tipos (Conta Corrente, Poupança, Cartão de Crédito, Dinheiro)
  - Controle de saldo inicial e atual

- **Gestão de Transações**
  - Receitas e despesas
  - Categorização automática
  - Controle de vencimentos
  - Transações recorrentes
  - Status de pagamento

- **Sistema de Categorias**
  - Categorias padrão do sistema
  - Categorias personalizadas por usuário
  - Cores e ícones personalizáveis
  - Relatórios por categoria

- **Sistema de Alertas**
  - Contas vencendo
  - Contas em atraso
  - Saldo baixo/negativo
  - Transações recorrentes

- **Backup e Restauração**
  - Backup completo em ZIP
  - Backup do banco de dados
  - Verificação de integridade
  - Restauração automática

- **Interface Gráfica Completa**
  - Dashboard com resumos
  - Abas organizadas por funcionalidade
  - Menus contextuais
  - Barra de status com alertas

## 📋 Requisitos

- Python 3.7 ou superior
- Bibliotecas Python (instalação opcional):
  - `bcrypt` (para criptografia avançada)
  - `pillow` (para manipulação de imagens)
  - `pyinstaller` (para criar executável)

## 🛠️ Instalação

1. **Clone ou baixe o projeto**
   ```bash
   git clone <url-do-repositorio>
   cd Gestao-de-Contas
   ```

2. **Instale as dependências (opcional)**
   ```bash
   pip install -r requirements.txt
   ```
   
   *Nota: O sistema funciona apenas com bibliotecas padrão do Python*

3. **Execute o sistema**
   ```bash
   python main.py
   ```

## 🔐 Primeiro Acesso

**Usuário padrão:**
- **Login:** admin
- **Senha:** admin123

*Recomenda-se alterar a senha após o primeiro acesso*

## 📁 Estrutura do Projeto

```
Gestao-de-Contas/
├── main.py                 # Arquivo principal
├── requirements.txt        # Dependências
├── README.md              # Este arquivo
├── test_system.py         # Testes completos
├── quick_test.py          # Teste rápido
├── data/                  # Banco de dados
│   └── gestao_contas.db
├── backups/               # Backups automáticos
├── src/                   # Código fonte
│   ├── __init__.py
│   ├── database.py        # Gerenciamento do banco
│   ├── auth.py           # Autenticação
│   ├── gui/              # Interface gráfica
│   │   ├── __init__.py
│   │   ├── login_window.py
│   │   └── main_window.py
│   └── modules/          # Módulos funcionais
│       ├── __init__.py
│       ├── wallet_manager.py
│       ├── transaction_manager.py
│       ├── category_manager.py
│       ├── alert_manager.py
│       └── backup_manager.py
```

## 🎯 Como Usar

### 1. **Dashboard**
- Visualize resumo financeiro
- Acompanhe transações recentes
- Monitore alertas importantes

### 2. **Carteiras**
- Crie diferentes tipos de conta
- Gerencie saldos
- Acompanhe movimentações

### 3. **Transações**
- Registre receitas e despesas
- Defina categorias
- Configure vencimentos
- Marque como pago/pendente

### 4. **Relatórios**
- Análise por categoria
- Fluxo de caixa
- Contas a vencer

### 5. **Configurações**
- Gerencie categorias
- Altere preferências
- Configure alertas

### 6. **Administração** (apenas admin)
- Gerencie usuários
- Visualize logs do sistema
- Configurações avançadas

## 💾 Backup e Segurança

- **Backup Automático:** O sistema cria backups regulares
- **Backup Manual:** Menu Arquivo > Backup
- **Restauração:** Menu Arquivo > Restaurar
- **Localização:** Pasta `backups/`

## 🔧 Solução de Problemas

### Erro ao iniciar
1. Verifique se Python está instalado
2. Execute `python --version`
3. Tente `python3 main.py` se necessário

### Banco de dados corrompido
1. Use Menu Arquivo > Restaurar
2. Selecione backup mais recente
3. Ou delete `data/gestao_contas.db` para reiniciar

### Interface não aparece
1. Verifique se Tkinter está instalado
2. No Linux: `sudo apt-get install python3-tk`
3. Teste com `python -c "import tkinter"`

## 🚀 Criando Executável

Para criar um executável independente:

```bash
pip install pyinstaller
pyinstaller --onefile --windowed main.py
```

O executável será criado em `dist/main.exe`

## 📊 Funcionalidades Avançadas

### Transações Recorrentes
- Configure receitas/despesas que se repetem
- Tipos: diária, semanal, mensal, anual
- Alertas automáticos para processamento

### Sistema de Alertas
- **Críticos:** Contas vencendo hoje, saldo negativo
- **Altos:** Contas vencendo amanhã
- **Médios:** Contas vencendo em poucos dias, saldo baixo
- **Baixos:** Lembretes gerais

### Categorização Inteligente
- Categorias padrão do sistema
- Categorias personalizadas por usuário
- Cores e ícones para identificação visual
- Estatísticas de uso

## 🔒 Segurança

- Senhas criptografadas com hash
- Controle de acesso por usuário
- Logs de auditoria
- Backup automático dos dados
- Verificação de integridade

## 📈 Relatórios Disponíveis

1. **Dashboard Financeiro**
   - Saldo total das carteiras
   - Receitas/despesas do mês
   - Balanço mensal

2. **Por Categoria**
   - Gastos por categoria
   - Comparativo mensal
   - Tendências

3. **Fluxo de Caixa**
   - Entradas e saídas
   - Projeções
   - Análise temporal

4. **Contas a Vencer**
   - Próximos vencimentos
   - Contas em atraso
   - Planejamento de pagamentos

## 🆘 Suporte

Para problemas ou sugestões:
1. Verifique os logs em `data/`
2. Execute `python test_system.py` para diagnóstico
3. Consulte a documentação no código

## 📝 Licença

Este projeto é de uso livre para fins pessoais e educacionais.

---

**Sistema de Gestão de Contas v1.0.0**  
*Desenvolvido em Python com Tkinter*
