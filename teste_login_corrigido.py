#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Teste do login corrigido
"""

from src.database import DatabaseManager
from src.auth import AuthManager

def main():
    print("=" * 50)
    print("    TESTE DE LOGIN CORRIGIDO")
    print("=" * 50)
    
    try:
        # Inicializar componentes
        print("🔄 Inicializando...")
        db_manager = DatabaseManager()
        db_manager.initialize_database()
        auth_manager = AuthManager(db_manager)
        
        # Verificar usuários existentes
        print("\n📋 Usuários existentes:")
        users = db_manager.execute_query("SELECT id, username, email, full_name, is_admin FROM users")
        
        for user in users:
            user_dict = dict(user)
            print(f"   ID: {user_dict['id']} | User: {user_dict['username']} | Admin: {user_dict['is_admin']}")
        
        # Testar login
        print("\n🔐 Testando login...")
        
        # Teste com admin/admin123
        print("   Tentativa: admin / admin123")
        result = auth_manager.authenticate_user('admin', 'admin123')
        
        if result:
            print("   ✅ LOGIN SUCESSO!")
            print(f"      Nome: {result['full_name']}")
            print(f"      Email: {result['email']}")
            print(f"      Admin: {result['is_admin']}")
        else:
            print("   ❌ LOGIN FALHOU!")
            
            # Tentar recriar usuário
            print("   🔧 Recriando usuário admin...")
            
            # Deletar usuário existente
            db_manager.execute_query("DELETE FROM users WHERE username = 'admin'")
            
            # Criar novo
            success = auth_manager.create_user(
                username='admin',
                password='admin123',
                email='<EMAIL>',
                is_admin=True,
                full_name='Administrador do Sistema'
            )
            
            if success:
                print("      ✓ Usuário recriado")
                
                # Testar novamente
                result = auth_manager.authenticate_user('admin', 'admin123')
                if result:
                    print("      ✅ LOGIN AGORA FUNCIONA!")
                else:
                    print("      ❌ Ainda não funciona")
            else:
                print("      ❌ Erro ao recriar usuário")
        
        # Teste com senha errada
        print("\n   Teste com senha errada: admin / senha_errada")
        result = auth_manager.authenticate_user('admin', 'senha_errada')
        
        if result:
            print("   ❌ PROBLEMA: Aceitou senha errada!")
        else:
            print("   ✅ OK: Rejeitou senha errada")
        
        print("\n" + "=" * 50)
        print("TESTE CONCLUÍDO")
        print("=" * 50)
        
        if auth_manager.authenticate_user('admin', 'admin123'):
            print("✅ LOGIN ESTÁ FUNCIONANDO!")
            print("💡 Agora você pode executar a aplicação:")
            print("   py executar_terminal.py")
        else:
            print("❌ LOGIN AINDA NÃO FUNCIONA")
            print("💡 Verifique os erros acima")
        
    except Exception as e:
        print(f"\n❌ ERRO: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
