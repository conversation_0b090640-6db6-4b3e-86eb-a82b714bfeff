#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Teste específico para nova despesa
"""

import sys
import os
import tkinter as tk

# Adicionar o diretório src ao path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from database import DatabaseManager
from auth import AuthManager
from gui.main_window import MainWindow

def test_new_expense():
    """Testa especificamente a criação de nova despesa"""
    print("💸 TESTE ESPECÍFICO - NOVA DESPESA")
    print("=" * 50)
    
    try:
        # Inicializar banco de dados
        db_manager = DatabaseManager()
        db_manager.initialize_database()
        
        auth_manager = AuthManager(db_manager)
        
        # Fazer login
        user_data = auth_manager.authenticate_user('admin', 'admin123')
        if not user_data:
            print("❌ Falha no login!")
            return
        
        print(f"✅ Login realizado: {user_data['full_name']}")
        
        # Criar janela principal
        root = tk.Tk()
        root.title("Teste Nova Despesa")
        root.geometry("1200x800")
        
        # Função de logout mock
        def mock_logout():
            print("Logout chamado")
        
        # Criar MainWindow
        main_window = MainWindow(root, db_manager, auth_manager, user_data, mock_logout)
        
        print("✅ Janela principal criada com sucesso!")
        
        # Testar método new_expense
        print("\n🔧 Testando método new_expense...")
        try:
            # Mock para não aguardar fechamento da janela
            original_wait_window = root.wait_window
            root.wait_window = lambda window: None
            
            main_window.new_expense()
            print("✅ Método new_expense executado sem erros!")
            
            # Restaurar
            root.wait_window = original_wait_window
            
        except Exception as e:
            print(f"❌ Erro no método new_expense: {str(e)}")
            import traceback
            traceback.print_exc()
            root.wait_window = original_wait_window
        
        # Testar criação direta do formulário
        print("\n🔧 Testando criação direta do formulário...")
        try:
            from src.gui.transaction_form_new import TransactionFormNew
            
            form = TransactionFormNew(
                root, 
                db_manager, 
                user_data['id'], 
                'expense'
            )
            
            print("✅ Formulário criado diretamente!")
            print(f"📐 Geometria: {form.window.geometry()}")
            
            # Verificar se botões estão visíveis
            def check_visibility():
                try:
                    form.window.update_idletasks()
                    
                    # Obter dimensões da janela
                    window_height = form.window.winfo_height()
                    window_width = form.window.winfo_width()
                    
                    print(f"📏 Dimensões da janela: {window_width}x{window_height}")
                    
                    # Procurar botões
                    buttons = []
                    def find_buttons(widget):
                        if isinstance(widget, tk.Button):
                            button_info = {
                                'text': widget['text'],
                                'y': widget.winfo_y(),
                                'height': widget.winfo_height()
                            }
                            buttons.append(button_info)
                        for child in widget.winfo_children():
                            find_buttons(child)
                    
                    find_buttons(form.window)
                    
                    print(f"🔍 Botões encontrados: {len(buttons)}")
                    for btn in buttons:
                        print(f"   - {btn['text']}: Y={btn['y']}, H={btn['height']}")
                        if btn['y'] + btn['height'] > window_height:
                            print(f"     ⚠️  Botão pode estar fora da área visível!")
                        else:
                            print(f"     ✅ Botão está visível!")
                    
                except Exception as e:
                    print(f"❌ Erro ao verificar visibilidade: {str(e)}")
            
            # Aguardar renderização e verificar
            root.after(1000, check_visibility)
            
            # Fechar após teste
            root.after(3000, form.window.destroy)
            
        except Exception as e:
            print(f"❌ Erro ao criar formulário: {str(e)}")
            import traceback
            traceback.print_exc()
        
        print("\n✅ TESTE DE NOVA DESPESA CONCLUÍDO!")
        
        # Executar por tempo limitado
        root.after(4000, root.quit)
        root.mainloop()
        root.destroy()
        
    except Exception as e:
        print(f"❌ ERRO GERAL: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_new_expense()
