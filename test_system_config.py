#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Teste das configurações do sistema
"""

import sys
import os
import tkinter as tk

# Adicionar o diretório src ao path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from database import DatabaseManager
from auth import AuthManager
from gui.main_window import MainWindow

def test_system_config():
    """Testa as configurações do sistema"""
    print("⚙️ TESTE DAS CONFIGURAÇÕES DO SISTEMA")
    print("=" * 60)
    
    try:
        # Inicializar banco de dados
        db_manager = DatabaseManager()
        db_manager.initialize_database()
        
        auth_manager = AuthManager(db_manager)
        
        # Fazer login como admin
        user_data = auth_manager.authenticate_user('admin', 'admin123')
        if not user_data:
            print("❌ Falha no login!")
            return
        
        print(f"✅ Login realizado: {user_data['full_name']}")
        print(f"✅ Usuário é admin: {user_data['is_admin']}")
        
        # Criar janela principal
        root = tk.Tk()
        root.title("Teste Configurações Sistema")
        root.geometry("1200x800")
        
        # Função de logout mock
        def mock_logout():
            print("Logout chamado")
        
        # Criar MainWindow
        main_window = MainWindow(root, db_manager, auth_manager, user_data, mock_logout)
        
        print("✅ Janela principal criada com sucesso!")
        
        # Testar logs do sistema
        print("\n🔧 Testando logs do sistema...")
        try:
            main_window.show_system_logs()
            print("✅ Logs do sistema funcionou!")
        except Exception as e:
            print(f"❌ Erro nos logs do sistema: {str(e)}")
        
        # Testar estatísticas do sistema
        print("\n🔧 Testando estatísticas do sistema...")
        try:
            main_window.show_system_stats()
            print("✅ Estatísticas do sistema funcionou!")
        except Exception as e:
            print(f"❌ Erro nas estatísticas: {str(e)}")
        
        # Testar manutenção do banco
        print("\n🔧 Testando manutenção do banco...")
        try:
            main_window.show_database_maintenance()
            print("✅ Manutenção do banco funcionou!")
        except Exception as e:
            print(f"❌ Erro na manutenção do banco: {str(e)}")
        
        # Testar obtenção de estatísticas
        print("\n📊 Testando obtenção de estatísticas...")
        try:
            stats = main_window.get_system_statistics()
            print("✅ Estatísticas obtidas:")
            for key, value in stats.items():
                print(f"   {key}: {value}")
        except Exception as e:
            print(f"❌ Erro ao obter estatísticas: {str(e)}")
        
        # Testar carregamento de logs
        print("\n📋 Testando carregamento de logs...")
        try:
            # Criar widget de texto mock
            text_widget = tk.Text(root)
            main_window.load_system_logs(text_widget, "Todos")
            
            # Verificar se logs foram carregados
            content = text_widget.get(1.0, tk.END)
            if content.strip():
                print("✅ Logs carregados com sucesso!")
                lines = content.strip().split('\n')
                print(f"   {len(lines)} linhas de log encontradas")
            else:
                print("⚠️  Nenhum log encontrado")
                
        except Exception as e:
            print(f"❌ Erro ao carregar logs: {str(e)}")
        
        print("\n✅ TODOS OS TESTES CONCLUÍDOS!")
        print("💡 Se não houve erros, as configurações do sistema estão funcionando.")
        
        # Fechar janela após um tempo
        root.after(3000, root.destroy)  # Fechar após 3 segundos
        root.mainloop()
        
    except Exception as e:
        print(f"❌ ERRO GERAL: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_system_config()
