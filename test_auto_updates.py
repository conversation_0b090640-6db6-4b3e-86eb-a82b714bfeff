#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Teste do Sistema de Atualizações Automáticas
"""

import sys
import time
from pathlib import Path

# Adicionar src ao path
sys.path.append('src')

from database import DatabaseManager
from modules.auto_update_manager import AutoUpdateManager

def test_auto_updates():
    """Testa o sistema de atualizações automáticas"""
    print("=" * 60)
    print("    TESTE DO SISTEMA DE ATUALIZAÇÕES AUTOMÁTICAS")
    print("=" * 60)
    print()
    
    try:
        # Inicializar componentes
        print("🔄 Inicializando componentes...")
        db_manager = DatabaseManager("data/test_auto_updates.db")
        db_manager.initialize_database()
        print("   ✓ Banco de dados inicializado")
        
        # Criar manager de atualizações
        auto_update_manager = AutoUpdateManager(db_manager, "config/test_auto_update_config.json")
        print("   ✓ Manager de atualizações criado")
        
        # Mostrar configurações
        print("\n📋 Configurações atuais:")
        config = auto_update_manager.config
        print(f"   Sistema habilitado: {config['enabled']}")
        print(f"   Intervalo de verificação: {config['update_interval_seconds']} segundos")
        print(f"   Tarefas configuradas: {len(config['tasks'])}")
        
        for task_name, task_config in config['tasks'].items():
            status = "✓" if task_config['enabled'] else "✗"
            print(f"   {status} {task_name}: {task_config['interval_minutes']} min")
        
        # Testar execução individual de tarefas
        print("\n🧪 Testando execução individual de tarefas...")
        
        test_tasks = [
            'fuel_efficiency_update',
            'maintenance_reminders',
            'insurance_expiry_check',
            'financial_calculations'
        ]
        
        for task_name in test_tasks:
            print(f"\n   Executando: {task_name}")
            try:
                success = auto_update_manager.force_run_task(task_name)
                if success:
                    print(f"   ✓ {task_name} executada com sucesso")
                else:
                    print(f"   ✗ Falha ao executar {task_name}")
            except Exception as e:
                print(f"   ✗ Erro em {task_name}: {e}")
        
        # Testar sistema completo por alguns segundos
        print("\n🚀 Iniciando sistema de atualizações...")
        auto_update_manager.start()
        
        print("   Sistema rodando... (aguardando 10 segundos)")
        time.sleep(10)
        
        # Mostrar status
        print("\n📊 Status das tarefas:")
        status = auto_update_manager.get_task_status()
        
        print(f"   Sistema rodando: {status['system_running']}")
        print(f"   Sistema habilitado: {status['system_enabled']}")
        
        for task_name, task_info in status['tasks'].items():
            enabled_status = "✓" if task_info['enabled'] else "✗"
            last_run = task_info['last_run'] or "Nunca"
            if last_run != "Nunca":
                last_run = last_run.split('T')[1][:8]  # Apenas hora
            
            print(f"   {enabled_status} {task_name}: Última execução: {last_run}")
        
        # Parar sistema
        print("\n🛑 Parando sistema...")
        auto_update_manager.stop()
        print("   ✓ Sistema parado")
        
        print("\n✅ Teste concluído com sucesso!")
        
    except Exception as e:
        print(f"\n❌ Erro durante o teste: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    return True

def create_test_config():
    """Cria configuração de teste"""
    import json
    
    test_config = {
        "enabled": True,
        "update_interval_seconds": 5,  # Mais rápido para teste
        "tasks": {
            "fuel_efficiency_update": {
                "enabled": True,
                "interval_minutes": 1,  # 1 minuto para teste
                "description": "Atualiza cálculos de eficiência de combustível"
            },
            "maintenance_reminders": {
                "enabled": True,
                "interval_minutes": 2,  # 2 minutos para teste
                "description": "Verifica lembretes de manutenção"
            },
            "insurance_expiry_check": {
                "enabled": True,
                "interval_minutes": 3,  # 3 minutos para teste
                "description": "Verifica vencimento de seguros"
            },
            "financial_calculations": {
                "enabled": True,
                "interval_minutes": 1,  # 1 minuto para teste
                "description": "Atualiza cálculos financeiros"
            },
            "data_cleanup": {
                "enabled": False,  # Desabilitado para teste
                "interval_minutes": 60,
                "description": "Limpeza de dados antigos"
            }
        }
    }
    
    # Criar diretório config se não existir
    config_dir = Path("config")
    config_dir.mkdir(exist_ok=True)
    
    # Salvar configuração de teste
    with open("config/test_auto_update_config.json", 'w', encoding='utf-8') as f:
        json.dump(test_config, f, indent=2, ensure_ascii=False)
    
    print("✓ Configuração de teste criada")

def create_test_data():
    """Cria dados de teste para as atualizações"""
    print("\n📝 Criando dados de teste...")
    
    try:
        db_manager = DatabaseManager("data/test_auto_updates.db")
        
        # Criar usuário de teste
        from auth import AuthManager
        auth_manager = AuthManager(db_manager)
        
        if not auth_manager.user_exists('test_user'):
            auth_manager.create_user(
                username='test_user',
                password='test123',
                email='<EMAIL>',
                full_name='Usuário de Teste'
            )
            print("   ✓ Usuário de teste criado")
        
        # Criar veículo de teste
        vehicle_data = {
            'name': 'Carro de Teste',
            'brand': 'Toyota',
            'model': 'Corolla',
            'year': 2020,
            'license_plate': 'TEST-123',
            'fuel_type': 'gasoline',
            'mileage': 50000,
            'insurance_expiry': '2024-12-31'
        }
        
        vehicle_id = db_manager.add_vehicle(1, vehicle_data)
        print(f"   ✓ Veículo de teste criado (ID: {vehicle_id})")
        
        # Criar registros de combustível de teste
        fuel_records = [
            {
                'vehicle_id': vehicle_id,
                'fuel_date': '2024-01-15',
                'fuel_type': 'gasoline',
                'liters': 40.0,
                'price_per_liter': 5.50,
                'total_cost': 220.00,
                'mileage': 49500,
                'is_full_tank': True
            },
            {
                'vehicle_id': vehicle_id,
                'fuel_date': '2024-01-20',
                'fuel_type': 'gasoline',
                'liters': 45.0,
                'price_per_liter': 5.60,
                'total_cost': 252.00,
                'mileage': 50000,
                'is_full_tank': True
            }
        ]
        
        for fuel_data in fuel_records:
            db_manager.add_fuel_record(1, fuel_data)
        
        print(f"   ✓ {len(fuel_records)} registros de combustível criados")
        
        # Criar manutenção de teste
        maintenance_data = {
            'vehicle_id': vehicle_id,
            'maintenance_type': 'oil_change',
            'description': 'Troca de óleo',
            'service_date': '2024-01-10',
            'cost': 150.00,
            'next_service_date': '2024-07-10',
            'next_service_mileage': 55000,
            'is_completed': True
        }
        
        db_manager.add_maintenance_record(1, maintenance_data)
        print("   ✓ Registro de manutenção criado")
        
    except Exception as e:
        print(f"   ✗ Erro ao criar dados de teste: {e}")

def main():
    """Função principal"""
    print("Preparando teste...")
    
    # Criar configuração de teste
    create_test_config()
    
    # Criar dados de teste
    create_test_data()
    
    # Executar teste
    success = test_auto_updates()
    
    if success:
        print("\n🎉 Todos os testes passaram!")
        return 0
    else:
        print("\n💥 Alguns testes falharam!")
        return 1

if __name__ == "__main__":
    sys.exit(main())
