#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Script de inicialização do Sistema de Gestão de Contas
"""

import sys
import os
from pathlib import Path

def check_python_version():
    """Verifica se a versão do Python é compatível"""
    if sys.version_info < (3, 7):
        print("❌ ERRO: Python 3.7 ou superior é necessário")
        print(f"Versão atual: {sys.version}")
        print("Por favor, atualize o Python e tente novamente.")
        return False
    
    print(f"✅ Python {sys.version_info.major}.{sys.version_info.minor}.{sys.version_info.micro} - OK")
    return True

def check_dependencies():
    """Verifica dependências básicas"""
    required_modules = [
        ('tkinter', 'Interface gráfica'),
        ('sqlite3', 'Banco de dados'),
        ('datetime', 'Manipulação de datas'),
        ('pathlib', 'Manipulação de caminhos'),
        ('json', 'Manipulação de JSON'),
        ('shutil', 'Operações de arquivo'),
        ('zipfile', 'Compressão de arquivos')
    ]
    
    missing_modules = []
    
    for module, description in required_modules:
        try:
            __import__(module)
            print(f"✅ {module} - OK")
        except ImportError:
            print(f"❌ {module} - FALTANDO ({description})")
            missing_modules.append(module)
    
    if missing_modules:
        print(f"\n❌ ERRO: Módulos faltando: {', '.join(missing_modules)}")
        
        if 'tkinter' in missing_modules:
            print("\nPara instalar tkinter:")
            print("- Ubuntu/Debian: sudo apt-get install python3-tk")
            print("- CentOS/RHEL: sudo yum install tkinter")
            print("- Windows/macOS: Já incluído no Python padrão")
        
        return False
    
    return True

def check_optional_dependencies():
    """Verifica dependências opcionais"""
    optional_modules = [
        ('bcrypt', 'Criptografia avançada de senhas'),
        ('PIL', 'Manipulação de imagens (Pillow)'),
    ]
    
    print("\n📦 Verificando dependências opcionais:")
    
    for module, description in optional_modules:
        try:
            __import__(module)
            print(f"✅ {module} - Disponível ({description})")
        except ImportError:
            print(f"⚠️  {module} - Não instalado ({description})")
    
    print("\nNota: Dependências opcionais não são necessárias para o funcionamento básico.")

def setup_environment():
    """Configura o ambiente de execução"""
    print("\n🔧 Configurando ambiente...")
    
    # Adicionar src ao path
    src_path = Path(__file__).parent / 'src'
    if src_path.exists():
        sys.path.insert(0, str(src_path))
        print("✅ Caminho dos módulos configurado")
    else:
        print("❌ ERRO: Diretório 'src' não encontrado")
        return False
    
    # Criar diretórios necessários
    directories = ['data', 'backups', 'logs']
    
    for directory in directories:
        dir_path = Path(directory)
        dir_path.mkdir(exist_ok=True)
        print(f"✅ Diretório '{directory}' verificado")
    
    return True

def run_quick_test():
    """Executa teste rápido do sistema"""
    print("\n🧪 Executando teste rápido...")
    
    try:
        # Testar importação dos módulos principais
        from database import DatabaseManager
        from auth import AuthManager
        
        print("✅ Módulos principais importados com sucesso")
        
        # Testar criação do banco
        db_manager = DatabaseManager("data/test_startup.db")
        db_manager.initialize_database()
        
        print("✅ Banco de dados inicializado com sucesso")
        
        # Testar autenticação
        auth_manager = AuthManager(db_manager)
        
        if not auth_manager.user_exists('admin'):
            auth_manager.create_user(
                username='admin',
                password='admin123',
                email='<EMAIL>',
                is_admin=True,
                full_name='Administrador do Sistema'
            )
            print("✅ Usuário administrador criado")
        else:
            print("✅ Usuário administrador já existe")
        
        # Limpar arquivo de teste
        test_db = Path("data/test_startup.db")
        if test_db.exists():
            test_db.unlink()
        
        print("✅ Teste rápido concluído com sucesso")
        return True
        
    except Exception as e:
        print(f"❌ ERRO no teste: {str(e)}")
        return False

def show_welcome_message():
    """Mostra mensagem de boas-vindas"""
    print("\n" + "="*60)
    print("🏦 SISTEMA DE GESTÃO DE CONTAS v1.0.0")
    print("="*60)
    print("📋 Funcionalidades:")
    print("   • Gestão de carteiras e contas")
    print("   • Controle de receitas e despesas")
    print("   • Sistema de alertas e vencimentos")
    print("   • Relatórios e análises")
    print("   • Backup e restauração automática")
    print("   • Controle de usuários e permissões")
    print("\n🔐 Login padrão:")
    print("   Usuário: admin")
    print("   Senha: admin123")
    print("\n💡 Dica: Altere a senha após o primeiro acesso!")
    print("="*60)

def start_application():
    """Inicia a aplicação principal"""
    print("\n🚀 Iniciando aplicação...")
    
    try:
        # Importar e executar aplicação principal
        from main import GestaoContasApp
        
        app = GestaoContasApp()
        app.start_application()
        
    except KeyboardInterrupt:
        print("\n\n👋 Aplicação encerrada pelo usuário.")
    except Exception as e:
        print(f"\n❌ ERRO FATAL: {str(e)}")
        print("\n🔧 Soluções possíveis:")
        print("1. Verifique se todos os arquivos estão presentes")
        print("2. Execute 'python test_system.py' para diagnóstico")
        print("3. Delete a pasta 'data' para reiniciar o banco")
        print("4. Reinstale as dependências: pip install -r requirements.txt")

def main():
    """Função principal"""
    print("🔍 Verificando sistema...")
    
    # Verificações básicas
    if not check_python_version():
        input("\nPressione Enter para sair...")
        return
    
    if not check_dependencies():
        input("\nPressione Enter para sair...")
        return
    
    # Verificações opcionais
    check_optional_dependencies()
    
    # Configurar ambiente
    if not setup_environment():
        input("\nPressione Enter para sair...")
        return
    
    # Teste rápido
    if not run_quick_test():
        print("\n⚠️  AVISO: Teste rápido falhou, mas tentando iniciar mesmo assim...")
    
    # Mostrar boas-vindas
    show_welcome_message()
    
    # Perguntar se deseja continuar
    try:
        response = input("\n▶️  Pressione Enter para iniciar ou 'q' para sair: ").strip().lower()
        if response == 'q':
            print("👋 Até logo!")
            return
    except KeyboardInterrupt:
        print("\n👋 Até logo!")
        return
    
    # Iniciar aplicação
    start_application()

if __name__ == "__main__":
    main()
