#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Executar aplicação - Versão Final
"""

import tkinter as tk
from tkinter import messagebox, ttk
import sys
import os
import threading
import time

class AplicacaoLauncher:
    def __init__(self):
        self.root = None
        self.app_running = False
        
    def criar_janela_inicial(self):
        """Cria janela inicial de boas-vindas"""
        self.root = tk.Tk()
        self.root.title("Sistema de Gestão de Contas - Launcher")
        self.root.geometry("700x500")
        self.root.configure(bg='#f0f0f0')
        
        # Forçar janela aparecer na frente
        self.root.lift()
        self.root.attributes('-topmost', True)
        self.root.focus_force()
        
        # Centralizar
        self.centralizar_janela()
        
        # Criar interface
        self.criar_interface()
        
        # Remover topmost após 2 segundos
        self.root.after(2000, lambda: self.root.attributes('-topmost', False))
        
        return self.root
    
    def centralizar_janela(self):
        """Centraliza a janela na tela"""
        self.root.update_idletasks()
        x = (self.root.winfo_screenwidth() // 2) - (700 // 2)
        y = (self.root.winfo_screenheight() // 2) - (500 // 2)
        self.root.geometry(f"700x500+{x}+{y}")
    
    def criar_interface(self):
        """Cria a interface da janela inicial"""
        # Frame principal
        main_frame = tk.Frame(self.root, bg='#f0f0f0', padx=40, pady=30)
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # Título
        title_frame = tk.Frame(main_frame, bg='#f0f0f0')
        title_frame.pack(fill=tk.X, pady=(0, 30))
        
        tk.Label(title_frame, text="🚗", font=('Arial', 40), bg='#f0f0f0').pack()
        tk.Label(title_frame, text="Sistema de Gestão de Contas", 
                font=('Arial', 20, 'bold'), bg='#f0f0f0').pack()
        tk.Label(title_frame, text="Versão 2.0 - Com Gerenciamento de Veículos", 
                font=('Arial', 12), bg='#f0f0f0', fg='#666').pack()
        
        # Separador
        ttk.Separator(main_frame, orient='horizontal').pack(fill=tk.X, pady=20)
        
        # Funcionalidades
        func_frame = tk.Frame(main_frame, bg='#f0f0f0')
        func_frame.pack(fill=tk.X, pady=10)
        
        tk.Label(func_frame, text="✨ Funcionalidades Disponíveis:", 
                font=('Arial', 14, 'bold'), bg='#f0f0f0').pack(anchor='w')
        
        funcionalidades = [
            "💰 Controle financeiro completo",
            "🏦 Gerenciamento de carteiras e contas",
            "📊 Transações (receitas e despesas)",
            "📈 Relatórios financeiros detalhados",
            "🚗 NOVO: Cadastro de veículos",
            "🔧 NOVO: Controle de manutenção",
            "⛽ NOVO: Registro de combustível",
            "📊 NOVO: Estatísticas de consumo"
        ]
        
        for func in funcionalidades:
            tk.Label(func_frame, text=func, font=('Arial', 11), 
                    bg='#f0f0f0', anchor='w').pack(anchor='w', padx=20, pady=2)
        
        # Separador
        ttk.Separator(main_frame, orient='horizontal').pack(fill=tk.X, pady=20)
        
        # Credenciais
        cred_frame = tk.Frame(main_frame, bg='#e8f4fd', relief='solid', bd=1)
        cred_frame.pack(fill=tk.X, pady=10)
        
        tk.Label(cred_frame, text="🔑 Credenciais Padrão:", 
                font=('Arial', 12, 'bold'), bg='#e8f4fd').pack(pady=(10, 5))
        tk.Label(cred_frame, text="Usuário: admin", 
                font=('Arial', 11, 'bold'), bg='#e8f4fd').pack()
        tk.Label(cred_frame, text="Senha: admin123", 
                font=('Arial', 11, 'bold'), bg='#e8f4fd').pack(pady=(0, 10))
        
        # Botões
        btn_frame = tk.Frame(main_frame, bg='#f0f0f0')
        btn_frame.pack(fill=tk.X, pady=30)
        
        # Botão principal
        btn_executar = tk.Button(btn_frame, text="🚀 EXECUTAR APLICAÇÃO", 
                               command=self.executar_aplicacao,
                               font=('Arial', 14, 'bold'),
                               bg='#007ACC', fg='white',
                               padx=30, pady=15,
                               cursor='hand2')
        btn_executar.pack(pady=10)
        
        # Botão de teste
        btn_teste = tk.Button(btn_frame, text="🧪 Executar Teste das Tabelas", 
                            command=self.executar_teste,
                            font=('Arial', 10),
                            bg='#28a745', fg='white',
                            padx=20, pady=8,
                            cursor='hand2')
        btn_teste.pack(pady=5)
        
        # Status
        self.status_label = tk.Label(main_frame, text="Pronto para executar", 
                                   font=('Arial', 10), bg='#f0f0f0', fg='#666')
        self.status_label.pack(pady=10)
    
    def executar_aplicacao(self):
        """Executa a aplicação principal"""
        if self.app_running:
            messagebox.showwarning("Aviso", "A aplicação já está sendo executada!")
            return
        
        try:
            self.status_label.config(text="Iniciando aplicação...", fg='blue')
            self.root.update()
            
            # Fechar janela launcher
            self.root.destroy()
            
            # Executar aplicação principal
            self.executar_main()
            
        except Exception as e:
            messagebox.showerror("Erro", f"Erro ao executar aplicação:\n{str(e)}")
            import traceback
            traceback.print_exc()
    
    def executar_main(self):
        """Executa o main.py"""
        try:
            from main import GestaoContasApp
            
            app = GestaoContasApp()
            app.start_application()
            
        except Exception as e:
            # Criar nova janela para mostrar erro
            error_root = tk.Tk()
            error_root.title("Erro")
            error_root.geometry("500x300")
            
            frame = tk.Frame(error_root, padx=20, pady=20)
            frame.pack(fill=tk.BOTH, expand=True)
            
            tk.Label(frame, text="❌ Erro ao executar aplicação", 
                    font=('Arial', 14, 'bold'), fg='red').pack(pady=10)
            
            error_text = tk.Text(frame, height=10, width=60)
            error_text.pack(fill=tk.BOTH, expand=True, pady=10)
            error_text.insert('1.0', str(e))
            
            tk.Button(frame, text="Fechar", command=error_root.destroy).pack(pady=10)
            
            error_root.mainloop()
    
    def executar_teste(self):
        """Executa o teste das tabelas"""
        try:
            self.status_label.config(text="Executando teste...", fg='orange')
            self.root.update()
            
            import subprocess
            import sys
            
            # Executar teste em processo separado
            result = subprocess.run([sys.executable, "test_vehicle_tables.py"], 
                                  capture_output=True, text=True)
            
            # Mostrar resultado
            result_window = tk.Toplevel(self.root)
            result_window.title("Resultado do Teste")
            result_window.geometry("600x400")
            
            frame = tk.Frame(result_window, padx=20, pady=20)
            frame.pack(fill=tk.BOTH, expand=True)
            
            tk.Label(frame, text="📋 Resultado do Teste das Tabelas", 
                    font=('Arial', 14, 'bold')).pack(pady=10)
            
            text_widget = tk.Text(frame, height=20, width=70)
            text_widget.pack(fill=tk.BOTH, expand=True, pady=10)
            
            if result.returncode == 0:
                text_widget.insert('1.0', "✅ TESTE EXECUTADO COM SUCESSO!\n\n" + result.stdout)
            else:
                text_widget.insert('1.0', "❌ ERRO NO TESTE:\n\n" + result.stderr)
            
            tk.Button(frame, text="Fechar", command=result_window.destroy).pack(pady=10)
            
            self.status_label.config(text="Teste concluído", fg='green')
            
        except Exception as e:
            messagebox.showerror("Erro", f"Erro ao executar teste:\n{str(e)}")
            self.status_label.config(text="Erro no teste", fg='red')

def main():
    """Função principal"""
    print("=" * 60)
    print("    SISTEMA DE GESTÃO DE CONTAS - LAUNCHER")
    print("    Versão 2.0 - Com Gerenciamento de Veículos")
    print("=" * 60)
    print()
    print("Iniciando interface gráfica...")
    print("Uma janela deve aparecer na sua tela.")
    print()
    
    try:
        launcher = AplicacaoLauncher()
        root = launcher.criar_janela_inicial()
        
        print("✓ Janela criada com sucesso!")
        print("✓ Se não aparecer, verifique a barra de tarefas")
        print()
        
        root.mainloop()
        
    except Exception as e:
        print(f"✗ Erro ao criar interface: {e}")
        import traceback
        traceback.print_exc()
        
        # Tentar executar diretamente
        print("\nTentando executar aplicação diretamente...")
        try:
            from main import main as main_app
            main_app()
        except Exception as e2:
            print(f"✗ Erro ao executar diretamente: {e2}")

if __name__ == "__main__":
    main()
