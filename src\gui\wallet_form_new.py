#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Formulário de Carteira Completamente Novo e Melhorado
Criado do zero com design moderno e funcionalidades avançadas
"""

import tkinter as tk
from tkinter import ttk, messagebox
import locale

class WalletFormNew:
    def __init__(self, parent, db_manager, user_id, wallet_id=None):
        self.parent = parent
        self.db_manager = db_manager
        self.user_id = user_id
        self.wallet_id = wallet_id
        self.result = False
        
        # Configurações visuais
        self.config = {
            'title': '💳 Nova Carteira' if not wallet_id else '💳 Editar Carteira',
            'color': '#3498db',
            'button_text': '💳 <PERSON><PERSON><PERSON>' if not wallet_id else '💳 Salvar Carteira'
        }
        
        # Configurar locale para formatação de moeda
        try:
            locale.setlocale(locale.LC_ALL, 'pt_BR.UTF-8')
        except:
            pass
        
        # Criar janela principal
        self.create_main_window()
        
        # Criar interface
        self.create_interface()
        
        # Carregar dados se for edição
        if wallet_id:
            self.load_wallet_data()
        
        # Configurar eventos
        self.setup_events()
        
        # Focar no primeiro campo
        self.name_entry.focus()
    
    def create_main_window(self):
        """Cria a janela principal"""
        self.window = tk.Toplevel(self.parent)
        self.window.title(self.config['title'])
        self.window.geometry("750x650")
        self.window.resizable(False, False)
        self.window.configure(bg='#f8f9fa')

        # Centralizar janela
        self.center_window()

        # Configurar janela
        self.window.transient(self.parent)
        self.window.grab_set()
    
    def center_window(self):
        """Centraliza a janela na tela"""
        self.window.update_idletasks()
        width = self.window.winfo_width()
        height = self.window.winfo_height()
        x = (self.window.winfo_screenwidth() // 2) - (width // 2)
        y = (self.window.winfo_screenheight() // 2) - (height // 2)
        self.window.geometry(f'{width}x{height}+{x}+{y}')
    
    def create_interface(self):
        """Cria a interface completa"""
        # Container principal
        main_container = tk.Frame(self.window, bg='#f8f9fa')
        main_container.pack(fill=tk.BOTH, expand=True, padx=25, pady=25)
        
        # Cabeçalho
        self.create_header(main_container)
        
        # Área de conteúdo com scroll
        self.create_content_area(main_container)
        
        # Rodapé com botões
        self.create_footer(main_container)
    
    def create_header(self, parent):
        """Cria o cabeçalho"""
        header_frame = tk.Frame(parent, bg=self.config['color'], height=100)
        header_frame.pack(fill=tk.X, pady=(0, 25))
        header_frame.pack_propagate(False)
        
        # Título principal
        title_label = tk.Label(
            header_frame, 
            text=self.config['title'],
            font=("Arial", 22, "bold"),
            fg='white',
            bg=self.config['color']
        )
        title_label.pack(expand=True)
        
        # Subtítulo
        subtitle = "Configure uma nova carteira financeira" if not self.wallet_id else "Edite as configurações da carteira"
        subtitle_label = tk.Label(
            header_frame,
            text=subtitle,
            font=("Arial", 12),
            fg='white',
            bg=self.config['color']
        )
        subtitle_label.pack()
    
    def create_content_area(self, parent):
        """Cria a área de conteúdo com scroll"""
        # Frame para scroll
        canvas_frame = tk.Frame(parent, bg='white', relief=tk.RAISED, bd=2)
        canvas_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 25))
        
        # Canvas e scrollbar
        canvas = tk.Canvas(canvas_frame, bg='white', highlightthickness=0)
        scrollbar = ttk.Scrollbar(canvas_frame, orient="vertical", command=canvas.yview)
        self.scrollable_frame = tk.Frame(canvas, bg='white')
        
        # Configurar scroll
        self.scrollable_frame.bind(
            "<Configure>",
            lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
        )
        
        canvas.create_window((0, 0), window=self.scrollable_frame, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar.set)
        
        # Pack canvas e scrollbar
        canvas.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")
        
        # Criar seções do formulário
        self.create_basic_info_section()
        self.create_financial_section()
        self.create_configuration_section()
        self.create_help_section()
    
    def create_basic_info_section(self):
        """Cria seção de informações básicas"""
        section_frame = self.create_section_frame("💼 Informações Básicas", "#2c3e50")
        
        # Nome da carteira
        self.create_field(
            section_frame,
            "💼 Nome da Carteira *",
            "name",
            placeholder="Ex: Conta Corrente Banco do Brasil, Carteira Pessoal..."
        )
        
        # Linha com tipo e banco
        row_frame = tk.Frame(section_frame, bg='white')
        row_frame.pack(fill=tk.X, pady=(0, 20))
        
        # Tipo de carteira (60%)
        type_frame = tk.Frame(row_frame, bg='white')
        type_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=(0, 15))
        
        tk.Label(
            type_frame,
            text="🏦 Tipo de Carteira *",
            font=("Arial", 12, "bold"),
            bg='white',
            fg='#2c3e50'
        ).pack(anchor=tk.W, pady=(0, 8))
        
        self.wallet_type_combo = ttk.Combobox(
            type_frame,
            state="readonly",
            font=("Arial", 12),
            height=10
        )
        self.wallet_type_combo['values'] = [
            '🏦 Conta Corrente',
            '💰 Conta Poupança',
            '💳 Cartão de Crédito',
            '💳 Cartão de Débito',
            '💵 Dinheiro Físico',
            '📈 Investimentos',
            '🏪 Vale Alimentação',
            '🚗 Vale Transporte',
            '🎁 Vale Presente',
            '🔄 Conta Digital',
            '🌐 Carteira Digital',
            '📊 Outros'
        ]
        self.wallet_type_combo.current(0)
        self.wallet_type_combo.pack(fill=tk.X)
        
        # Banco/Instituição (40%)
        bank_frame = tk.Frame(row_frame, bg='white')
        bank_frame.pack(side=tk.LEFT, fill=tk.Y, padx=(15, 0))
        
        self.create_field_in_frame(
            bank_frame,
            "🏛️ Banco/Instituição",
            "bank",
            placeholder="Ex: Banco do Brasil, Nubank...",
            width=25
        )
    
    def create_financial_section(self):
        """Cria seção financeira"""
        section_frame = self.create_section_frame("💰 Informações Financeiras", "#27ae60")
        
        # Linha com saldo e limite
        financial_row = tk.Frame(section_frame, bg='white')
        financial_row.pack(fill=tk.X, pady=(0, 20))
        
        # Saldo inicial (50%)
        balance_frame = tk.Frame(financial_row, bg='white')
        balance_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=(0, 15))
        
        tk.Label(
            balance_frame,
            text="💰 Saldo Inicial (R$) *",
            font=("Arial", 12, "bold"),
            bg='white',
            fg='#2c3e50'
        ).pack(anchor=tk.W, pady=(0, 8))
        
        self.initial_balance_entry = tk.Entry(
            balance_frame,
            font=("Arial", 12),
            relief=tk.SOLID,
            bd=1
        )
        self.initial_balance_entry.pack(fill=tk.X)
        self.initial_balance_entry.insert(0, "0,00")
        self.initial_balance_entry.bind('<KeyRelease>', self.format_currency)
        
        # Limite (50%)
        limit_frame = tk.Frame(financial_row, bg='white')
        limit_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=(15, 0))
        
        tk.Label(
            limit_frame,
            text="🔒 Limite (R$)",
            font=("Arial", 12, "bold"),
            bg='white',
            fg='#2c3e50'
        ).pack(anchor=tk.W, pady=(0, 8))
        
        self.limit_entry = tk.Entry(
            limit_frame,
            font=("Arial", 12),
            relief=tk.SOLID,
            bd=1
        )
        self.limit_entry.pack(fill=tk.X)
        self.limit_entry.insert(0, "0,00")
        self.limit_entry.bind('<KeyRelease>', self.format_currency)
        
        # Informações sobre limites
        limit_info = tk.Label(
            section_frame,
            text="💡 Limite: Para cartões de crédito ou contas com limite especial",
            font=("Arial", 10),
            bg='white',
            fg='#7f8c8d'
        )
        limit_info.pack(anchor=tk.W, pady=(5, 0))
    
    def create_configuration_section(self):
        """Cria seção de configurações"""
        section_frame = self.create_section_frame("⚙️ Configurações e Observações", "#e74c3c")
        
        # Descrição
        tk.Label(
            section_frame,
            text="📝 Descrição",
            font=("Arial", 12, "bold"),
            bg='white',
            fg='#2c3e50'
        ).pack(anchor=tk.W, pady=(0, 8))
        
        self.description_text = tk.Text(
            section_frame,
            height=4,
            font=("Arial", 11),
            relief=tk.SOLID,
            bd=1,
            wrap=tk.WORD
        )
        self.description_text.pack(fill=tk.X, pady=(0, 20))
        
        # Placeholder para descrição
        self.description_text.insert('1.0', "Digite informações adicionais sobre esta carteira...")
        self.description_text.bind('<FocusIn>', self.clear_description_placeholder)
        self.description_text.bind('<FocusOut>', self.restore_description_placeholder)
        
        # Configurações
        config_row = tk.Frame(section_frame, bg='white')
        config_row.pack(fill=tk.X, pady=(0, 10))
        
        # Status ativo
        self.is_active_var = tk.BooleanVar(value=True)
        active_check = tk.Checkbutton(
            config_row,
            text="✅ Carteira ativa",
            variable=self.is_active_var,
            font=("Arial", 11, "bold"),
            bg='white',
            fg='#27ae60',
            selectcolor='white',
            activebackground='white',
            activeforeground='#27ae60'
        )
        active_check.pack(side=tk.LEFT, padx=(0, 30))
        
        # Incluir em relatórios
        self.include_reports_var = tk.BooleanVar(value=True)
        reports_check = tk.Checkbutton(
            config_row,
            text="📊 Incluir em relatórios",
            variable=self.include_reports_var,
            font=("Arial", 11),
            bg='white',
            fg='#2c3e50'
        )
        reports_check.pack(side=tk.LEFT, padx=(0, 30))
        
        # Carteira principal
        self.is_main_var = tk.BooleanVar(value=False)
        main_check = tk.Checkbutton(
            config_row,
            text="⭐ Carteira principal",
            variable=self.is_main_var,
            font=("Arial", 11),
            bg='white',
            fg='#f39c12'
        )
        main_check.pack(side=tk.LEFT)
    
    def create_help_section(self):
        """Cria seção de ajuda"""
        section_frame = self.create_section_frame("ℹ️ Informações e Dicas", "#9b59b6")
        
        help_text = """💡 Tipos de Carteira Explicados:

🏦 Conta Corrente: Para movimentações bancárias do dia a dia
💰 Conta Poupança: Para reservas de emergência e economias
💳 Cartão de Crédito: Para compras parceladas e controle de gastos
💳 Cartão de Débito: Vinculado à conta corrente ou poupança
💵 Dinheiro Físico: Para controle de dinheiro em espécie
📈 Investimentos: Para aplicações financeiras e investimentos
🏪 Vale Alimentação: Para benefícios de alimentação
🚗 Vale Transporte: Para benefícios de transporte
🎁 Vale Presente: Para cartões presente e gift cards
🔄 Conta Digital: Para bancos digitais (Nubank, Inter, etc.)
🌐 Carteira Digital: Para PIX, PayPal, PicPay, etc.
📊 Outros: Para tipos personalizados de carteira

🔧 Dicas de Configuração:
• Use nomes descritivos para facilitar a identificação
• Configure limites para cartões de crédito
• Marque como "principal" sua carteira mais usada
• Mantenha ativas apenas as carteiras em uso"""
        
        help_label = tk.Label(
            section_frame,
            text=help_text,
            font=("Arial", 10),
            bg='white',
            fg='#2c3e50',
            justify=tk.LEFT
        )
        help_label.pack(anchor=tk.W, fill=tk.X)
    
    def create_section_frame(self, title, color):
        """Cria um frame de seção"""
        section_frame = tk.LabelFrame(
            self.scrollable_frame,
            text=title,
            font=("Arial", 14, "bold"),
            bg='white',
            fg=color,
            relief=tk.GROOVE,
            bd=2,
            labelanchor='n'
        )
        section_frame.pack(fill=tk.X, padx=15, pady=(0, 15))
        section_frame.configure(padx=15, pady=15)
        
        return section_frame
    
    def create_field(self, parent, label_text, field_name, placeholder="", width=None):
        """Cria um campo de entrada"""
        # Label
        label = tk.Label(
            parent,
            text=label_text,
            font=("Arial", 12, "bold"),
            bg='white',
            fg='#2c3e50'
        )
        label.pack(anchor=tk.W, pady=(0, 8))
        
        # Campo
        field = tk.Entry(
            parent,
            font=("Arial", 12),
            relief=tk.SOLID,
            bd=1,
            width=width
        )
        field.pack(fill=tk.X, pady=(0, 20))
        
        if placeholder:
            field.insert(0, placeholder)
            field.bind('<FocusIn>', lambda e: self.clear_placeholder(e, placeholder))
            field.bind('<FocusOut>', lambda e: self.restore_placeholder(e, placeholder))
        
        setattr(self, f"{field_name}_entry", field)
        return field
    
    def create_field_in_frame(self, parent, label_text, field_name, placeholder="", width=None):
        """Cria um campo de entrada em um frame específico"""
        # Label
        label = tk.Label(
            parent,
            text=label_text,
            font=("Arial", 12, "bold"),
            bg='white',
            fg='#2c3e50'
        )
        label.pack(anchor=tk.W, pady=(0, 8))
        
        # Campo
        field = tk.Entry(
            parent,
            font=("Arial", 12),
            relief=tk.SOLID,
            bd=1,
            width=width
        )
        field.pack(fill=tk.X)
        
        if placeholder:
            field.insert(0, placeholder)
            field.bind('<FocusIn>', lambda e: self.clear_placeholder(e, placeholder))
            field.bind('<FocusOut>', lambda e: self.restore_placeholder(e, placeholder))
        
        setattr(self, f"{field_name}_entry", field)
        return field
    
    def create_footer(self, parent):
        """Cria o rodapé com botões"""
        footer_frame = tk.Frame(parent, bg='#ecf0f1', height=100)
        footer_frame.pack(fill=tk.X)
        footer_frame.pack_propagate(False)
        
        # Container dos botões
        button_container = tk.Frame(footer_frame, bg='#ecf0f1')
        button_container.pack(expand=True, pady=10)

        # Botão cancelar
        cancel_btn = tk.Button(
            button_container,
            text="❌ Cancelar",
            command=self.cancel,
            font=("Arial", 12, "bold"),
            bg='#e74c3c',
            fg='white',
            relief=tk.RAISED,
            bd=2,
            padx=30,
            pady=12,
            cursor='hand2'
        )
        cancel_btn.pack(side=tk.LEFT, padx=(0, 20))

        # Botão salvar
        save_btn = tk.Button(
            button_container,
            text=self.config['button_text'],
            command=self.save,
            font=("Arial", 12, "bold"),
            bg=self.config['color'],
            fg='white',
            relief=tk.RAISED,
            bd=2,
            padx=35,
            pady=12,
            cursor='hand2'
        )
        save_btn.pack(side=tk.LEFT)
        
        # Informações de ajuda
        help_label = tk.Label(
            footer_frame,
            text="💡 Dicas: Tab para navegar • Enter para salvar • Esc para cancelar • * = Obrigatório",
            font=("Arial", 11),
            bg='#ecf0f1',
            fg='#7f8c8d'
        )
        help_label.pack(side=tk.BOTTOM, pady=(0, 15))
    
    def format_currency(self, event):
        """Formata campos de moeda"""
        widget = event.widget
        value = widget.get().replace(',', '').replace('.', '')
        
        # Remove caracteres não numéricos
        value = ''.join(filter(str.isdigit, value))
        
        if value:
            # Converte para float e formata
            try:
                float_value = float(value) / 100
                formatted = f"{float_value:,.2f}".replace(',', 'X').replace('.', ',').replace('X', '.')
                
                # Atualiza o campo
                widget.delete(0, tk.END)
                widget.insert(0, formatted)
            except:
                pass
    
    def clear_placeholder(self, event, placeholder):
        """Remove placeholder ao focar"""
        if event.widget.get() == placeholder:
            event.widget.delete(0, tk.END)
    
    def restore_placeholder(self, event, placeholder):
        """Restaura placeholder se vazio"""
        if not event.widget.get():
            event.widget.insert(0, placeholder)
    
    def clear_description_placeholder(self, event):
        """Remove placeholder da descrição"""
        if self.description_text.get('1.0', tk.END).strip() == "Digite informações adicionais sobre esta carteira...":
            self.description_text.delete('1.0', tk.END)
    
    def restore_description_placeholder(self, event):
        """Restaura placeholder da descrição"""
        if not self.description_text.get('1.0', tk.END).strip():
            self.description_text.insert('1.0', "Digite informações adicionais sobre esta carteira...")
    
    def load_wallet_data(self):
        """Carrega dados da carteira para edição"""
        # Implementar carregamento de dados
        pass
    
    def setup_events(self):
        """Configura eventos"""
        self.window.bind('<Return>', lambda e: self.save())
        self.window.bind('<Escape>', lambda e: self.cancel())
    
    def save(self):
        """Salva a carteira"""
        try:
            if self.validate_form():
                messagebox.showinfo("Sucesso", "Carteira salva com sucesso!")
                self.result = True
                self.window.destroy()
        except Exception as e:
            messagebox.showerror("Erro", str(e))
    
    def validate_form(self):
        """Valida o formulário"""
        # Implementar validações
        return True
    
    def cancel(self):
        """Cancela a operação"""
        self.result = False
        self.window.destroy()
