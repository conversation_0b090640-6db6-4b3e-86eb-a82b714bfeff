#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Sistema de Gestão de Contas - Versão Simplificada
Aplicação desktop para controle financeiro pessoal
"""

import tkinter as tk
from tkinter import ttk, messagebox
import sqlite3
import os
import sys
from datetime import datetime, date
import hashlib
from pathlib import Path

class SimpleGestaoContas:
    def __init__(self):
        self.db_path = "data/gestao_contas.db"
        self.ensure_data_directory()
        self.initialize_database()
        self.current_user = None
        
    def ensure_data_directory(self):
        """Garante que o diretório de dados existe"""
        Path("data").mkdir(exist_ok=True)
        Path("backups").mkdir(exist_ok=True)
    
    def get_connection(self):
        """Retorna conexão com o banco"""
        conn = sqlite3.connect(self.db_path)
        conn.row_factory = sqlite3.Row
        return conn
    
    def initialize_database(self):
        """Inicializa o banco de dados"""
        conn = self.get_connection()
        cursor = conn.cursor()
        
        try:
            # Tabela de usuários
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS users (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    username TEXT UNIQUE NOT NULL,
                    password_hash TEXT NOT NULL,
                    email TEXT UNIQUE NOT NULL,
                    full_name TEXT NOT NULL,
                    is_admin BOOLEAN DEFAULT FALSE,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            ''')
            
            # Tabela de carteiras
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS wallets (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    user_id INTEGER NOT NULL,
                    name TEXT NOT NULL,
                    current_balance DECIMAL(10,2) DEFAULT 0.00,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (user_id) REFERENCES users (id)
                )
            ''')
            
            # Tabela de transações
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS transactions (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    user_id INTEGER NOT NULL,
                    wallet_id INTEGER NOT NULL,
                    description TEXT NOT NULL,
                    amount DECIMAL(10,2) NOT NULL,
                    transaction_type TEXT NOT NULL,
                    transaction_date DATE NOT NULL,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (user_id) REFERENCES users (id),
                    FOREIGN KEY (wallet_id) REFERENCES wallets (id)
                )
            ''')
            
            conn.commit()
            
            # Criar usuário admin se não existir
            cursor.execute("SELECT COUNT(*) FROM users WHERE username = 'admin'")
            if cursor.fetchone()[0] == 0:
                password_hash = hashlib.sha256('admin123'.encode()).hexdigest()
                cursor.execute('''
                    INSERT INTO users (username, password_hash, email, full_name, is_admin)
                    VALUES ('admin', ?, '<EMAIL>', 'Administrador', TRUE)
                ''', (password_hash,))
                conn.commit()
                
        except Exception as e:
            print(f"Erro ao inicializar banco: {e}")
        finally:
            conn.close()
    
    def authenticate_user(self, username, password):
        """Autentica usuário"""
        conn = self.get_connection()
        cursor = conn.cursor()
        
        password_hash = hashlib.sha256(password.encode()).hexdigest()
        cursor.execute('''
            SELECT id, username, full_name, is_admin
            FROM users 
            WHERE username = ? AND password_hash = ?
        ''', (username, password_hash))
        
        result = cursor.fetchone()
        conn.close()
        
        if result:
            return dict(result)
        return None
    
    def show_login(self):
        """Mostra tela de login"""
        login_window = tk.Tk()
        login_window.title("Sistema de Gestão de Contas - Login")
        login_window.geometry("800x300")
        login_window.resizable(False, False)
        
        # Centralizar janela
        login_window.update_idletasks()
        x = (login_window.winfo_screenwidth() // 2) - (400 // 2)
        y = (login_window.winfo_screenheight() // 2) - (300 // 2)
        login_window.geometry(f'800x300+{x}+{y}')
        
        # Frame principal
        main_frame = ttk.Frame(login_window, padding="30")
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # Título
        title_label = ttk.Label(main_frame, text="Sistema de Gestão de Contas", 
                               font=("Arial", 16, "bold"))
        title_label.pack(pady=(0, 30))
        
        # Campos
        ttk.Label(main_frame, text="Usuário:").pack(anchor=tk.W, pady=(0, 5))
        username_entry = ttk.Entry(main_frame, font=("Arial", 11))
        username_entry.pack(fill=tk.X, pady=(0, 15))
        username_entry.insert(0, "admin")
        
        ttk.Label(main_frame, text="Senha:").pack(anchor=tk.W, pady=(0, 5))
        password_entry = ttk.Entry(main_frame, show="*", font=("Arial", 11))
        password_entry.pack(fill=tk.X, pady=(0, 20))
        password_entry.insert(0, "admin123")
        
        def do_login():
            username = username_entry.get().strip()
            password = password_entry.get()
            
            if not username or not password:
                messagebox.showerror("Erro", "Preencha todos os campos")
                return
            
            user_data = self.authenticate_user(username, password)
            if user_data:
                self.current_user = user_data
                login_window.destroy()
                self.show_main_window()
            else:
                messagebox.showerror("Erro", "Usuário ou senha incorretos")
                password_entry.delete(0, tk.END)
        
        # Botão login
        login_btn = ttk.Button(main_frame, text="Entrar", command=do_login)
        login_btn.pack(fill=tk.X, pady=(0, 10))
        
        # Info
        info_label = ttk.Label(main_frame, 
                              text="Login padrão:\nUsuário: admin\nSenha: admin123",
                              font=("Arial", 9), foreground="gray")
        info_label.pack(pady=(20, 0))
        
        # Bind Enter
        login_window.bind('<Return>', lambda e: do_login())
        username_entry.focus()
        
        login_window.mainloop()
    
    def show_main_window(self):
        """Mostra janela principal"""
        if not self.current_user:
            return
            
        main_window = tk.Tk()
        main_window.title(f"Sistema de Gestão de Contas - {self.current_user['full_name']}")
        main_window.geometry("1000x700")
        main_window.state('zoomed')
        
        # Menu
        menubar = tk.Menu(main_window)
        main_window.config(menu=menubar)
        
        file_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="Arquivo", menu=file_menu)
        file_menu.add_command(label="Sair", command=main_window.quit)
        
        # Frame principal
        main_frame = ttk.Frame(main_window)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # Título
        title_label = ttk.Label(main_frame, text="Dashboard Financeiro", 
                               font=("Arial", 18, "bold"))
        title_label.pack(pady=(0, 20))
        
        # Notebook
        notebook = ttk.Notebook(main_frame)
        notebook.pack(fill=tk.BOTH, expand=True)
        
        # Aba Dashboard
        dashboard_frame = ttk.Frame(notebook)
        notebook.add(dashboard_frame, text="Dashboard")
        
        # Cards de resumo
        cards_frame = ttk.Frame(dashboard_frame)
        cards_frame.pack(fill=tk.X, padx=20, pady=20)
        
        # Card saldo
        saldo_card = ttk.LabelFrame(cards_frame, text="Saldo Total", padding=10)
        saldo_card.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=(0, 10))
        
        saldo_label = ttk.Label(saldo_card, text="R$ 0,00", 
                               font=('Arial', 16, 'bold'), foreground='blue')
        saldo_label.pack()
        
        # Aba Carteiras
        wallets_frame = ttk.Frame(notebook)
        notebook.add(wallets_frame, text="Carteiras")
        
        # Toolbar carteiras
        wallet_toolbar = ttk.Frame(wallets_frame)
        wallet_toolbar.pack(fill=tk.X, padx=10, pady=10)
        
        ttk.Button(wallet_toolbar, text="Nova Carteira", 
                  command=lambda: self.show_wallet_dialog(main_window)).pack(side=tk.LEFT)
        
        # Lista carteiras
        wallet_tree = ttk.Treeview(wallets_frame, columns=('Nome', 'Saldo'), show='headings')
        wallet_tree.heading('Nome', text='Nome')
        wallet_tree.heading('Saldo', text='Saldo')
        wallet_tree.pack(fill=tk.BOTH, expand=True, padx=10, pady=(0, 10))
        
        # Aba Transações
        trans_frame = ttk.Frame(notebook)
        notebook.add(trans_frame, text="Transações")
        
        # Toolbar transações
        trans_toolbar = ttk.Frame(trans_frame)
        trans_toolbar.pack(fill=tk.X, padx=10, pady=10)
        
        ttk.Button(trans_toolbar, text="Nova Receita", 
                  command=lambda: self.show_transaction_dialog(main_window, "income")).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(trans_toolbar, text="Nova Despesa", 
                  command=lambda: self.show_transaction_dialog(main_window, "expense")).pack(side=tk.LEFT)
        
        # Lista transações
        trans_tree = ttk.Treeview(trans_frame, columns=('Data', 'Descrição', 'Valor', 'Tipo'), show='headings')
        trans_tree.heading('Data', text='Data')
        trans_tree.heading('Descrição', text='Descrição')
        trans_tree.heading('Valor', text='Valor')
        trans_tree.heading('Tipo', text='Tipo')
        trans_tree.pack(fill=tk.BOTH, expand=True, padx=10, pady=(0, 10))
        
        # Carregar dados
        self.load_wallets(wallet_tree)
        self.load_transactions(trans_tree)
        self.update_balance(saldo_label)
        
        # Atualizar dados a cada 5 segundos
        def refresh_data():
            self.load_wallets(wallet_tree)
            self.load_transactions(trans_tree)
            self.update_balance(saldo_label)
            main_window.after(5000, refresh_data)
        
        main_window.after(1000, refresh_data)
        
        main_window.mainloop()
    
    def show_wallet_dialog(self, parent):
        """Mostra diálogo para criar carteira"""
        dialog = tk.Toplevel(parent)
        dialog.title("Nova Carteira")
        dialog.geometry("400x200")
        dialog.transient(parent)
        dialog.grab_set()
        
        # Centralizar
        dialog.update_idletasks()
        x = parent.winfo_x() + (parent.winfo_width() // 2) - (400 // 2)
        y = parent.winfo_y() + (parent.winfo_height() // 2) - (200 // 2)
        dialog.geometry(f'400x200+{x}+{y}')
        
        frame = ttk.Frame(dialog, padding="20")
        frame.pack(fill=tk.BOTH, expand=True)
        
        ttk.Label(frame, text="Nome da Carteira:").pack(anchor=tk.W, pady=(0, 5))
        name_entry = ttk.Entry(frame, font=("Arial", 11))
        name_entry.pack(fill=tk.X, pady=(0, 15))
        
        ttk.Label(frame, text="Saldo Inicial:").pack(anchor=tk.W, pady=(0, 5))
        balance_entry = ttk.Entry(frame, font=("Arial", 11))
        balance_entry.pack(fill=tk.X, pady=(0, 20))
        balance_entry.insert(0, "0.00")
        
        def save_wallet():
            name = name_entry.get().strip()
            try:
                balance = float(balance_entry.get().replace(',', '.'))
            except:
                balance = 0.0
            
            if not name:
                messagebox.showerror("Erro", "Nome é obrigatório")
                return
            
            conn = self.get_connection()
            cursor = conn.cursor()
            cursor.execute('''
                INSERT INTO wallets (user_id, name, current_balance)
                VALUES (?, ?, ?)
            ''', (self.current_user['id'], name, balance))
            conn.commit()
            conn.close()
            
            messagebox.showinfo("Sucesso", "Carteira criada com sucesso!")
            dialog.destroy()
        
        button_frame = ttk.Frame(frame)
        button_frame.pack(fill=tk.X, pady=(10, 0))
        
        ttk.Button(button_frame, text="Cancelar", command=dialog.destroy).pack(side=tk.LEFT)
        ttk.Button(button_frame, text="Salvar", command=save_wallet).pack(side=tk.RIGHT)
        
        name_entry.focus()
    
    def show_transaction_dialog(self, parent, trans_type):
        """Mostra diálogo para criar transação"""
        dialog = tk.Toplevel(parent)
        dialog.title(f"Nova {'Receita' if trans_type == 'income' else 'Despesa'}")
        dialog.geometry("400x300")
        dialog.transient(parent)
        dialog.grab_set()
        
        # Centralizar
        dialog.update_idletasks()
        x = parent.winfo_x() + (parent.winfo_width() // 2) - (400 // 2)
        y = parent.winfo_y() + (parent.winfo_height() // 2) - (300 // 2)
        dialog.geometry(f'400x300+{x}+{y}')
        
        frame = ttk.Frame(dialog, padding="20")
        frame.pack(fill=tk.BOTH, expand=True)
        
        # Carteira
        ttk.Label(frame, text="Carteira:").pack(anchor=tk.W, pady=(0, 5))
        wallet_combo = ttk.Combobox(frame, state="readonly")
        wallet_combo.pack(fill=tk.X, pady=(0, 15))
        
        # Carregar carteiras
        conn = self.get_connection()
        cursor = conn.cursor()
        cursor.execute("SELECT id, name FROM wallets WHERE user_id = ?", (self.current_user['id'],))
        wallets = cursor.fetchall()
        conn.close()
        
        wallet_combo['values'] = [f"{w['name']}" for w in wallets]
        if wallets:
            wallet_combo.current(0)
        
        # Descrição
        ttk.Label(frame, text="Descrição:").pack(anchor=tk.W, pady=(0, 5))
        desc_entry = ttk.Entry(frame, font=("Arial", 11))
        desc_entry.pack(fill=tk.X, pady=(0, 15))
        
        # Valor
        ttk.Label(frame, text="Valor:").pack(anchor=tk.W, pady=(0, 5))
        value_entry = ttk.Entry(frame, font=("Arial", 11))
        value_entry.pack(fill=tk.X, pady=(0, 20))
        
        def save_transaction():
            if not wallets:
                messagebox.showerror("Erro", "Crie uma carteira primeiro")
                return
                
            wallet_id = wallets[wallet_combo.current()]['id']
            description = desc_entry.get().strip()
            
            try:
                amount = float(value_entry.get().replace(',', '.'))
            except:
                messagebox.showerror("Erro", "Valor inválido")
                return
            
            if not description or amount <= 0:
                messagebox.showerror("Erro", "Preencha todos os campos corretamente")
                return
            
            conn = self.get_connection()
            cursor = conn.cursor()
            
            # Inserir transação
            cursor.execute('''
                INSERT INTO transactions (user_id, wallet_id, description, amount, transaction_type, transaction_date)
                VALUES (?, ?, ?, ?, ?, ?)
            ''', (self.current_user['id'], wallet_id, description, amount, trans_type, date.today()))
            
            # Atualizar saldo da carteira
            if trans_type == 'income':
                cursor.execute("UPDATE wallets SET current_balance = current_balance + ? WHERE id = ?", (amount, wallet_id))
            else:
                cursor.execute("UPDATE wallets SET current_balance = current_balance - ? WHERE id = ?", (amount, wallet_id))
            
            conn.commit()
            conn.close()
            
            messagebox.showinfo("Sucesso", "Transação criada com sucesso!")
            dialog.destroy()
        
        button_frame = ttk.Frame(frame)
        button_frame.pack(fill=tk.X, pady=(10, 0))
        
        ttk.Button(button_frame, text="Cancelar", command=dialog.destroy).pack(side=tk.LEFT)
        ttk.Button(button_frame, text="Salvar", command=save_transaction).pack(side=tk.RIGHT)
        
        desc_entry.focus()
    
    def load_wallets(self, tree):
        """Carrega carteiras na árvore"""
        for item in tree.get_children():
            tree.delete(item)
        
        conn = self.get_connection()
        cursor = conn.cursor()
        cursor.execute("SELECT name, current_balance FROM wallets WHERE user_id = ?", (self.current_user['id'],))
        
        for wallet in cursor.fetchall():
            tree.insert('', 'end', values=(wallet['name'], f"R$ {wallet['current_balance']:,.2f}"))
        
        conn.close()
    
    def load_transactions(self, tree):
        """Carrega transações na árvore"""
        for item in tree.get_children():
            tree.delete(item)
        
        conn = self.get_connection()
        cursor = conn.cursor()
        cursor.execute('''
            SELECT transaction_date, description, amount, transaction_type
            FROM transactions 
            WHERE user_id = ?
            ORDER BY transaction_date DESC, id DESC
            LIMIT 50
        ''', (self.current_user['id'],))
        
        for trans in cursor.fetchall():
            trans_type = "Receita" if trans['transaction_type'] == 'income' else "Despesa"
            tree.insert('', 'end', values=(
                trans['transaction_date'],
                trans['description'],
                f"R$ {trans['amount']:,.2f}",
                trans_type
            ))
        
        conn.close()
    
    def update_balance(self, label):
        """Atualiza saldo total"""
        conn = self.get_connection()
        cursor = conn.cursor()
        cursor.execute("SELECT SUM(current_balance) FROM wallets WHERE user_id = ?", (self.current_user['id'],))
        
        result = cursor.fetchone()
        total = result[0] if result[0] else 0
        
        label.config(text=f"R$ {total:,.2f}")
        conn.close()
    
    def run(self):
        """Executa a aplicação"""
        try:
            self.show_login()
        except Exception as e:
            messagebox.showerror("Erro Fatal", f"Erro ao iniciar aplicação: {str(e)}")

def main():
    """Função principal"""
    try:
        app = SimpleGestaoContas()
        app.run()
    except Exception as e:
        print(f"Erro fatal: {str(e)}")
        input("Pressione Enter para sair...")

if __name__ == "__main__":
    main()
