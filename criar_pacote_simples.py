#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Cria pacote simples do executável
"""

import shutil
import zipfile
from pathlib import Path
import time

def create_simple_package():
    """Cria pacote simples"""
    try:
        print("Criando pacote simples...")
        
        # Aguardar um pouco para liberar arquivos
        time.sleep(2)
        
        # Criar diretório
        dist_dir = Path("GestaoContasCompletoFinal_v1.0.0")
        if dist_dir.exists():
            shutil.rmtree(dist_dir)
        
        dist_dir.mkdir()
        
        # Copiar executável
        exe_source = Path("dist/GestaoContasCompletoFinal.exe")
        if exe_source.exists():
            shutil.copy2(exe_source, dist_dir / "GestaoContasCompletoFinal.exe")
            print("✓ Executável copiado")
        
        # Criar diretórios
        (dist_dir / "data").mkdir()
        (dist_dir / "backups").mkdir()
        
        # Copiar script de atalho
        atalho_source = Path("criar_atalho_desktop.py")
        if atalho_source.exists():
            shutil.copy2(atalho_source, dist_dir / "Criar Atalho na Área de Trabalho.py")
        
        # Criar .bat
        bat_content = '''@echo off
echo Criando atalho na área de trabalho...
python "Criar Atalho na Área de Trabalho.py"
pause'''
        
        with open(dist_dir / "🔗 Criar Atalho na Área de Trabalho.bat", 'w', encoding='utf-8') as f:
            f.write(bat_content)
        
        # Criar instruções
        instructions = """🏦 SISTEMA DE GESTÃO FINANCEIRA - VERSÃO COMPLETA FINAL
═══════════════════════════════════════════════════════════════

🎉 VERSÃO DEFINITIVA COM TODAS AS FUNCIONALIDADES!
✨ NOVO: Botões de Editar Funcionando!
💳 NOVO: Sistema de Parcelas Automáticas!
🔗 NOVO: Criador de Atalho na Área de Trabalho!

═══════════════════════════════════════════════════════════════

🚀 COMO USAR:

1. Execute: GestaoContasCompletoFinal.exe
2. Login: admin / admin123
3. Explore todas as funcionalidades!

🔗 CRIAR ATALHO:

1. Execute: "🔗 Criar Atalho na Área de Trabalho.bat"
2. Atalho será criado na área de trabalho

═══════════════════════════════════════════════════════════════

✅ CORREÇÕES MAIS RECENTES:

🔧 BOTÕES DE EDITAR FUNCIONANDO
• ✏️ Editar Carteiras - Totalmente funcional
• ✏️ Editar Transações - Totalmente funcional
• 📝 Formulários melhorados e completos
• 💾 Salvamento de edições funcionando

💳 SISTEMA DE PARCELAS AUTOMÁTICAS
• Campo "Número de Parcelas" (1 a 60)
• Geração automática de parcelas mensais
• Vencimentos automáticos mês a mês
• Controle individual de cada parcela

🔗 ATALHO NA ÁREA DE TRABALHO
• Script automático para criar atalho
• Acesso rápido ao sistema
• Ícone na área de trabalho

═══════════════════════════════════════════════════════════════

📊 TODAS AS FUNCIONALIDADES:

• 📊 Dashboard inteligente com cards coloridos
• 💳 Gestão completa de carteiras (criar/editar/excluir)
• 💸 Transações avançadas com parcelas e vencimentos
• 👥 Sistema completo de usuários
• 🛡️ Administração avançada
• 📊 5 tipos de relatórios funcionais
• ⚙️ Backup e restauração
• 🎨 Interface moderna e profissional

═══════════════════════════════════════════════════════════════

🎯 COMO TESTAR AS EDIÇÕES:

💳 EDITAR CARTEIRAS:
1. Vá para aba "Carteiras"
2. Selecione uma carteira
3. Clique "✏️ Editar"
4. Modifique os dados e salve

💸 EDITAR TRANSAÇÕES:
1. Vá para aba "Transações"
2. Selecione uma transação
3. Clique "✏️ Editar"
4. Modifique os dados e salve

💳 CRIAR PARCELAS:
1. Crie nova transação
2. Defina "Número de Parcelas" > 1
3. Sistema gera parcelas automaticamente

═══════════════════════════════════════════════════════════════

🎉 SISTEMA COMPLETO E FUNCIONAL!
TODAS AS FUNCIONALIDADES + EDIÇÕES + PARCELAS + ATALHO!

© 2024 - Sistema de Gestão Financeira Completo Final
Desenvolvido com ❤️ em Python"""
        
        with open(dist_dir / "🎉 LEIA-ME PRIMEIRO - VERSÃO COMPLETA FINAL.txt", 'w', encoding='utf-8') as f:
            f.write(instructions)
        
        print("✓ Pacote criado com sucesso!")
        return True
        
    except Exception as e:
        print(f"✗ Erro: {str(e)}")
        return False

def main():
    """Função principal"""
    print("CRIADOR DE PACOTE SIMPLES")
    print("=" * 40)
    
    if create_simple_package():
        print("\n🎉 Pacote criado com sucesso!")
        print("📁 Pasta: GestaoContasCompletoFinal_v1.0.0/")
    else:
        print("\n❌ Falha ao criar pacote")
    
    input("\nPressione Enter para sair...")

if __name__ == "__main__":
    main()
