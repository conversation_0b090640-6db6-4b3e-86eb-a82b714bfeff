#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Teste simples para criação de transações
"""

import sys
import os

# Adicionar o diretório src ao path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from database import DatabaseManager
from modules.transaction_manager import TransactionManager

def test_transaction_creation():
    """Testa criação de transação"""
    print("🔍 TESTE DE CRIAÇÃO DE TRANSAÇÃO")
    print("=" * 50)
    
    try:
        # Inicializar banco de dados
        db_manager = DatabaseManager()
        db_manager.initialize_database()
        
        # Criar transaction manager
        transaction_manager = TransactionManager(db_manager)
        
        # Verificar se existem dados necessários
        print("📊 Verificando dados necessários...")
        
        # Verificar usuários
        users = db_manager.execute_query("SELECT id, username FROM users")
        print(f"   Usuários: {len(users)}")
        if not users:
            print("   ❌ Nenhum usuário encontrado!")
            return
        
        # Usar o usuário admin (ID 1)
        admin_user = next((u for u in users if u['username'] == 'admin'), users[0])
        user_id = admin_user['id']
        print(f"   ✅ Usando usuário: {admin_user['username']} (ID: {user_id})")
        
        # Verificar carteiras
        wallets = db_manager.execute_query("SELECT id, name FROM wallets WHERE user_id = ?", (user_id,))
        print(f"   Carteiras: {len(wallets)}")
        if not wallets:
            print("   ❌ Nenhuma carteira encontrada!")
            return
        
        wallet_id = wallets[0]['id']
        print(f"   ✅ Usando carteira: {wallets[0]['name']} (ID: {wallet_id})")
        
        # Verificar categorias
        categories = db_manager.execute_query("SELECT id, name FROM categories WHERE category_type = 'income' AND is_active = TRUE")
        print(f"   Categorias de receita: {len(categories)}")
        if not categories:
            print("   ❌ Nenhuma categoria de receita encontrada!")
            return
        
        category_id = categories[0]['id']
        print(f"   ✅ Usando categoria: {categories[0]['name']} (ID: {category_id})")
        
        # Contar transações antes
        count_before = db_manager.execute_query("SELECT COUNT(*) FROM transactions")[0][0]
        print(f"\n📈 Transações antes: {count_before}")
        
        # Criar transação de teste
        print("\n💰 Criando transação de teste...")
        result = transaction_manager.create_transaction(
            user_id=user_id,
            wallet_id=wallet_id,
            category_id=category_id,
            transaction_type='income',
            amount=1000.00,
            description='Teste de receita - Criado automaticamente',
            is_paid=True
        )
        
        if result:
            print("   ✅ Transação criada com sucesso!")
        else:
            print("   ❌ Falha ao criar transação!")
            return
        
        # Contar transações depois
        count_after = db_manager.execute_query("SELECT COUNT(*) FROM transactions")[0][0]
        print(f"📈 Transações depois: {count_after}")
        
        if count_after > count_before:
            print("✅ TESTE PASSOU! Transação foi criada no banco de dados.")
            
            # Mostrar a transação criada
            last_transaction = db_manager.execute_query("""
                SELECT t.*, w.name as wallet_name, c.name as category_name 
                FROM transactions t
                JOIN wallets w ON t.wallet_id = w.id
                JOIN categories c ON t.category_id = c.id
                ORDER BY t.id DESC LIMIT 1
            """)[0]
            
            print(f"\n📋 Detalhes da transação criada:")
            print(f"   ID: {last_transaction['id']}")
            print(f"   Descrição: {last_transaction['description']}")
            print(f"   Valor: R$ {last_transaction['amount']:,.2f}")
            print(f"   Tipo: {last_transaction['transaction_type']}")
            print(f"   Carteira: {last_transaction['wallet_name']}")
            print(f"   Categoria: {last_transaction['category_name']}")
            print(f"   Pago: {'Sim' if last_transaction['is_paid'] else 'Não'}")
            print(f"   Data: {last_transaction['transaction_date']}")
            
        else:
            print("❌ TESTE FALHOU! Transação não foi criada no banco de dados.")
        
    except Exception as e:
        print(f"❌ ERRO NO TESTE: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_transaction_creation()
