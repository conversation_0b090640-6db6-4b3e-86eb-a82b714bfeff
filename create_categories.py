#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Script para criar categorias padrão
"""

import sys
import os

# Adicionar o diretório src ao path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from database import DatabaseManager

def create_categories():
    """Cria categorias padrão"""
    print("📂 CRIANDO CATEGORIAS PADRÃO")
    print("=" * 50)
    
    try:
        db_manager = DatabaseManager()
        conn = db_manager.get_connection()
        cursor = conn.cursor()
        
        # Verificar se já existem categorias
        cursor.execute("SELECT COUNT(*) FROM categories")
        existing_count = cursor.fetchone()[0]
        print(f"Categorias existentes: {existing_count}")
        
        if existing_count > 0:
            print("✅ Categorias já existem")
            conn.close()
            return
        
        # Criar categorias manualmente
        categories = [
            # Receitas
            (None, '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON> mensal', 'income', '#28a745', True),
            (None, 'Freelance', 'Trabalhos extras', 'income', '#17a2b8', True),
            (None, 'Investimentos', 'Rendimentos de investimentos', 'income', '#ffc107', True),
            (None, 'Outros', 'Outras receitas', 'income', '#6c757d', True),
            
            # Despesas
            (None, 'Alimentação', 'Gastos com comida', 'expense', '#dc3545', True),
            (None, 'Transporte', 'Gastos com transporte', 'expense', '#fd7e14', True),
            (None, 'Moradia', 'Aluguel, condomínio, etc.', 'expense', '#6f42c1', True),
            (None, 'Saúde', 'Gastos médicos', 'expense', '#e83e8c', True),
            (None, 'Educação', 'Cursos, livros, etc.', 'expense', '#20c997', True),
            (None, 'Lazer', 'Entretenimento', 'expense', '#0dcaf0', True),
            (None, 'Outros', 'Outras despesas', 'expense', '#6c757d', True)
        ]
        
        for user_id, name, description, category_type, color, is_active in categories:
            cursor.execute('''
                INSERT INTO categories (user_id, name, description, category_type, color, is_active)
                VALUES (?, ?, ?, ?, ?, ?)
            ''', (user_id, name, description, category_type, color, is_active))
            print(f"✅ Categoria criada: {name} ({category_type})")
        
        conn.commit()
        conn.close()
        
        print(f"\n✅ {len(categories)} categorias criadas com sucesso!")
        
    except Exception as e:
        print(f"❌ ERRO: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    create_categories()
