#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Módulo de gerenciamento de transações (receitas e despesas)
"""

from datetime import datetime, date
import sqlite3

class TransactionManager:
    def __init__(self, db_manager):
        self.db_manager = db_manager
    
    def create_transaction(self, user_id, wallet_id, category_id, transaction_type,
                         amount, description, transaction_date=None, due_date=None,
                         is_paid=False, is_recurring=False, recurring_type=None,
                         recurring_day=None, notes="", installments=1, installment_number=1):
        """Cria uma nova transação"""
        try:
            # Validações
            if transaction_type not in ['income', 'expense']:
                raise ValueError("Tipo de transação deve ser 'income' ou 'expense'")
            
            if amount <= 0:
                raise ValueError("Valor deve ser maior que zero")
            
            if not description or len(description.strip()) == 0:
                raise ValueError("Descrição é obrigatória")
            
            if len(description) > 200:
                raise ValueError("Descrição deve ter no máximo 200 caracteres")
            
            # Verificar se carteira existe e pertence ao usuário
            if not self.wallet_exists(user_id, wallet_id):
                raise ValueError("Carteira não encontrada")
            
            # Verificar se categoria existe
            if not self.category_exists(user_id, category_id, transaction_type):
                raise ValueError("Categoria não encontrada ou incompatível com o tipo de transação")
            
            # Data padrão é hoje
            if transaction_date is None:
                transaction_date = date.today()
            elif isinstance(transaction_date, str):
                transaction_date = datetime.strptime(transaction_date, '%Y-%m-%d').date()
            
            # Validar data de vencimento
            if due_date and isinstance(due_date, str):
                due_date = datetime.strptime(due_date, '%Y-%m-%d').date()
            
            # Validar recorrência
            if is_recurring:
                if not recurring_type or recurring_type not in ['daily', 'weekly', 'monthly', 'yearly']:
                    raise ValueError("Tipo de recorrência inválido")
                
                if recurring_type in ['monthly', 'yearly'] and not recurring_day:
                    raise ValueError("Dia da recorrência é obrigatório para recorrência mensal/anual")
            
            # Inserir transação
            query = '''
                INSERT INTO transactions (
                    user_id, wallet_id, category_id, transaction_type, amount,
                    description, transaction_date, due_date, is_paid, is_recurring,
                    recurring_type, recurring_day, notes, installments, installment_number
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            '''

            self.db_manager.execute_query(query, (
                user_id, wallet_id, category_id, transaction_type, amount,
                description.strip(), transaction_date, due_date, is_paid,
                is_recurring, recurring_type, recurring_day, notes.strip(),
                installments, installment_number
            ))
            
            # Atualizar saldo da carteira se a transação estiver paga
            if is_paid:
                self.update_wallet_balance(wallet_id, amount, transaction_type)
            
            return True

        except Exception as e:
            raise Exception(f"Erro ao criar transação: {str(e)}")

    def create_installment_transactions(self, user_id, wallet_id, category_id, transaction_type,
                                      total_amount, description, transaction_date=None,
                                      due_date=None, is_paid=False, notes="", installments=1):
        """Cria transações parceladas automaticamente"""
        try:
            if installments <= 1:
                # Se não há parcelamento, criar transação única
                return self.create_transaction(
                    user_id, wallet_id, category_id, transaction_type,
                    total_amount, description, transaction_date, due_date,
                    is_paid, False, None, None, notes, installments, 1
                )

            # Calcular valor de cada parcela
            installment_amount = total_amount / installments

            # Data base para as parcelas
            from datetime import datetime, timedelta
            if transaction_date:
                base_date = datetime.strptime(transaction_date, '%Y-%m-%d')
            else:
                base_date = datetime.now()

            if due_date:
                base_due_date = datetime.strptime(due_date, '%Y-%m-%d')
            else:
                base_due_date = base_date

            created_transactions = []

            # Criar cada parcela
            for i in range(installments):
                # Calcular data da parcela (mensal)
                installment_date = base_date + timedelta(days=30 * i)
                installment_due_date = base_due_date + timedelta(days=30 * i)

                # Descrição da parcela
                installment_description = f"{description} - Parcela {i+1}/{installments}"

                # Primeira parcela pode estar paga, outras pendentes
                installment_paid = is_paid if i == 0 else False

                # Criar transação da parcela
                success = self.create_transaction(
                    user_id, wallet_id, category_id, transaction_type,
                    installment_amount, installment_description,
                    installment_date.strftime('%Y-%m-%d'),
                    installment_due_date.strftime('%Y-%m-%d'),
                    installment_paid, False, None, None, notes,
                    installments, i + 1
                )

                if success:
                    created_transactions.append(i + 1)
                else:
                    print(f"Erro ao criar parcela {i+1}")

            print(f"Criadas {len(created_transactions)} parcelas de {installments}")
            return len(created_transactions) == installments

        except Exception as e:
            print(f"Erro ao criar transações parceladas: {str(e)}")
            return False

    def wallet_exists(self, user_id, wallet_id):
        """Verifica se carteira existe e pertence ao usuário"""
        query = "SELECT COUNT(*) FROM wallets WHERE id = ? AND user_id = ?"
        result = self.db_manager.execute_query(query, (wallet_id, user_id))
        return result[0][0] > 0
    
    def category_exists(self, user_id, category_id, transaction_type):
        """Verifica se categoria existe e é compatível"""
        query = '''
            SELECT COUNT(*) FROM categories 
            WHERE id = ? AND (user_id = ? OR user_id IS NULL) 
            AND category_type = ? AND is_active = TRUE
        '''
        result = self.db_manager.execute_query(query, (category_id, user_id, transaction_type))
        return result[0][0] > 0
    
    def update_wallet_balance(self, wallet_id, amount, transaction_type):
        """Atualiza saldo da carteira"""
        if transaction_type == 'income':
            query = "UPDATE wallets SET current_balance = current_balance + ? WHERE id = ?"
        else:  # expense
            query = "UPDATE wallets SET current_balance = current_balance - ? WHERE id = ?"
        
        self.db_manager.execute_query(query, (amount, wallet_id))
    
    def get_user_transactions(self, user_id, limit=None, offset=0, filters=None):
        """Retorna transações do usuário com filtros opcionais"""
        try:
            base_query = '''
                SELECT t.id, t.transaction_date, t.description, t.amount, t.transaction_type,
                       t.is_paid, t.due_date, t.notes, t.created_at,
                       c.name as category_name, c.color as category_color,
                       w.name as wallet_name
                FROM transactions t
                JOIN categories c ON t.category_id = c.id
                JOIN wallets w ON t.wallet_id = w.id
                WHERE t.user_id = ?
            '''
            
            params = [user_id]
            
            # Aplicar filtros
            if filters:
                if filters.get('transaction_type'):
                    base_query += " AND t.transaction_type = ?"
                    params.append(filters['transaction_type'])
                
                if filters.get('wallet_id'):
                    base_query += " AND t.wallet_id = ?"
                    params.append(filters['wallet_id'])
                
                if filters.get('category_id'):
                    base_query += " AND t.category_id = ?"
                    params.append(filters['category_id'])
                
                if filters.get('is_paid') is not None:
                    base_query += " AND t.is_paid = ?"
                    params.append(filters['is_paid'])
                
                if filters.get('date_from'):
                    base_query += " AND t.transaction_date >= ?"
                    params.append(filters['date_from'])
                
                if filters.get('date_to'):
                    base_query += " AND t.transaction_date <= ?"
                    params.append(filters['date_to'])
                
                if filters.get('search'):
                    base_query += " AND (t.description LIKE ? OR t.notes LIKE ?)"
                    search_term = f"%{filters['search']}%"
                    params.extend([search_term, search_term])
            
            base_query += " ORDER BY t.transaction_date DESC, t.created_at DESC"
            
            if limit:
                base_query += " LIMIT ? OFFSET ?"
                params.extend([limit, offset])
            
            return self.db_manager.execute_query(base_query, params)
            
        except Exception as e:
            raise Exception(f"Erro ao buscar transações: {str(e)}")
    
    def get_transaction_by_id(self, transaction_id, user_id):
        """Retorna transação específica"""
        query = '''
            SELECT t.*, c.name as category_name, w.name as wallet_name
            FROM transactions t
            JOIN categories c ON t.category_id = c.id
            JOIN wallets w ON t.wallet_id = w.id
            WHERE t.id = ? AND t.user_id = ?
        '''
        
        result = self.db_manager.execute_query(query, (transaction_id, user_id))
        return result[0] if result else None
    
    def update_transaction(self, transaction_id, user_id, **kwargs):
        """Atualiza transação"""
        try:
            # Verificar se transação existe
            old_transaction = self.get_transaction_by_id(transaction_id, user_id)
            if not old_transaction:
                raise ValueError("Transação não encontrada")
            
            updates = []
            params = []
            
            # Campos que podem ser atualizados
            updatable_fields = [
                'wallet_id', 'category_id', 'amount', 'description',
                'transaction_date', 'due_date', 'is_paid', 'notes', 'installments'
            ]
            
            for field in updatable_fields:
                if field in kwargs:
                    value = kwargs[field]
                    
                    # Validações específicas
                    if field == 'amount' and value <= 0:
                        raise ValueError("Valor deve ser maior que zero")
                    
                    if field == 'description' and (not value or len(value.strip()) == 0):
                        raise ValueError("Descrição é obrigatória")
                    
                    if field == 'wallet_id' and not self.wallet_exists(user_id, value):
                        raise ValueError("Carteira não encontrada")
                    
                    if field == 'category_id' and not self.category_exists(user_id, value, old_transaction['transaction_type']):
                        raise ValueError("Categoria não encontrada ou incompatível")
                    
                    updates.append(f"{field} = ?")
                    params.append(value)
            
            if not updates:
                return True  # Nada para atualizar
            
            # Atualizar transação
            updates.append("updated_at = CURRENT_TIMESTAMP")
            query = f"UPDATE transactions SET {', '.join(updates)} WHERE id = ? AND user_id = ?"
            params.extend([transaction_id, user_id])
            
            self.db_manager.execute_query(query, params)
            
            # Atualizar saldo da carteira se necessário
            if 'is_paid' in kwargs or 'amount' in kwargs:
                self.recalculate_wallet_balance(old_transaction['wallet_id'])
            
            return True
            
        except Exception as e:
            raise Exception(f"Erro ao atualizar transação: {str(e)}")
    
    def delete_transaction(self, transaction_id, user_id):
        """Exclui transação"""
        try:
            transaction = self.get_transaction_by_id(transaction_id, user_id)
            if not transaction:
                raise ValueError("Transação não encontrada")
            
            # Excluir transação
            query = "DELETE FROM transactions WHERE id = ? AND user_id = ?"
            self.db_manager.execute_query(query, (transaction_id, user_id))
            
            # Recalcular saldo da carteira
            self.recalculate_wallet_balance(transaction['wallet_id'])
            
            return True
            
        except Exception as e:
            raise Exception(f"Erro ao excluir transação: {str(e)}")
    
    def recalculate_wallet_balance(self, wallet_id):
        """Recalcula saldo da carteira baseado nas transações pagas"""
        try:
            # Obter saldo inicial da carteira
            query = "SELECT initial_balance FROM wallets WHERE id = ?"
            result = self.db_manager.execute_query(query, (wallet_id,))
            
            if not result:
                return
            
            initial_balance = result[0]['initial_balance']
            
            # Calcular total de receitas pagas
            query = '''
                SELECT COALESCE(SUM(amount), 0) FROM transactions 
                WHERE wallet_id = ? AND transaction_type = 'income' AND is_paid = TRUE
            '''
            result = self.db_manager.execute_query(query, (wallet_id,))
            total_income = result[0][0]
            
            # Calcular total de despesas pagas
            query = '''
                SELECT COALESCE(SUM(amount), 0) FROM transactions 
                WHERE wallet_id = ? AND transaction_type = 'expense' AND is_paid = TRUE
            '''
            result = self.db_manager.execute_query(query, (wallet_id,))
            total_expense = result[0][0]
            
            # Atualizar saldo atual
            current_balance = initial_balance + total_income - total_expense
            query = "UPDATE wallets SET current_balance = ? WHERE id = ?"
            self.db_manager.execute_query(query, (current_balance, wallet_id))
            
        except Exception as e:
            raise Exception(f"Erro ao recalcular saldo: {str(e)}")

    def recalculate_all_wallet_balances(self, user_id):
        """Recalcula saldos de todas as carteiras do usuário"""
        try:
            # Buscar todas as carteiras do usuário
            query = "SELECT id FROM wallets WHERE user_id = ?"
            wallets = self.db_manager.execute_query(query, (user_id,))

            for wallet in wallets:
                self.recalculate_wallet_balance(wallet['id'])

            return True

        except Exception as e:
            raise Exception(f"Erro ao recalcular saldos das carteiras: {str(e)}")

    def mark_as_paid(self, transaction_id, user_id):
        """Marca transação como paga"""
        try:
            transaction = self.get_transaction_by_id(transaction_id, user_id)
            if not transaction:
                raise ValueError("Transação não encontrada")
            
            if transaction['is_paid']:
                return True  # Já está paga
            
            # Marcar como paga
            query = "UPDATE transactions SET is_paid = TRUE WHERE id = ? AND user_id = ?"
            self.db_manager.execute_query(query, (transaction_id, user_id))
            
            # Atualizar saldo da carteira
            self.update_wallet_balance(
                transaction['wallet_id'], 
                transaction['amount'], 
                transaction['transaction_type']
            )
            
            return True

        except Exception as e:
            raise Exception(f"Erro ao marcar como paga: {str(e)}")

    def mark_as_unpaid(self, transaction_id, user_id):
        """Marca transação como não paga"""
        try:
            transaction = self.get_transaction_by_id(transaction_id, user_id)
            if not transaction:
                raise ValueError("Transação não encontrada")

            if not transaction['is_paid']:
                return True  # Já está não paga

            # Marcar como não paga
            query = "UPDATE transactions SET is_paid = FALSE WHERE id = ? AND user_id = ?"
            self.db_manager.execute_query(query, (transaction_id, user_id))

            # Reverter saldo da carteira
            if transaction['transaction_type'] == 'income':
                # Se era receita paga, remover do saldo
                query = "UPDATE wallets SET current_balance = current_balance - ? WHERE id = ?"
            else:
                # Se era despesa paga, adicionar de volta ao saldo
                query = "UPDATE wallets SET current_balance = current_balance + ? WHERE id = ?"

            self.db_manager.execute_query(query, (transaction['amount'], transaction['wallet_id']))

            return True

        except Exception as e:
            raise Exception(f"Erro ao marcar como não paga: {str(e)}")

    def get_transactions_summary(self, user_id, period_start=None, period_end=None):
        """Retorna resumo das transações"""
        try:
            base_query = '''
                SELECT 
                    transaction_type,
                    COUNT(*) as count,
                    COALESCE(SUM(amount), 0) as total,
                    COALESCE(SUM(CASE WHEN is_paid = TRUE THEN amount ELSE 0 END), 0) as paid_total
                FROM transactions
                WHERE user_id = ?
            '''
            
            params = [user_id]
            
            if period_start:
                base_query += " AND transaction_date >= ?"
                params.append(period_start)
            
            if period_end:
                base_query += " AND transaction_date <= ?"
                params.append(period_end)
            
            base_query += " GROUP BY transaction_type"
            
            results = self.db_manager.execute_query(base_query, params)
            
            summary = {
                'income': {'count': 0, 'total': 0, 'paid_total': 0},
                'expense': {'count': 0, 'total': 0, 'paid_total': 0}
            }
            
            for row in results:
                summary[row['transaction_type']] = {
                    'count': row['count'],
                    'total': row['total'],
                    'paid_total': row['paid_total']
                }
            
            # Calcular balanço
            summary['balance'] = summary['income']['paid_total'] - summary['expense']['paid_total']
            summary['pending_income'] = summary['income']['total'] - summary['income']['paid_total']
            summary['pending_expense'] = summary['expense']['total'] - summary['expense']['paid_total']
            
            return summary
            
        except Exception as e:
            raise Exception(f"Erro ao obter resumo: {str(e)}")
