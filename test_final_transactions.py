#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Teste final das funcionalidades de editar e excluir transações
"""

import sys
import os
import tkinter as tk

# Adicionar o diretório src ao path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from database import DatabaseManager
from auth import AuthManager
from gui.main_window import MainWindow

def test_final_transactions():
    """Teste final das transações"""
    print("🎯 TESTE FINAL - EDITAR E EXCLUIR TRANSAÇÕES")
    print("=" * 60)
    
    try:
        # Inicializar banco de dados
        db_manager = DatabaseManager()
        db_manager.initialize_database()
        
        auth_manager = AuthManager(db_manager)
        
        # Fazer login
        user_data = auth_manager.authenticate_user('admin', 'admin123')
        if not user_data:
            print("❌ Falha no login!")
            return
        
        print(f"✅ Login realizado: {user_data['full_name']}")
        
        # Criar janela principal
        root = tk.Tk()
        root.title("Teste Final Transações")
        root.geometry("800x600")
        
        # Função de logout mock
        def mock_logout():
            print("Logout chamado")
        
        # Criar MainWindow
        main_window = MainWindow(root, db_manager, auth_manager, user_data, mock_logout)
        
        print("✅ Janela principal criada com sucesso!")
        
        # Carregar transações
        main_window.load_transactions()
        children = main_window.transactions_tree.get_children()
        print(f"✅ {len(children)} transações carregadas")
        
        if children:
            # Selecionar primeira transação
            main_window.transactions_tree.selection_set(children[0])
            
            # Testar edição
            print("\n🔧 Testando edição...")
            try:
                # Mock para evitar abrir janela real
                original_wait_window = root.wait_window
                root.wait_window = lambda window: None
                
                main_window.edit_transaction()
                print("✅ Edição funcionou sem erros!")
                
                # Restaurar
                root.wait_window = original_wait_window
                
            except Exception as e:
                print(f"❌ Erro na edição: {str(e)}")
                root.wait_window = original_wait_window
            
            # Testar exclusão
            print("\n🔧 Testando exclusão...")
            try:
                # Mock para cancelar confirmação
                import tkinter.messagebox
                original_askyesno = tkinter.messagebox.askyesno
                tkinter.messagebox.askyesno = lambda title, message: False
                
                main_window.delete_transaction()
                print("✅ Exclusão funcionou sem erros!")
                
                # Restaurar
                tkinter.messagebox.askyesno = original_askyesno
                
            except Exception as e:
                print(f"❌ Erro na exclusão: {str(e)}")
                tkinter.messagebox.askyesno = original_askyesno
        
        else:
            print("⚠️  Nenhuma transação disponível para teste")
        
        print("\n✅ TESTE FINAL CONCLUÍDO COM SUCESSO!")
        print("🎉 As funcionalidades de editar e excluir estão funcionando!")
        
        # Fechar janela
        root.destroy()
        
    except Exception as e:
        print(f"❌ ERRO: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_final_transactions()
