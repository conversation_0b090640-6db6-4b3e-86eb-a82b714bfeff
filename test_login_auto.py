#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Teste de login automático para verificar o botão
"""

import sys
import tkinter as tk
sys.path.append('src')

from database import DatabaseManager
from auth import AuthManager
from modules.auto_update_manager import AutoUpdateManager
from gui.main_window import <PERSON>Window

def test_auto_login():
    print("Testando login automático...")
    
    # Inicializar componentes (similar ao executar_terminal.py)
    db_manager = DatabaseManager()
    db_manager.initialize_database()
    
    auth_manager = AuthManager(db_manager)
    
    # Criar usuário admin se não existir
    if not auth_manager.user_exists('admin'):
        auth_manager.create_user(
            username='admin',
            password='admin123',
            email='<EMAIL>',
            is_admin=True,
            full_name='Administrador do Sistema'
        )
    
    # Inicializar sistema de atualizações automáticas
    auto_update_manager = None
    try:
        auto_update_manager = AutoUpdateManager(db_manager)
        print("✓ Sistema de atualizações automáticas inicializado")
    except Exception as e:
        print(f"⚠️ Erro ao inicializar atualizações automáticas: {e}")
    
    # Criar janela principal
    root = tk.Tk()
    root.title("Sistema de Gestão de Contas")
    root.geometry("800x800")
    
    # Simular login bem-sucedido
    user_data = {
        'id': 1,
        'username': 'admin',
        'full_name': 'Administrador do Sistema',
        'email': '<EMAIL>',
        'is_admin': True
    }
    
    print(f"Debug: auto_update_manager é None? {auto_update_manager is None}")
    
    # Iniciar sistema de atualizações automáticas
    if auto_update_manager:
        try:
            auto_update_manager.start()
            print("✓ Sistema de atualizações automáticas iniciado")
        except Exception as e:
            print(f"⚠️ Erro ao iniciar atualizações automáticas: {e}")
    else:
        print("⚠️ auto_update_manager não está disponível")
    
    def on_logout():
        if auto_update_manager:
            try:
                auto_update_manager.stop()
                print("✓ Sistema de atualizações automáticas parado")
            except Exception as e:
                print(f"⚠️ Erro ao parar atualizações automáticas: {e}")
        root.quit()
    
    # Criar interface principal
    print("Criando MainWindow com auto_update_manager...")
    main_window = MainWindow(root, db_manager, auth_manager, user_data, on_logout, auto_update_manager)
    
    print("MainWindow criado!")
    print(f"main_window.auto_update_manager é None? {main_window.auto_update_manager is None}")
    
    # Adicionar botão de teste para abrir configurações
    test_frame = tk.Frame(root)
    test_frame.pack(side=tk.TOP, fill=tk.X, padx=10, pady=5)
    
    def test_config():
        try:
            main_window.show_auto_update_config()
        except Exception as e:
            print(f"Erro ao abrir configurações: {e}")
            import traceback
            traceback.print_exc()
    
    tk.Button(test_frame, text="🧪 TESTE: Abrir Configurações Auto-Update", 
             command=test_config, bg='yellow').pack(side=tk.LEFT)
    
    # Mostrar janela
    root.mainloop()

if __name__ == "__main__":
    test_auto_login()
