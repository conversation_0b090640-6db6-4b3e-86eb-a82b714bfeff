#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Teste do parcelamento automático de transações
"""

import sys
import os
from datetime import datetime, timedelta

# Adicionar o diretório src ao path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from database import DatabaseManager
from auth import AuthManager
from modules.transaction_manager import TransactionManager

def test_parcelamento_automatico():
    """Testa o parcelamento automático de transações"""
    print("💳 TESTE DO PARCELAMENTO AUTOMÁTICO")
    print("=" * 60)
    
    try:
        # Inicializar banco de dados
        db_manager = DatabaseManager()
        db_manager.initialize_database()
        
        auth_manager = AuthManager(db_manager)
        transaction_manager = TransactionManager(db_manager)
        
        # Fazer login
        user_data = auth_manager.authenticate_user('admin', 'admin123')
        if not user_data:
            print("❌ Falha no login!")
            return
        
        print(f"✅ Login realizado: {user_data['full_name']}")
        user_id = user_data['id']
        
        # Obter carteira e categoria para teste
        wallets = db_manager.execute_query("SELECT * FROM wallets WHERE user_id = ? LIMIT 1", (user_id,))
        if not wallets:
            print("❌ Nenhuma carteira encontrada!")
            return
        
        wallet_id = wallets[0]['id']
        print(f"✅ Usando carteira: {wallets[0]['name']} (ID: {wallet_id})")
        
        categories = db_manager.execute_query("SELECT * FROM categories WHERE user_id = ? AND category_type = 'expense' LIMIT 1", (user_id,))
        if not categories:
            # Criar categoria de teste
            print("⚠️  Nenhuma categoria de despesa encontrada. Criando categoria de teste...")
            db_manager.execute_query("""
                INSERT INTO categories (user_id, name, category_type, color, description)
                VALUES (?, 'Teste', 'expense', '#e74c3c', 'Categoria de teste para parcelamento')
            """, (user_id,))

            categories = db_manager.execute_query("SELECT * FROM categories WHERE user_id = ? AND category_type = 'expense' LIMIT 1", (user_id,))
            if not categories:
                print("❌ Falha ao criar categoria de teste!")
                return

        category_id = categories[0]['id']
        print(f"✅ Usando categoria: {categories[0]['name']} (ID: {category_id})")
        
        # Contar transações antes
        count_before = db_manager.execute_query("SELECT COUNT(*) FROM transactions WHERE user_id = ?", (user_id,))[0][0]
        print(f"\n📊 Transações antes: {count_before}")
        
        # Teste 1: Transação à vista (1 parcela)
        print("\n🔧 TESTE 1: Transação à vista (1 parcela)")
        success = transaction_manager.create_installment_transactions(
            user_id=user_id,
            wallet_id=wallet_id,
            category_id=category_id,
            transaction_type='expense',
            total_amount=100.00,
            description='Compra à vista',
            installments=1
        )
        
        if success:
            print("   ✅ Transação à vista criada com sucesso!")
        else:
            print("   ❌ Falha ao criar transação à vista!")
        
        # Teste 2: Transação parcelada (3 parcelas)
        print("\n🔧 TESTE 2: Transação parcelada (3 parcelas)")
        success = transaction_manager.create_installment_transactions(
            user_id=user_id,
            wallet_id=wallet_id,
            category_id=category_id,
            transaction_type='expense',
            total_amount=300.00,
            description='Compra parcelada',
            transaction_date=datetime.now().strftime('%Y-%m-%d'),
            due_date=datetime.now().strftime('%Y-%m-%d'),
            installments=3
        )
        
        if success:
            print("   ✅ Transação parcelada criada com sucesso!")
        else:
            print("   ❌ Falha ao criar transação parcelada!")
        
        # Teste 3: Transação parcelada (12 parcelas)
        print("\n🔧 TESTE 3: Transação parcelada (12 parcelas)")
        success = transaction_manager.create_installment_transactions(
            user_id=user_id,
            wallet_id=wallet_id,
            category_id=category_id,
            transaction_type='expense',
            total_amount=1200.00,
            description='Compra em 12x',
            installments=12
        )
        
        if success:
            print("   ✅ Transação em 12x criada com sucesso!")
        else:
            print("   ❌ Falha ao criar transação em 12x!")
        
        # Verificar resultados
        count_after = db_manager.execute_query("SELECT COUNT(*) FROM transactions WHERE user_id = ?", (user_id,))[0][0]
        print(f"\n📊 Transações depois: {count_after}")
        print(f"📈 Transações criadas: {count_after - count_before}")
        
        # Verificar parcelas criadas
        print(f"\n📋 VERIFICAÇÃO DAS PARCELAS CRIADAS:")
        
        # Buscar transações recentes
        recent_transactions = db_manager.execute_query("""
            SELECT description, amount, installments, installment_number, transaction_date
            FROM transactions 
            WHERE user_id = ? 
            ORDER BY created_at DESC 
            LIMIT 20
        """, (user_id,))
        
        current_group = None
        for trans in recent_transactions:
            desc = trans['description']
            base_desc = desc.split(' - Parcela')[0] if ' - Parcela' in desc else desc
            
            if base_desc != current_group:
                current_group = base_desc
                print(f"\n💰 {base_desc}:")
            
            if trans['installments'] > 1:
                print(f"   Parcela {trans['installment_number']}/{trans['installments']}: "
                      f"R$ {trans['amount']:.2f} - {trans['transaction_date']}")
            else:
                print(f"   À vista: R$ {trans['amount']:.2f} - {trans['transaction_date']}")
        
        # Verificar se as datas estão corretas (mensais)
        print(f"\n📅 VERIFICAÇÃO DAS DATAS (PARCELAMENTO MENSAL):")
        
        parceladas = db_manager.execute_query("""
            SELECT description, transaction_date, installment_number
            FROM transactions 
            WHERE user_id = ? AND installments > 1 AND description LIKE '%Compra parcelada%'
            ORDER BY installment_number
        """, (user_id,))
        
        if parceladas:
            print("   Parcelas da 'Compra parcelada':")
            for parcela in parceladas:
                print(f"   Parcela {parcela['installment_number']}: {parcela['transaction_date']}")
        
        print(f"\n✅ TESTE DO PARCELAMENTO AUTOMÁTICO CONCLUÍDO!")
        print(f"💡 Esperado: 1 + 3 + 12 = 16 transações criadas")
        print(f"📊 Resultado: {count_after - count_before} transações criadas")
        
        if count_after - count_before == 16:
            print("🎉 PARCELAMENTO AUTOMÁTICO FUNCIONANDO PERFEITAMENTE!")
        else:
            print("⚠️  Número de transações não confere - verificar implementação")
        
    except Exception as e:
        print(f"❌ ERRO GERAL: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_parcelamento_automatico()
