#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Script de teste completo do sistema
"""

import sys
import os
from datetime import datetime, date, timedelta

# Adicionar src ao path
sys.path.insert(0, 'src')

def test_database():
    """Testa o banco de dados"""
    print("=== Testando Banco de Dados ===")
    
    try:
        from database import DatabaseManager
        
        db_manager = DatabaseManager("data/test_gestao_contas.db")
        print("✓ DatabaseManager criado")
        
        db_manager.initialize_database()
        print("✓ Banco de dados inicializado")
        
        return db_manager
        
    except Exception as e:
        print(f"✗ Erro no banco de dados: {str(e)}")
        return None

def test_authentication(db_manager):
    """Testa autenticação"""
    print("\n=== Testando Autenticação ===")
    
    try:
        from auth import AuthManager
        
        auth_manager = AuthManager(db_manager)
        print("✓ AuthManager criado")
        
        # Criar usuário admin
        if not auth_manager.user_exists('admin'):
            auth_manager.create_user(
                username='admin',
                password='admin123',
                email='<EMAIL>',
                is_admin=True,
                full_name='Administrador do Sistema'
            )
            print("✓ Usuário admin criado")
        else:
            print("✓ Usuário admin já existe")
        
        # Testar login
        user_data = auth_manager.authenticate_user('admin', 'admin123')
        if user_data:
            print(f"✓ Login bem-sucedido: {user_data['full_name']}")
            return auth_manager, user_data
        else:
            print("✗ Erro no login")
            return None, None
            
    except Exception as e:
        print(f"✗ Erro na autenticação: {str(e)}")
        return None, None

def test_wallets(db_manager, user_data):
    """Testa gerenciamento de carteiras"""
    print("\n=== Testando Carteiras ===")
    
    try:
        from modules.wallet_manager import WalletManager
        
        wallet_manager = WalletManager(db_manager)
        print("✓ WalletManager criado")
        
        user_id = user_data['id']
        
        # Criar carteira de teste
        wallet_manager.create_wallet(
            user_id=user_id,
            name="Conta Corrente Teste",
            description="Conta para testes",
            initial_balance=1000.0,
            wallet_type="checking"
        )
        print("✓ Carteira criada")
        
        # Listar carteiras
        wallets = wallet_manager.get_user_wallets(user_id)
        print(f"✓ {len(wallets)} carteira(s) encontrada(s)")
        
        # Obter resumo
        summary = wallet_manager.get_wallet_summary(user_id)
        print(f"✓ Resumo: {summary['active_wallets']} ativas, saldo total: R$ {summary['total_balance']:,.2f}")
        
        return wallet_manager, wallets[0]['id'] if wallets else None
        
    except Exception as e:
        print(f"✗ Erro nas carteiras: {str(e)}")
        return None, None

def test_categories(db_manager, user_data):
    """Testa gerenciamento de categorias"""
    print("\n=== Testando Categorias ===")
    
    try:
        from modules.category_manager import CategoryManager
        
        category_manager = CategoryManager(db_manager)
        print("✓ CategoryManager criado")
        
        user_id = user_data['id']
        
        # Listar categorias padrão
        categories = category_manager.get_user_categories(user_id)
        print(f"✓ {len(categories)} categoria(s) disponível(is)")
        
        # Criar categoria personalizada
        category_manager.create_category(
            user_id=user_id,
            name="Teste Personalizado",
            description="Categoria de teste",
            category_type="expense",
            color="#ff0000"
        )
        print("✓ Categoria personalizada criada")
        
        # Obter estatísticas
        stats = category_manager.get_categories_by_type_summary(user_id)
        print(f"✓ Estatísticas obtidas: {len(stats)} tipos")
        
        return category_manager
        
    except Exception as e:
        print(f"✗ Erro nas categorias: {str(e)}")
        return None

def test_transactions(db_manager, user_data, wallet_id):
    """Testa gerenciamento de transações"""
    print("\n=== Testando Transações ===")
    
    if not wallet_id:
        print("✗ Sem carteira para testar transações")
        return None
    
    try:
        from modules.transaction_manager import TransactionManager
        
        transaction_manager = TransactionManager(db_manager)
        print("✓ TransactionManager criado")
        
        user_id = user_data['id']
        
        # Obter uma categoria para usar
        from modules.category_manager import CategoryManager
        category_manager = CategoryManager(db_manager)
        categories = category_manager.get_user_categories(user_id, category_type="income")
        
        if not categories:
            print("✗ Nenhuma categoria de receita encontrada")
            return None
        
        income_category_id = categories[0]['id']
        
        # Criar transação de receita
        transaction_manager.create_transaction(
            user_id=user_id,
            wallet_id=wallet_id,
            category_id=income_category_id,
            transaction_type="income",
            amount=500.0,
            description="Receita de teste",
            transaction_date=date.today(),
            is_paid=True
        )
        print("✓ Transação de receita criada")
        
        # Criar transação de despesa
        expense_categories = category_manager.get_user_categories(user_id, category_type="expense")
        if expense_categories:
            expense_category_id = expense_categories[0]['id']
            
            transaction_manager.create_transaction(
                user_id=user_id,
                wallet_id=wallet_id,
                category_id=expense_category_id,
                transaction_type="expense",
                amount=150.0,
                description="Despesa de teste",
                transaction_date=date.today(),
                due_date=date.today() + timedelta(days=5),
                is_paid=False
            )
            print("✓ Transação de despesa criada")
        
        # Listar transações
        transactions = transaction_manager.get_user_transactions(user_id, limit=10)
        print(f"✓ {len(transactions)} transação(ões) encontrada(s)")
        
        # Obter resumo
        summary = transaction_manager.get_transactions_summary(user_id)
        print(f"✓ Resumo: Receitas R$ {summary['income']['paid_total']:,.2f}, Despesas R$ {summary['expense']['paid_total']:,.2f}")
        
        return transaction_manager
        
    except Exception as e:
        print(f"✗ Erro nas transações: {str(e)}")
        return None

def test_alerts(db_manager, user_data):
    """Testa sistema de alertas"""
    print("\n=== Testando Alertas ===")
    
    try:
        from modules.alert_manager import AlertManager
        
        alert_manager = AlertManager(db_manager)
        print("✓ AlertManager criado")
        
        user_id = user_data['id']
        
        # Obter todos os alertas
        alerts = alert_manager.get_all_alerts(user_id)
        print(f"✓ {len(alerts)} alerta(s) encontrado(s)")
        
        # Obter resumo
        summary = alert_manager.get_alerts_summary(user_id)
        print(f"✓ Resumo: {summary['total']} total, {summary['critical']} críticos")
        
        return alert_manager
        
    except Exception as e:
        print(f"✗ Erro nos alertas: {str(e)}")
        return None

def test_backup(db_manager):
    """Testa sistema de backup"""
    print("\n=== Testando Backup ===")
    
    try:
        from modules.backup_manager import BackupManager
        
        backup_manager = BackupManager(db_manager)
        print("✓ BackupManager criado")
        
        # Criar backup do banco
        result = backup_manager.create_database_backup()
        if result['success']:
            print(f"✓ Backup criado: {result['backup_path']}")
            
            # Verificar integridade
            integrity = backup_manager.verify_backup_integrity(result['backup_path'])
            if integrity['valid']:
                print("✓ Integridade do backup verificada")
            else:
                print(f"✗ Problema na integridade: {integrity['error']}")
        
        # Listar backups
        backups = backup_manager.list_backups()
        print(f"✓ {len(backups)} backup(s) disponível(is)")
        
        return backup_manager
        
    except Exception as e:
        print(f"✗ Erro no backup: {str(e)}")
        return None

def main():
    """Função principal de teste"""
    print("Sistema de Gestão de Contas - Teste Completo")
    print("=" * 50)
    
    # Testar componentes
    db_manager = test_database()
    if not db_manager:
        return
    
    auth_manager, user_data = test_authentication(db_manager)
    if not auth_manager or not user_data:
        return
    
    wallet_manager, wallet_id = test_wallets(db_manager, user_data)
    category_manager = test_categories(db_manager, user_data)
    transaction_manager = test_transactions(db_manager, user_data, wallet_id)
    alert_manager = test_alerts(db_manager, user_data)
    backup_manager = test_backup(db_manager)
    
    print("\n" + "=" * 50)
    print("✓ TESTE COMPLETO FINALIZADO COM SUCESSO!")
    print("\nO sistema está pronto para uso.")
    print("\nPara iniciar a aplicação, execute: python main.py")
    print("Login: admin | Senha: admin123")

if __name__ == "__main__":
    main()
