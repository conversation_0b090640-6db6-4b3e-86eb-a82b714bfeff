#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Teste simples para verificar se as janelas aparecem
"""

import tkinter as tk
from tkinter import messagebox
import time

def main():
    print("Criando janela de teste...")
    
    # Criar janela
    root = tk.Tk()
    root.title("🚗 TESTE - Sistema de Gestão de Contas")
    root.geometry("600x400")
    
    # Configurações para forçar a janela aparecer
    root.lift()
    root.attributes('-topmost', True)
    root.focus_force()
    
    # Centralizar
    root.update_idletasks()
    x = (root.winfo_screenwidth() // 2) - (600 // 2)
    y = (root.winfo_screenheight() // 2) - (400 // 2)
    root.geometry(f"600x400+{x}+{y}")
    
    # Conteúdo
    frame = tk.Frame(root, padx=40, pady=40, bg='lightblue')
    frame.pack(fill=tk.BOTH, expand=True)
    
    tk.Label(frame, text="🎉 JANELA DE TESTE", 
            font=('Arial', 20, 'bold'), bg='lightblue').pack(pady=20)
    
    tk.Label(frame, text="Se você está vendo esta mensagem,\nas janelas estão funcionando!", 
            font=('Arial', 14), bg='lightblue').pack(pady=10)
    
    tk.Label(frame, text="Agora vamos tentar executar a aplicação principal:", 
            font=('Arial', 12), bg='lightblue').pack(pady=20)
    
    def executar_app():
        try:
            root.destroy()
            
            # Executar aplicação principal
            import subprocess
            import sys
            
            print("Executando aplicação principal...")
            subprocess.run([sys.executable, "main.py"])
            
        except Exception as e:
            messagebox.showerror("Erro", f"Erro: {e}")
    
    def fechar():
        root.destroy()
    
    # Botões
    btn_frame = tk.Frame(frame, bg='lightblue')
    btn_frame.pack(pady=20)
    
    tk.Button(btn_frame, text="🚀 EXECUTAR APLICAÇÃO", 
             command=executar_app,
             font=('Arial', 12, 'bold'),
             bg='green', fg='white',
             padx=20, pady=10).pack(side=tk.LEFT, padx=10)
    
    tk.Button(btn_frame, text="❌ FECHAR TESTE", 
             command=fechar,
             font=('Arial', 12, 'bold'),
             bg='red', fg='white',
             padx=20, pady=10).pack(side=tk.LEFT, padx=10)
    
    # Remover topmost após 1 segundo
    root.after(1000, lambda: root.attributes('-topmost', False))
    
    print("Janela criada! Deve aparecer na sua tela.")
    print("Se não aparecer, verifique a barra de tarefas.")
    
    # Iniciar loop
    root.mainloop()

if __name__ == "__main__":
    main()
