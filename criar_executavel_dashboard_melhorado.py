#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Cria executável com dashboard melhorado dos formulários
"""

import subprocess
import sys
import shutil
import zipfile
from pathlib import Path
import time

def check_dependencies():
    """Verifica e instala dependências necessárias"""
    dependencies = ['pyinstaller', 'tkcalendar']
    
    for dep in dependencies:
        try:
            __import__(dep)
            print(f"✓ {dep} encontrado")
        except ImportError:
            print(f"{dep} não encontrado. Instalando...")
            try:
                subprocess.check_call([sys.executable, "-m", "pip", "install", dep])
                print(f"✓ {dep} instalado com sucesso")
            except:
                print(f"✗ Erro ao instalar {dep}")
                return False
    
    return True

def create_executable():
    """Cria o executável com dashboard melhorado"""
    print("Criando executável GestaoContasDashboardMelhorado.exe...")
    
    try:
        # Comando para criar executável
        cmd = [
            sys.executable, "-m", "PyInstaller",
            "--onefile",
            "--windowed",
            "--name=GestaoContasDashboardMelhorado",
            "--icon=NONE",
            "gestao_contas_corrigido.py"
        ]
        
        print("Executando PyInstaller...")
        result = subprocess.run(cmd, capture_output=True, text=True)
        
        if result.returncode == 0:
            exe_path = Path("dist/GestaoContasDashboardMelhorado.exe")
            if exe_path.exists():
                size_mb = exe_path.stat().st_size / (1024 * 1024)
                print(f"✓ Executável criado: {exe_path}")
                print(f"✓ Tamanho: {size_mb:.1f} MB")
                return True
            else:
                print("✗ Executável não encontrado após criação")
                return False
        else:
            print("✗ Erro ao criar executável:")
            print(result.stderr)
            return False
            
    except Exception as e:
        print(f"✗ Erro: {str(e)}")
        return False

def create_distribution_package():
    """Cria pacote de distribuição com dashboard melhorado"""
    print("\nCriando pacote de distribuição com dashboard melhorado...")
    
    try:
        # Aguardar um pouco para liberar arquivos
        time.sleep(2)
        
        # Criar diretório de distribuição
        dist_dir = Path("GestaoContasDashboardMelhorado_v1.0.0")
        if dist_dir.exists():
            shutil.rmtree(dist_dir)
        
        dist_dir.mkdir()
        
        # Copiar executável
        exe_source = Path("dist/GestaoContasDashboardMelhorado.exe")
        if exe_source.exists():
            shutil.copy2(exe_source, dist_dir / "GestaoContasDashboardMelhorado.exe")
            print("✓ Executável copiado")
        else:
            print("✗ Executável não encontrado")
            return False
        
        # Criar diretórios necessários
        (dist_dir / "data").mkdir()
        (dist_dir / "backups").mkdir()
        (dist_dir / "src").mkdir()
        (dist_dir / "src" / "gui").mkdir()
        (dist_dir / "src" / "modules").mkdir()
        print("✓ Diretórios criados")
        
        # Copiar arquivos melhorados
        files_to_copy = [
            ("src/gui/transaction_dialog.py", "src/gui/transaction_dialog.py"),
            ("src/gui/user_dialog_improved.py", "src/gui/user_dialog_improved.py"),
            ("src/gui/wallet_dialog_improved.py", "src/gui/wallet_dialog_improved.py"),
            ("teste_formularios_melhorados.py", "Teste Formulários Melhorados.py"),
        ]
        
        for src, dst in files_to_copy:
            src_path = Path(src)
            if src_path.exists():
                shutil.copy2(src_path, dist_dir / dst)
                print(f"✓ {src} copiado")
        
        # Copiar script de atalho corrigido
        atalho_source = Path("criar_atalho_desktop.py")
        if atalho_source.exists():
            shutil.copy2(atalho_source, dist_dir / "Criar Atalho na Área de Trabalho.py")
            print("✓ Script de atalho corrigido copiado")
        
        # Criar arquivo .bat para atalho
        bat_content = '''@echo off
echo Criando atalho na área de trabalho...
python "Criar Atalho na Área de Trabalho.py"
pause'''
        
        with open(dist_dir / "🔗 Criar Atalho na Área de Trabalho.bat", 'w', encoding='utf-8') as f:
            f.write(bat_content)
        print("✓ Arquivo .bat de atalho criado")
        
        print("✓ Pacote criado com sucesso!")
        return True
        
    except Exception as e:
        print(f"✗ Erro ao criar pacote: {str(e)}")
        return False

def main():
    """Função principal"""
    print("CRIADOR DE EXECUTÁVEL - DASHBOARD MELHORADO DOS FORMULÁRIOS")
    print("Sistema com Formulários Organizados + Seções + Scroll + Campos Visíveis")
    print("=" * 80)
    
    # Verificar dependências
    if not check_dependencies():
        print("\n✗ Não foi possível instalar todas as dependências")
        print("Alternativa: Use 'python gestao_contas_corrigido.py'")
        input("Pressione Enter para sair...")
        return
    
    # Criar executável
    if not create_executable():
        print("\n✗ Falha na criação do executável")
        print("Alternativa: Use 'python gestao_contas_corrigido.py'")
        input("Pressione Enter para sair...")
        return
    
    # Criar pacote de distribuição
    if not create_distribution_package():
        print("\n✗ Falha na criação do pacote")
        input("Pressione Enter para sair...")
        return
    
    print("\n" + "=" * 80)
    print("🎉 EXECUTÁVEL COM DASHBOARD MELHORADO CRIADO!")
    print("\n📦 ARQUIVOS GERADOS:")
    print("  • dist/GestaoContasDashboardMelhorado.exe (executável)")
    print("  • GestaoContasDashboardMelhorado_v1.0.0/ (pasta completa)")
    
    print("\n🚀 COMO USAR:")
    print("  1. Acesse: GestaoContasDashboardMelhorado_v1.0.0/")
    print("  2. Execute: GestaoContasDashboardMelhorado.exe")
    print("  3. Login: admin/admin123")
    print("  4. Teste os formulários com dashboard melhorado!")
    
    print("\n🆕 MELHORIAS IMPLEMENTADAS:")
    print("  • 📊 Dashboard organizado em seções")
    print("  • 🔄 Scroll automático nos formulários")
    print("  • 📋 Campos organizados logicamente")
    print("  • 🎨 Interface moderna e profissional")
    print("  • 💡 Dicas e ajudas contextuais")
    print("  • ✅ Todos os campos visíveis e acessíveis")
    
    print("\n📱 FORMULÁRIOS MELHORADOS:")
    print("  • 💸 Transação: 700x750px com 4 seções organizadas")
    print("    - 📋 Informações Básicas (Descrição + Valor)")
    print("    - 🏦 Carteira e Categoria")
    print("    - 📅 Datas e Parcelas")
    print("    - 📝 Status e Observações")
    
    print("  • 👥 Usuário: 750x650px com 3 seções organizadas")
    print("    - 👤 Informações Pessoais")
    print("    - 🔐 Credenciais de Acesso")
    print("    - ⚙️ Configurações")
    
    print("  • 💳 Carteira: 650x550px com 3 seções organizadas")
    print("    - 💼 Informações Básicas")
    print("    - 📝 Descrição e Configurações")
    print("    - ℹ️ Informações Adicionais")
    
    print("\n🎯 CARACTERÍSTICAS DO DASHBOARD:")
    print("  • 📊 Cabeçalhos coloridos por tipo")
    print("  • 🔄 Scroll suave e responsivo")
    print("  • 📋 Seções bem definidas com bordas")
    print("  • 💡 Dicas e informações contextuais")
    print("  • ⌨️ Navegação por teclado (Tab, Enter, Esc)")
    print("  • 🎨 Ícones e cores organizacionais")
    print("  • ✅ Campos obrigatórios marcados com *")
    print("  • 📱 Layout responsivo e moderno")
    
    print("\n✅ FUNCIONALIDADES COMPLETAS:")
    print("  • 📊 Dashboard financeiro moderno")
    print("  • 💳 Gestão completa de carteiras")
    print("  • 💸 Transações avançadas com parcelas")
    print("  • 📅 Controle de vencimentos")
    print("  • 👥 Sistema completo de usuários")
    print("  • 🛡️ Administração avançada")
    print("  • 📊 5 tipos de relatórios funcionais")
    print("  • ⚙️ Backup e restauração")
    print("  • ✏️ Botões de editar funcionando")
    print("  • 🔄 Atualização automática")
    print("  • 🎨 Interface moderna e profissional")
    print("  • 📊 Linhas alternadas em todas as listas")
    print("  • 📅 Calendários nos campos de data")
    print("  • 🔢 Sistema de parcelas automáticas")
    
    print("\n🎯 DASHBOARD MELHORADO DOS FORMULÁRIOS IMPLEMENTADO!")
    print("=" * 80)
    
    input("\nPressione Enter para sair...")

if __name__ == "__main__":
    main()
