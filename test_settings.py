#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Teste das configurações
"""

import sys
import os
import tkinter as tk

# Adicionar o diretório src ao path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from database import DatabaseManager
from auth import AuthManager
from gui.main_window import MainWindow

def test_settings():
    """Testa as configurações"""
    print("🧪 TESTE DAS CONFIGURAÇÕES")
    print("=" * 60)
    
    try:
        # Inicializar banco de dados
        db_manager = DatabaseManager()
        db_manager.initialize_database()
        
        auth_manager = AuthManager(db_manager)
        
        # Fazer login
        user_data = auth_manager.authenticate_user('admin', 'admin123')
        if not user_data:
            print("❌ Falha no login!")
            return
        
        print(f"✅ Login realizado: {user_data['full_name']}")
        
        # Criar janela principal
        root = tk.Tk()
        root.title("Teste Configurações")
        root.geometry("800x600")
        
        # Função de logout mock
        def mock_logout():
            print("Logout chamado")
        
        # Criar MainWindow
        main_window = MainWindow(root, db_manager, auth_manager, user_data, mock_logout)
        
        print("✅ Janela principal criada com sucesso!")
        
        # Testar navegação para configurações
        print("\n🔧 Testando navegação para configurações...")
        try:
            main_window.show_preferences()
            print("✅ Navegação para configurações funcionou!")
        except Exception as e:
            print(f"❌ Erro na navegação: {str(e)}")
        
        # Testar gerenciamento de categorias
        print("\n🔧 Testando gerenciamento de categorias...")
        try:
            main_window.show_categories_manager()
            print("✅ Gerenciamento de categorias funcionou!")
        except Exception as e:
            print(f"❌ Erro no gerenciamento de categorias: {str(e)}")
        
        # Testar importação de categorias padrão
        print("\n🔧 Testando importação de categorias padrão...")
        try:
            # Simular resposta "não" para não criar categorias no teste
            import tkinter.messagebox
            original_askyesno = tkinter.messagebox.askyesno
            tkinter.messagebox.askyesno = lambda title, message: False
            
            main_window.import_default_categories()
            print("✅ Importação de categorias padrão funcionou!")
            
            # Restaurar função original
            tkinter.messagebox.askyesno = original_askyesno
            
        except Exception as e:
            print(f"❌ Erro na importação de categorias: {str(e)}")
        
        # Testar alteração de senha
        print("\n🔧 Testando diálogo de alteração de senha...")
        try:
            main_window.show_change_password()
            print("✅ Diálogo de alteração de senha funcionou!")
        except Exception as e:
            print(f"❌ Erro no diálogo de senha: {str(e)}")
        
        # Testar edição de perfil
        print("\n🔧 Testando diálogo de edição de perfil...")
        try:
            main_window.show_edit_profile()
            print("✅ Diálogo de edição de perfil funcionou!")
        except Exception as e:
            print(f"❌ Erro no diálogo de perfil: {str(e)}")
        
        # Testar backup
        print("\n🔧 Testando backup...")
        try:
            # Simular cancelamento do diálogo de arquivo
            import tkinter.filedialog
            original_asksaveasfilename = tkinter.filedialog.asksaveasfilename
            tkinter.filedialog.asksaveasfilename = lambda **kwargs: None
            
            main_window.backup_database()
            print("✅ Função de backup funcionou!")
            
            # Restaurar função original
            tkinter.filedialog.asksaveasfilename = original_asksaveasfilename
            
        except Exception as e:
            print(f"❌ Erro no backup: {str(e)}")
        
        print("\n✅ TODOS OS TESTES CONCLUÍDOS!")
        print("💡 Se não houve erros, as configurações estão funcionando.")
        
        # Fechar janela após um tempo
        root.after(3000, root.destroy)  # Fechar após 3 segundos
        root.mainloop()
        
    except Exception as e:
        print(f"❌ ERRO GERAL: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_settings()
