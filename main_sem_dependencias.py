#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Sistema de Gestão de Contas - Versão Sem Dependências Externas
Funciona apenas com bibliotecas padrão do Python
"""

import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import sqlite3
import os
import sys
from datetime import datetime, date, timedelta
import hashlib
import json
import shutil
from pathlib import Path

class GestaoContasSemDependencias:
    def __init__(self):
        self.db_path = "data/gestao_contas.db"
        self.ensure_directories()
        self.initialize_database()
        self.current_user = None
        
    def ensure_directories(self):
        """Garante que os diretórios necessários existem"""
        for directory in ["data", "backups", "logs"]:
            Path(directory).mkdir(exist_ok=True)
    
    def get_connection(self):
        """Retorna conexão com o banco"""
        conn = sqlite3.connect(self.db_path)
        conn.row_factory = sqlite3.Row
        return conn
    
    def hash_password(self, password):
        """<PERSON>era hash da senha usando hashlib"""
        return hashlib.sha256(password.encode('utf-8')).hexdigest()
    
    def verify_password(self, password, hashed_password):
        """Verifica se a senha está correta"""
        return hashlib.sha256(password.encode('utf-8')).hexdigest() == hashed_password
    
    def initialize_database(self):
        """Inicializa o banco de dados"""
        conn = self.get_connection()
        cursor = conn.cursor()
        
        try:
            # Tabela de usuários
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS users (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    username TEXT UNIQUE NOT NULL,
                    password_hash TEXT NOT NULL,
                    email TEXT UNIQUE NOT NULL,
                    full_name TEXT NOT NULL,
                    is_admin BOOLEAN DEFAULT FALSE,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            ''')
            
            # Tabela de carteiras
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS wallets (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    user_id INTEGER NOT NULL,
                    name TEXT NOT NULL,
                    description TEXT DEFAULT '',
                    initial_balance DECIMAL(10,2) DEFAULT 0.00,
                    current_balance DECIMAL(10,2) DEFAULT 0.00,
                    wallet_type TEXT DEFAULT 'checking',
                    is_active BOOLEAN DEFAULT TRUE,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (user_id) REFERENCES users (id)
                )
            ''')
            
            # Tabela de categorias
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS categories (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    user_id INTEGER,
                    name TEXT NOT NULL,
                    description TEXT DEFAULT '',
                    category_type TEXT NOT NULL,
                    color TEXT DEFAULT '#007ACC',
                    is_active BOOLEAN DEFAULT TRUE,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (user_id) REFERENCES users (id)
                )
            ''')
            
            # Tabela de transações
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS transactions (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    user_id INTEGER NOT NULL,
                    wallet_id INTEGER NOT NULL,
                    category_id INTEGER NOT NULL,
                    transaction_type TEXT NOT NULL,
                    amount DECIMAL(10,2) NOT NULL,
                    description TEXT NOT NULL,
                    transaction_date DATE NOT NULL,
                    due_date DATE,
                    is_paid BOOLEAN DEFAULT FALSE,
                    notes TEXT DEFAULT '',
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (user_id) REFERENCES users (id),
                    FOREIGN KEY (wallet_id) REFERENCES wallets (id),
                    FOREIGN KEY (category_id) REFERENCES categories (id)
                )
            ''')
            
            conn.commit()
            
            # Criar usuário admin se não existir
            cursor.execute("SELECT COUNT(*) FROM users WHERE username = 'admin'")
            if cursor.fetchone()[0] == 0:
                password_hash = self.hash_password('admin123')
                cursor.execute('''
                    INSERT INTO users (username, password_hash, email, full_name, is_admin)
                    VALUES ('admin', ?, '<EMAIL>', 'Administrador', TRUE)
                ''', (password_hash,))
                conn.commit()
                print("Usuário admin criado: admin/admin123")
            
            # Criar categorias padrão se não existirem
            cursor.execute("SELECT COUNT(*) FROM categories")
            if cursor.fetchone()[0] == 0:
                self.create_default_categories(cursor)
                conn.commit()
                
        except Exception as e:
            print(f"Erro ao inicializar banco: {e}")
        finally:
            conn.close()
    
    def create_default_categories(self, cursor):
        """Cria categorias padrão"""
        # Categorias de receita
        income_categories = [
            ('Salário', 'Salário mensal', '#28a745'),
            ('Freelance', 'Trabalhos extras', '#17a2b8'),
            ('Investimentos', 'Rendimentos', '#ffc107'),
            ('Outros', 'Outras receitas', '#6c757d')
        ]
        
        # Categorias de despesa
        expense_categories = [
            ('Alimentação', 'Gastos com comida', '#dc3545'),
            ('Transporte', 'Gastos com transporte', '#fd7e14'),
            ('Moradia', 'Aluguel, condomínio', '#6f42c1'),
            ('Saúde', 'Gastos médicos', '#e83e8c'),
            ('Educação', 'Cursos, livros', '#20c997'),
            ('Lazer', 'Entretenimento', '#0dcaf0'),
            ('Outros', 'Outras despesas', '#6c757d')
        ]
        
        for name, desc, color in income_categories:
            cursor.execute('''
                INSERT OR IGNORE INTO categories (user_id, name, description, category_type, color)
                VALUES (NULL, ?, ?, 'income', ?)
            ''', (name, desc, color))
        
        for name, desc, color in expense_categories:
            cursor.execute('''
                INSERT OR IGNORE INTO categories (user_id, name, description, category_type, color)
                VALUES (NULL, ?, ?, 'expense', ?)
            ''', (name, desc, color))
    
    def authenticate_user(self, username, password):
        """Autentica usuário"""
        conn = self.get_connection()
        cursor = conn.cursor()
        
        cursor.execute('''
            SELECT id, username, full_name, is_admin, email
            FROM users 
            WHERE username = ? AND password_hash = ?
        ''', (username, self.hash_password(password)))
        
        result = cursor.fetchone()
        conn.close()
        
        if result:
            return dict(result)
        return None
    
    def show_login(self):
        """Mostra tela de login"""
        login_window = tk.Tk()
        login_window.title("Sistema de Gestão de Contas - Login")
        login_window.geometry("450x350")
        login_window.resizable(False, False)
        
        # Centralizar janela
        login_window.update_idletasks()
        x = (login_window.winfo_screenwidth() // 2) - (450 // 2)
        y = (login_window.winfo_screenheight() // 2) - (350 // 2)
        login_window.geometry(f'450x350+{x}+{y}')
        
        # Frame principal
        main_frame = ttk.Frame(login_window, padding="30")
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # Título
        title_label = ttk.Label(main_frame, text="Sistema de Gestão de Contas", 
                               font=("Arial", 18, "bold"))
        title_label.pack(pady=(0, 10))
        
        subtitle_label = ttk.Label(main_frame, text="Versão Sem Dependências Externas", 
                                 font=("Arial", 10), foreground="gray")
        subtitle_label.pack(pady=(0, 30))
        
        # Campos
        ttk.Label(main_frame, text="Usuário:", font=("Arial", 11)).pack(anchor=tk.W, pady=(0, 5))
        username_entry = ttk.Entry(main_frame, font=("Arial", 12))
        username_entry.pack(fill=tk.X, pady=(0, 15))
        username_entry.insert(0, "admin")
        
        ttk.Label(main_frame, text="Senha:", font=("Arial", 11)).pack(anchor=tk.W, pady=(0, 5))
        password_entry = ttk.Entry(main_frame, show="*", font=("Arial", 12))
        password_entry.pack(fill=tk.X, pady=(0, 25))
        password_entry.insert(0, "admin123")
        
        def do_login():
            username = username_entry.get().strip()
            password = password_entry.get()
            
            if not username or not password:
                messagebox.showerror("Erro", "Preencha todos os campos")
                return
            
            user_data = self.authenticate_user(username, password)
            if user_data:
                self.current_user = user_data
                login_window.destroy()
                self.show_main_window()
            else:
                messagebox.showerror("Erro", "Usuário ou senha incorretos")
                password_entry.delete(0, tk.END)
                password_entry.focus()
        
        # Botão login
        login_btn = ttk.Button(main_frame, text="Entrar", command=do_login)
        login_btn.pack(fill=tk.X, pady=(0, 15))
        
        # Separador
        separator = ttk.Separator(main_frame, orient='horizontal')
        separator.pack(fill=tk.X, pady=(10, 20))
        
        # Info
        info_label = ttk.Label(main_frame, 
                              text="Login padrão:\nUsuário: admin\nSenha: admin123\n\nEsta versão funciona apenas\ncom bibliotecas padrão do Python",
                              font=("Arial", 9), foreground="gray", justify=tk.CENTER)
        info_label.pack()
        
        # Bind Enter
        login_window.bind('<Return>', lambda e: do_login())
        username_entry.focus()
        
        login_window.mainloop()
    
    def show_main_window(self):
        """Mostra janela principal"""
        if not self.current_user:
            return
            
        main_window = tk.Tk()
        main_window.title(f"Sistema de Gestão de Contas - {self.current_user['full_name']}")
        main_window.geometry("1200x800")
        main_window.state('zoomed')
        
        # Menu
        self.create_menu(main_window)
        
        # Frame principal
        main_frame = ttk.Frame(main_window)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # Header
        self.create_header(main_frame)
        
        # Notebook
        self.notebook = ttk.Notebook(main_frame)
        self.notebook.pack(fill=tk.BOTH, expand=True, pady=(10, 0))
        
        # Criar abas
        self.create_dashboard_tab()
        self.create_wallets_tab()
        self.create_transactions_tab()
        self.create_reports_tab()
        self.create_settings_tab()
        
        if self.current_user['is_admin']:
            self.create_admin_tab()
        
        # Carregar dados iniciais
        self.load_initial_data()
        
        main_window.mainloop()
    
    def create_menu(self, window):
        """Cria menu da aplicação"""
        menubar = tk.Menu(window)
        window.config(menu=menubar)
        
        # Menu Arquivo
        file_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="Arquivo", menu=file_menu)
        file_menu.add_command(label="Backup", command=self.backup_database)
        file_menu.add_command(label="Restaurar", command=self.restore_database)
        file_menu.add_separator()
        file_menu.add_command(label="Sair", command=window.quit)
        
        # Menu Ajuda
        help_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="Ajuda", menu=help_menu)
        help_menu.add_command(label="Sobre", command=self.show_about)
    
    def create_header(self, parent):
        """Cria cabeçalho"""
        header_frame = ttk.Frame(parent)
        header_frame.pack(fill=tk.X, pady=(0, 10))
        
        title_label = ttk.Label(header_frame, text="Dashboard Financeiro", 
                               font=("Arial", 16, "bold"))
        title_label.pack(side=tk.LEFT)
        
        user_info = f"Usuário: {self.current_user['full_name']}"
        if self.current_user['is_admin']:
            user_info += " (Admin)"
        
        user_label = ttk.Label(header_frame, text=user_info, font=("Arial", 10))
        user_label.pack(side=tk.RIGHT)
        
        date_label = ttk.Label(header_frame, text=f"Data: {date.today().strftime('%d/%m/%Y')}",
                              font=("Arial", 10))
        date_label.pack(side=tk.RIGHT, padx=(0, 20))

    def create_dashboard_tab(self):
        """Cria aba do dashboard"""
        dashboard_frame = ttk.Frame(self.notebook)
        self.notebook.add(dashboard_frame, text="Dashboard")

        # Cards de resumo
        cards_frame = ttk.Frame(dashboard_frame)
        cards_frame.pack(fill=tk.X, padx=20, pady=20)

        # Card Saldo Total
        total_card = ttk.LabelFrame(cards_frame, text="Saldo Total", padding=10)
        total_card.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=(0, 10))

        self.total_balance_label = ttk.Label(total_card, text="R$ 0,00",
                                           font=('Arial', 16, 'bold'), foreground='blue')
        self.total_balance_label.pack()

        # Card Receitas do Mês
        income_card = ttk.LabelFrame(cards_frame, text="Receitas do Mês", padding=10)
        income_card.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=(0, 10))

        self.month_income_label = ttk.Label(income_card, text="R$ 0,00",
                                          font=('Arial', 16, 'bold'), foreground='green')
        self.month_income_label.pack()

        # Card Despesas do Mês
        expense_card = ttk.LabelFrame(cards_frame, text="Despesas do Mês", padding=10)
        expense_card.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=(0, 10))

        self.month_expense_label = ttk.Label(expense_card, text="R$ 0,00",
                                           font=('Arial', 16, 'bold'), foreground='red')
        self.month_expense_label.pack()

        # Card Saldo do Mês
        balance_card = ttk.LabelFrame(cards_frame, text="Saldo do Mês", padding=10)
        balance_card.pack(side=tk.LEFT, fill=tk.X, expand=True)

        self.month_balance_label = ttk.Label(balance_card, text="R$ 0,00",
                                           font=('Arial', 16, 'bold'))
        self.month_balance_label.pack()

        # Lista de transações recentes
        recent_frame = ttk.LabelFrame(dashboard_frame, text="Transações Recentes", padding=10)
        recent_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=(0, 20))

        columns = ('Data', 'Descrição', 'Categoria', 'Valor', 'Tipo')
        self.recent_tree = ttk.Treeview(recent_frame, columns=columns, show='headings', height=10)

        for col in columns:
            self.recent_tree.heading(col, text=col)
            self.recent_tree.column(col, width=120)

        scrollbar = ttk.Scrollbar(recent_frame, orient=tk.VERTICAL, command=self.recent_tree.yview)
        self.recent_tree.configure(yscrollcommand=scrollbar.set)

        self.recent_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

    def create_wallets_tab(self):
        """Cria aba de carteiras"""
        wallets_frame = ttk.Frame(self.notebook)
        self.notebook.add(wallets_frame, text="Carteiras")

        # Toolbar
        toolbar = ttk.Frame(wallets_frame)
        toolbar.pack(fill=tk.X, padx=10, pady=10)

        ttk.Button(toolbar, text="Nova Carteira", command=self.new_wallet).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(toolbar, text="Editar", command=self.edit_wallet).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(toolbar, text="Excluir", command=self.delete_wallet).pack(side=tk.LEFT)

        # Lista de carteiras
        columns = ('Nome', 'Tipo', 'Saldo Inicial', 'Saldo Atual', 'Status')
        self.wallets_tree = ttk.Treeview(wallets_frame, columns=columns, show='headings')

        for col in columns:
            self.wallets_tree.heading(col, text=col)
            self.wallets_tree.column(col, width=150)

        wallet_scroll = ttk.Scrollbar(wallets_frame, orient=tk.VERTICAL, command=self.wallets_tree.yview)
        self.wallets_tree.configure(yscrollcommand=wallet_scroll.set)

        self.wallets_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=(10, 0), pady=(0, 10))
        wallet_scroll.pack(side=tk.RIGHT, fill=tk.Y, pady=(0, 10))

    def create_transactions_tab(self):
        """Cria aba de transações"""
        transactions_frame = ttk.Frame(self.notebook)
        self.notebook.add(transactions_frame, text="Transações")

        # Toolbar
        toolbar = ttk.Frame(transactions_frame)
        toolbar.pack(fill=tk.X, padx=10, pady=10)

        ttk.Button(toolbar, text="Nova Receita", command=self.new_income).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(toolbar, text="Nova Despesa", command=self.new_expense).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(toolbar, text="Editar", command=self.edit_transaction).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(toolbar, text="Excluir", command=self.delete_transaction).pack(side=tk.LEFT)

        # Lista de transações
        columns = ('Data', 'Descrição', 'Categoria', 'Carteira', 'Valor', 'Tipo', 'Status')
        self.transactions_tree = ttk.Treeview(transactions_frame, columns=columns, show='headings')

        for col in columns:
            self.transactions_tree.heading(col, text=col)
            if col == 'Descrição':
                self.transactions_tree.column(col, width=200)
            else:
                self.transactions_tree.column(col, width=100)

        trans_scroll = ttk.Scrollbar(transactions_frame, orient=tk.VERTICAL, command=self.transactions_tree.yview)
        self.transactions_tree.configure(yscrollcommand=trans_scroll.set)

        self.transactions_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=(10, 0), pady=(0, 10))
        trans_scroll.pack(side=tk.RIGHT, fill=tk.Y, pady=(0, 10))

    def create_reports_tab(self):
        """Cria aba de relatórios"""
        reports_frame = ttk.Frame(self.notebook)
        self.notebook.add(reports_frame, text="Relatórios")

        # Notebook para sub-relatórios
        reports_notebook = ttk.Notebook(reports_frame)
        reports_notebook.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        # Relatório por categoria
        category_frame = ttk.Frame(reports_notebook)
        reports_notebook.add(category_frame, text="Por Categoria")

        ttk.Label(category_frame, text="Relatório por Categoria",
                 font=("Arial", 14, "bold")).pack(pady=20)
        ttk.Label(category_frame, text="Funcionalidade em desenvolvimento",
                 foreground="gray").pack()

        # Fluxo de caixa
        cashflow_frame = ttk.Frame(reports_notebook)
        reports_notebook.add(cashflow_frame, text="Fluxo de Caixa")

        ttk.Label(cashflow_frame, text="Fluxo de Caixa",
                 font=("Arial", 14, "bold")).pack(pady=20)
        ttk.Label(cashflow_frame, text="Funcionalidade em desenvolvimento",
                 foreground="gray").pack()

        # Contas a vencer
        due_frame = ttk.Frame(reports_notebook)
        reports_notebook.add(due_frame, text="Contas a Vencer")

        ttk.Label(due_frame, text="Contas a Vencer",
                 font=("Arial", 14, "bold")).pack(pady=20)
        ttk.Label(due_frame, text="Funcionalidade em desenvolvimento",
                 foreground="gray").pack()

    def create_settings_tab(self):
        """Cria aba de configurações"""
        settings_frame = ttk.Frame(self.notebook)
        self.notebook.add(settings_frame, text="Configurações")

        main_frame = ttk.Frame(settings_frame, padding="20")
        main_frame.pack(fill=tk.BOTH, expand=True)

        ttk.Label(main_frame, text="Configurações do Sistema",
                 font=("Arial", 16, "bold")).pack(pady=(0, 20))

        # Seção Backup
        backup_section = ttk.LabelFrame(main_frame, text="Backup e Restauração", padding=10)
        backup_section.pack(fill=tk.X, pady=(0, 15))

        ttk.Button(backup_section, text="Criar Backup", command=self.backup_database).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(backup_section, text="Restaurar Backup", command=self.restore_database).pack(side=tk.LEFT)

        # Seção Usuário
        user_section = ttk.LabelFrame(main_frame, text="Conta do Usuário", padding=10)
        user_section.pack(fill=tk.X, pady=(0, 15))

        ttk.Label(user_section, text=f"Usuário: {self.current_user['full_name']}").pack(side=tk.LEFT)

        # Seção Sistema
        system_section = ttk.LabelFrame(main_frame, text="Sistema", padding=10)
        system_section.pack(fill=tk.X, pady=(0, 15))

        ttk.Label(system_section, text="Versão: 1.0.0 (Sem Dependências Externas)").pack(side=tk.LEFT)

    def create_admin_tab(self):
        """Cria aba de administração"""
        admin_frame = ttk.Frame(self.notebook)
        self.notebook.add(admin_frame, text="Administração")

        main_frame = ttk.Frame(admin_frame, padding="20")
        main_frame.pack(fill=tk.BOTH, expand=True)

        ttk.Label(main_frame, text="Administração do Sistema",
                 font=("Arial", 16, "bold")).pack(pady=(0, 20))

        # Estatísticas
        stats_section = ttk.LabelFrame(main_frame, text="Estatísticas", padding=10)
        stats_section.pack(fill=tk.X, pady=(0, 15))

        self.update_admin_stats(stats_section)

    def update_admin_stats(self, parent):
        """Atualiza estatísticas do admin"""
        try:
            conn = self.get_connection()
            cursor = conn.cursor()

            # Contar usuários
            cursor.execute("SELECT COUNT(*) FROM users")
            users_count = cursor.fetchone()[0]

            # Contar carteiras
            cursor.execute("SELECT COUNT(*) FROM wallets")
            wallets_count = cursor.fetchone()[0]

            # Contar transações
            cursor.execute("SELECT COUNT(*) FROM transactions")
            transactions_count = cursor.fetchone()[0]

            conn.close()

            stats_text = f"Usuários: {users_count} | Carteiras: {wallets_count} | Transações: {transactions_count}"
            ttk.Label(parent, text=stats_text).pack()

        except Exception as e:
            ttk.Label(parent, text=f"Erro ao carregar estatísticas: {str(e)}").pack()

    def load_initial_data(self):
        """Carrega dados iniciais"""
        self.update_summary_cards()
        self.load_recent_transactions()
        self.load_wallets()
        self.load_transactions()

    def update_summary_cards(self):
        """Atualiza cards de resumo"""
        try:
            conn = self.get_connection()
            cursor = conn.cursor()
            user_id = self.current_user['id']

            # Saldo total
            cursor.execute("SELECT SUM(current_balance) FROM wallets WHERE user_id = ? AND is_active = TRUE", (user_id,))
            total_balance = cursor.fetchone()[0] or 0
            self.total_balance_label.config(text=f"R$ {total_balance:,.2f}")

            # Receitas do mês
            current_month = datetime.now().strftime('%Y-%m')
            cursor.execute('''
                SELECT SUM(amount) FROM transactions
                WHERE user_id = ? AND transaction_type = 'income'
                AND strftime('%Y-%m', transaction_date) = ? AND is_paid = TRUE
            ''', (user_id, current_month))
            month_income = cursor.fetchone()[0] or 0
            self.month_income_label.config(text=f"R$ {month_income:,.2f}")

            # Despesas do mês
            cursor.execute('''
                SELECT SUM(amount) FROM transactions
                WHERE user_id = ? AND transaction_type = 'expense'
                AND strftime('%Y-%m', transaction_date) = ? AND is_paid = TRUE
            ''', (user_id, current_month))
            month_expense = cursor.fetchone()[0] or 0
            self.month_expense_label.config(text=f"R$ {month_expense:,.2f}")

            # Saldo do mês
            month_balance = month_income - month_expense
            color = 'green' if month_balance >= 0 else 'red'
            self.month_balance_label.config(text=f"R$ {month_balance:,.2f}", foreground=color)

            conn.close()
        except Exception as e:
            print(f"Erro ao atualizar resumo: {e}")

    def load_recent_transactions(self):
        """Carrega transações recentes"""
        try:
            for item in self.recent_tree.get_children():
                self.recent_tree.delete(item)

            conn = self.get_connection()
            cursor = conn.cursor()

            cursor.execute('''
                SELECT t.transaction_date, t.description, c.name, t.amount, t.transaction_type
                FROM transactions t
                JOIN categories c ON t.category_id = c.id
                WHERE t.user_id = ?
                ORDER BY t.created_at DESC
                LIMIT 10
            ''', (self.current_user['id'],))

            for trans in cursor.fetchall():
                date_str = trans[0]
                description = trans[1]
                category = trans[2]
                amount = f"R$ {trans[3]:,.2f}"
                trans_type = "Receita" if trans[4] == 'income' else "Despesa"

                item = self.recent_tree.insert('', 'end', values=(date_str, description, category, amount, trans_type))

                if trans[4] == 'income':
                    self.recent_tree.item(item, tags=('income',))
                else:
                    self.recent_tree.item(item, tags=('expense',))

            self.recent_tree.tag_configure('income', foreground='green')
            self.recent_tree.tag_configure('expense', foreground='red')

            conn.close()
        except Exception as e:
            print(f"Erro ao carregar transações recentes: {e}")

    def load_wallets(self):
        """Carrega carteiras"""
        try:
            for item in self.wallets_tree.get_children():
                self.wallets_tree.delete(item)

            conn = self.get_connection()
            cursor = conn.cursor()

            cursor.execute('''
                SELECT name, wallet_type, initial_balance, current_balance, is_active
                FROM wallets
                WHERE user_id = ?
                ORDER BY name
            ''', (self.current_user['id'],))

            for wallet in cursor.fetchall():
                wallet_type = {
                    'checking': 'Conta Corrente',
                    'savings': 'Poupança',
                    'credit': 'Cartão de Crédito',
                    'cash': 'Dinheiro'
                }.get(wallet[1], wallet[1])

                status = "Ativa" if wallet[4] else "Inativa"

                values = (
                    wallet[0],
                    wallet_type,
                    f"R$ {wallet[2]:,.2f}",
                    f"R$ {wallet[3]:,.2f}",
                    status
                )

                self.wallets_tree.insert('', 'end', values=values)

            conn.close()
        except Exception as e:
            print(f"Erro ao carregar carteiras: {e}")

    def load_transactions(self):
        """Carrega transações"""
        try:
            for item in self.transactions_tree.get_children():
                self.transactions_tree.delete(item)

            conn = self.get_connection()
            cursor = conn.cursor()

            cursor.execute('''
                SELECT t.transaction_date, t.description, c.name as category_name,
                       w.name as wallet_name, t.amount, t.transaction_type, t.is_paid
                FROM transactions t
                JOIN categories c ON t.category_id = c.id
                JOIN wallets w ON t.wallet_id = w.id
                WHERE t.user_id = ?
                ORDER BY t.transaction_date DESC
                LIMIT 100
            ''', (self.current_user['id'],))

            for trans in cursor.fetchall():
                date_str = datetime.strptime(trans[0], '%Y-%m-%d').strftime('%d/%m/%Y')
                trans_type = "Receita" if trans[5] == 'income' else "Despesa"
                status = "Pago" if trans[6] else "Pendente"

                values = (
                    date_str,
                    trans[1],
                    trans[2],
                    trans[3],
                    f"R$ {trans[4]:,.2f}",
                    trans_type,
                    status
                )

                item = self.transactions_tree.insert('', 'end', values=values)

                if trans[5] == 'income':
                    self.transactions_tree.item(item, tags=('income',))
                else:
                    self.transactions_tree.item(item, tags=('expense',))

            self.transactions_tree.tag_configure('income', foreground='green')
            self.transactions_tree.tag_configure('expense', foreground='red')

            conn.close()
        except Exception as e:
            print(f"Erro ao carregar transações: {e}")

    # Métodos de ação (simplificados)
    def new_wallet(self):
        """Cria nova carteira"""
        messagebox.showinfo("Funcionalidade", "Use a versão completa para criar carteiras")

    def edit_wallet(self):
        """Edita carteira"""
        messagebox.showinfo("Funcionalidade", "Use a versão completa para editar carteiras")

    def delete_wallet(self):
        """Exclui carteira"""
        messagebox.showinfo("Funcionalidade", "Use a versão completa para excluir carteiras")

    def new_income(self):
        """Nova receita"""
        messagebox.showinfo("Funcionalidade", "Use a versão completa para criar receitas")

    def new_expense(self):
        """Nova despesa"""
        messagebox.showinfo("Funcionalidade", "Use a versão completa para criar despesas")

    def edit_transaction(self):
        """Edita transação"""
        messagebox.showinfo("Funcionalidade", "Use a versão completa para editar transações")

    def delete_transaction(self):
        """Exclui transação"""
        messagebox.showinfo("Funcionalidade", "Use a versão completa para excluir transações")

    def backup_database(self):
        """Cria backup do banco"""
        try:
            backup_path = filedialog.asksaveasfilename(
                title="Salvar Backup",
                defaultextension=".db",
                filetypes=[("Banco de Dados", "*.db"), ("Todos os arquivos", "*.*")]
            )

            if backup_path:
                shutil.copy2(self.db_path, backup_path)
                messagebox.showinfo("Sucesso", "Backup criado com sucesso!")
        except Exception as e:
            messagebox.showerror("Erro", f"Erro ao criar backup: {str(e)}")

    def restore_database(self):
        """Restaura banco de dados"""
        if not messagebox.askyesno("Confirmar", "Esta operação substituirá todos os dados atuais. Continuar?"):
            return

        try:
            backup_path = filedialog.askopenfilename(
                title="Selecionar Backup",
                filetypes=[("Banco de Dados", "*.db"), ("Todos os arquivos", "*.*")]
            )

            if backup_path:
                shutil.copy2(backup_path, self.db_path)
                messagebox.showinfo("Sucesso", "Banco de dados restaurado com sucesso!")
                messagebox.showinfo("Reiniciar", "Reinicie a aplicação para ver as alterações")
        except Exception as e:
            messagebox.showerror("Erro", f"Erro ao restaurar backup: {str(e)}")

    def show_about(self):
        """Mostra informações sobre o sistema"""
        about_text = """Sistema de Gestão de Contas
Versão Sem Dependências Externas v1.0.0

Desenvolvido em Python com Tkinter
Funciona apenas com bibliotecas padrão do Python

Funcionalidades desta versão:
• Login de usuários
• Dashboard com resumo financeiro
• Visualização de carteiras
• Visualização de transações
• Backup e restauração básica

Para funcionalidades completas, use a versão principal.

© 2024 - Sistema de Gestão Financeira"""

        messagebox.showinfo("Sobre", about_text)

    def run(self):
        """Executa a aplicação"""
        try:
            print("Iniciando Sistema de Gestão de Contas (Sem Dependências)")
            print("=" * 60)
            print("✓ Usando apenas bibliotecas padrão do Python")
            print("✓ Banco de dados SQLite")
            print("✓ Interface Tkinter")
            print("✓ Login padrão: admin/admin123")
            print("=" * 60)

            self.show_login()
        except Exception as e:
            messagebox.showerror("Erro Fatal", f"Erro ao iniciar aplicação: {str(e)}")

def main():
    """Função principal"""
    try:
        app = GestaoContasSemDependencias()
        app.run()
    except Exception as e:
        print(f"Erro fatal: {str(e)}")
        input("Pressione Enter para sair...")

if __name__ == "__main__":
    main()
