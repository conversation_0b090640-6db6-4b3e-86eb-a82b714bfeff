#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Teste específico do formulário de transação para debug
"""

import tkinter as tk
from tkinter import ttk
import sys
import os

# Adicionar o diretório src ao path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from database import DatabaseManager
from gui.transaction_form_new import TransactionFormNew

def test_transaction_form():
    """Testa o formulário de transação com debug"""
    root = tk.Tk()
    root.title("Teste Formulário Transação - Debug")
    root.geometry("400x300")
    
    # Inicializar banco de dados
    db_manager = DatabaseManager()
    
    def test_income():
        """Testa formulário de receita"""
        print("🔍 Testando formulário de RECEITA...")
        form = TransactionFormNew(root, db_manager, 1, 'income')
        
        # Debug: verificar se todas as seções foram criadas
        print(f"📋 Seções criadas:")
        if hasattr(form, 'description_entry'):
            print("  ✅ Seção Básica: description_entry encontrado")
        else:
            print("  ❌ Seção Básica: description_entry NÃO encontrado")
            
        if hasattr(form, 'wallet_combo'):
            print("  ✅ Seção Financeira: wallet_combo encontrado")
        else:
            print("  ❌ Seção Financeira: wallet_combo NÃO encontrado")
            
        if hasattr(form, 'date_entry'):
            print("  ✅ Seção Datas: date_entry encontrado")
        else:
            print("  ❌ Seção Datas: date_entry NÃO encontrado")
            
        if hasattr(form, 'installments_entry'):
            print("  ✅ Seção Datas: installments_entry encontrado")
        else:
            print("  ❌ Seção Datas: installments_entry NÃO encontrado")
            
        if hasattr(form, 'paid_var'):
            print("  ✅ Seção Adicional: paid_var encontrado")
        else:
            print("  ❌ Seção Adicional: paid_var NÃO encontrado")
            
        if hasattr(form, 'notes_text'):
            print("  ✅ Seção Adicional: notes_text encontrado")
        else:
            print("  ❌ Seção Adicional: notes_text NÃO encontrado")
        
        # Verificar se o canvas e scroll estão funcionando
        if hasattr(form, 'canvas'):
            print("  ✅ Canvas encontrado")
            if hasattr(form, 'scrollable_frame'):
                print("  ✅ Scrollable frame encontrado")
                # Verificar quantos filhos o scrollable_frame tem
                children = form.scrollable_frame.winfo_children()
                print(f"  📊 Scrollable frame tem {len(children)} filhos")
                for i, child in enumerate(children):
                    print(f"    {i+1}. {child.__class__.__name__}: {child.cget('text') if hasattr(child, 'cget') else 'N/A'}")
            else:
                print("  ❌ Scrollable frame NÃO encontrado")
        else:
            print("  ❌ Canvas NÃO encontrado")
        
        root.wait_window(form.window)
        print(f"📊 Resultado: {form.result}")
    
    def test_expense():
        """Testa formulário de despesa"""
        print("🔍 Testando formulário de DESPESA...")
        form = TransactionFormNew(root, db_manager, 1, 'expense')
        
        # Debug: verificar se todas as seções foram criadas
        print(f"📋 Seções criadas:")
        if hasattr(form, 'description_entry'):
            print("  ✅ Seção Básica: description_entry encontrado")
        else:
            print("  ❌ Seção Básica: description_entry NÃO encontrado")
            
        if hasattr(form, 'wallet_combo'):
            print("  ✅ Seção Financeira: wallet_combo encontrado")
        else:
            print("  ❌ Seção Financeira: wallet_combo NÃO encontrado")
            
        if hasattr(form, 'date_entry'):
            print("  ✅ Seção Datas: date_entry encontrado")
        else:
            print("  ❌ Seção Datas: date_entry NÃO encontrado")
            
        if hasattr(form, 'installments_entry'):
            print("  ✅ Seção Datas: installments_entry encontrado")
        else:
            print("  ❌ Seção Datas: installments_entry NÃO encontrado")
            
        if hasattr(form, 'paid_var'):
            print("  ✅ Seção Adicional: paid_var encontrado")
        else:
            print("  ❌ Seção Adicional: paid_var NÃO encontrado")
            
        if hasattr(form, 'notes_text'):
            print("  ✅ Seção Adicional: notes_text encontrado")
        else:
            print("  ❌ Seção Adicional: notes_text NÃO encontrado")
        
        # Verificar se o canvas e scroll estão funcionando
        if hasattr(form, 'canvas'):
            print("  ✅ Canvas encontrado")
            if hasattr(form, 'scrollable_frame'):
                print("  ✅ Scrollable frame encontrado")
                # Verificar quantos filhos o scrollable_frame tem
                children = form.scrollable_frame.winfo_children()
                print(f"  📊 Scrollable frame tem {len(children)} filhos")
                for i, child in enumerate(children):
                    print(f"    {i+1}. {child.__class__.__name__}: {child.cget('text') if hasattr(child, 'cget') else 'N/A'}")
            else:
                print("  ❌ Scrollable frame NÃO encontrado")
        else:
            print("  ❌ Canvas NÃO encontrado")
        
        root.wait_window(form.window)
        print(f"📊 Resultado: {form.result}")
    
    # Interface de teste
    ttk.Label(root, text="Teste Formulário Transação - Debug", 
             font=("Arial", 14, "bold")).pack(pady=20)
    
    ttk.Button(root, text="🔍 Testar Receita (Debug)", 
              command=test_income).pack(pady=10)
    
    ttk.Button(root, text="🔍 Testar Despesa (Debug)", 
              command=test_expense).pack(pady=10)
    
    ttk.Button(root, text="🚪 Sair", command=root.quit).pack(pady=20)
    
    print("🔍 TESTE DE DEBUG DO FORMULÁRIO DE TRANSAÇÃO")
    print("=" * 50)
    print("Este teste irá verificar se todas as seções estão sendo criadas corretamente")
    print("Observe o console para informações de debug")
    
    root.mainloop()

if __name__ == "__main__":
    test_transaction_form()
