#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Execução via terminal - versão corrigida para executável
"""

import sys
import os
import time
import io

# Configurar codificação para Windows
if sys.platform.startswith('win'):
    # Configurar stdout para UTF-8
    sys.stdout = io.TextIOWrapper(sys.stdout.buffer, encoding='utf-8', errors='replace')
    sys.stderr = io.TextIOWrapper(sys.stderr.buffer, encoding='utf-8', errors='replace')

def main():
    """Executa a aplicação diretamente"""
    try:
        print("=" * 70)
        print("           SISTEMA DE GESTAO DE CONTAS")
        print("           Versao 2.0 - Com Gerenciamento de Veiculos")
        print("=" * 70)
        print()
        
        print("Funcionalidades disponiveis:")
        print("   - Controle financeiro completo")
        print("   - Gerenciamento de carteiras")
        print("   - Transacoes (receitas e despesas)")
        print("   - Relatorios financeiros")
        print("   - NOVO: Cadastro de veiculos")
        print("   - NOVO: Controle de manutencao")
        print("   - NOVO: Registro de combustivel")
        print("   - NOVO: Estatisticas de consumo")
        print()
        
        print("Credenciais padrao:")
        print("   Usuario: admin")
        print("   Senha: admin123")
        print()
        
        print("Iniciando aplicacao...")
        print("-" * 70)
        
        # Verificar se os módulos existem
        print("Verificando modulos...")
        
        try:
            import tkinter as tk
            print("   tkinter - OK")
        except ImportError:
            print("   tkinter - ERRO: Nao encontrado")
            input("Pressione Enter para sair...")
            return
        
        try:
            import sqlite3
            print("   sqlite3 - OK")
        except ImportError:
            print("   sqlite3 - ERRO: Nao encontrado")
            input("Pressione Enter para sair...")
            return
        
        # Verificar arquivos do projeto
        print("Verificando arquivos...")
        
        arquivos = ['main.py', 'src/database.py', 'src/auth.py', 'src/gui/main_window.py', 'src/gui/login_window.py']
        
        for arquivo in arquivos:
            if os.path.exists(arquivo):
                print(f"   {arquivo} - OK")
            else:
                print(f"   {arquivo} - ERRO: Nao encontrado")
                input("Pressione Enter para sair...")
                return
        
        print()
        print("Inicializando banco de dados...")
        
        # Importar e inicializar
        from src.database import DatabaseManager
        from src.auth import AuthManager
        from src.modules.auto_update_manager import AutoUpdateManager

        db_manager = DatabaseManager()
        db_manager.initialize_database()

        auth_manager = AuthManager(db_manager)

        # Inicializar sistema de atualizações automáticas
        auto_update_manager = None
        try:
            auto_update_manager = AutoUpdateManager(db_manager)
            print("   Sistema de atualizacoes automaticas inicializado")
        except Exception as e:
            print(f"   Erro ao inicializar atualizacoes automaticas: {e}")
        
        # Criar usuário admin se não existir
        if not auth_manager.user_exists('admin'):
            auth_manager.create_user(
                username='admin',
                password='admin123',
                email='<EMAIL>',
                is_admin=True,
                full_name='Administrador do Sistema'
            )
            print("   Usuario admin criado")
        else:
            print("   Usuario admin ja existe")
        
        print()
        print("Abrindo interface grafica...")
        print("   Uma janela deve aparecer na sua tela!")
        print()
        
        # Criar interface gráfica
        root = tk.Tk()
        root.title("Sistema de Gestao de Contas")
        root.geometry("800x800")
        
        try:
            root.state('zoomed')  # Maximizar
        except:
            pass  # Ignorar se não suportar
        
        # Forçar janela aparecer
        root.lift()
        try:
            root.attributes('-topmost', True)
        except:
            pass
        root.focus_force()
        
        # Importar janelas
        from src.gui.login_window import LoginWindow
        from src.gui.main_window import MainWindow
        
        # Esconder janela principal inicialmente
        root.withdraw()
        
        def on_login_success(user_data):
            """Callback após login bem-sucedido"""
            print(f"Login realizado: {user_data['full_name']}")

            # Iniciar sistema de atualizações automáticas
            if auto_update_manager:
                try:
                    auto_update_manager.start()
                    print("   Sistema de atualizacoes automaticas iniciado")
                except Exception as e:
                    print(f"   Erro ao iniciar atualizacoes automaticas: {e}")

            # Mostrar janela principal
            root.deiconify()
            try:
                root.attributes('-topmost', False)
            except:
                pass

            # Criar interface principal
            MainWindow(root, db_manager, auth_manager, user_data, on_logout, auto_update_manager)
        
        def on_logout():
            """Callback no logout"""
            print("Logout realizado")

            # Parar sistema de atualizações automáticas
            if auto_update_manager:
                try:
                    auto_update_manager.stop()
                    print("   Sistema de atualizacoes automaticas parado")
                except Exception as e:
                    print(f"   Erro ao parar atualizacoes automaticas: {e}")

            root.withdraw()
            show_login()
        
        def show_login():
            """Mostra tela de login"""
            LoginWindow(root, auth_manager, on_login_success)
        
        # Mostrar login
        show_login()
        
        print("Interface criada com sucesso!")
        print("Procure a janela de login na sua tela")
        print()
        print("Dicas:")
        print("   - Verifique a barra de tarefas")
        print("   - Use Alt+Tab para alternar janelas")
        print("   - A janela pode estar atras de outras")
        print()
        print("Aguardando interacao do usuario...")
        print("   (Para encerrar, pressione Ctrl+C)")
        
        # Iniciar loop principal
        root.mainloop()
        
        print("\nAplicacao encerrada normalmente")
        
    except KeyboardInterrupt:
        print("\n\nAplicacao interrompida pelo usuario")
        
    except Exception as e:
        print(f"\nERRO: {str(e)}")
        print("\nDetalhes do erro:")
        import traceback
        traceback.print_exc()
        
        print("\nPossiveis solucoes:")
        print("   1. Verifique se o Python esta instalado corretamente")
        print("   2. Instale as dependencias: py -m pip install -r requirements.txt")
        print("   3. Verifique se todos os arquivos estao presentes")
        
        input("\nPressione Enter para sair...")

if __name__ == "__main__":
    main()
