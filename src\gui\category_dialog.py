#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Diálogo para criar/editar categorias
"""

import tkinter as tk
from tkinter import ttk, messagebox, colorchooser
from src.modules.category_manager import CategoryManager

class CategoryDialog:
    def __init__(self, parent, db_manager, user_id, category_id=None):
        self.parent = parent
        self.db_manager = db_manager
        self.user_id = user_id
        self.category_id = category_id
        self.category_manager = CategoryManager(db_manager)
        self.result = False
        self.selected_color = "#007ACC"
        
        # Criar janela
        self.window = tk.Toplevel(parent)
        self.window.title("Nova Categoria" if not category_id else "Editar Categoria")
        self.window.geometry("500x450")
        self.window.resizable(False, False)
        
        # Centralizar janela
        self.center_window()
        
        # Configurar janela
        self.window.transient(parent)
        self.window.grab_set()
        
        # Criar interface
        self.create_widgets()
        
        # Carregar dados se for edição
        if category_id:
            self.load_category_data()
        
        # Focar no primeiro campo
        self.name_entry.focus()
    
    def center_window(self):
        """Centraliza a janela na tela"""
        self.window.update_idletasks()
        width = self.window.winfo_width()
        height = self.window.winfo_height()
        x = (self.window.winfo_screenwidth() // 2) - (width // 2)
        y = (self.window.winfo_screenheight() // 2) - (height // 2)
        self.window.geometry(f"{width}x{height}+{x}+{y}")
    
    def create_widgets(self):
        """Cria os widgets da interface"""
        # Frame principal
        main_frame = ttk.Frame(self.window)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)
        
        # Título
        title_text = "Nova Categoria" if not self.category_id else "Editar Categoria"
        title_label = ttk.Label(main_frame, text=title_text, font=('Arial', 16, 'bold'))
        title_label.pack(pady=(0, 20))
        
        # Frame do formulário
        form_frame = ttk.Frame(main_frame)
        form_frame.pack(fill=tk.BOTH, expand=True)
        
        # Nome da categoria
        ttk.Label(form_frame, text="Nome da Categoria *:").pack(anchor=tk.W, pady=(0, 5))
        self.name_entry = ttk.Entry(form_frame, font=("Arial", 11))
        self.name_entry.pack(fill=tk.X, pady=(0, 15))
        
        # Tipo da categoria
        ttk.Label(form_frame, text="Tipo *:").pack(anchor=tk.W, pady=(0, 5))
        self.type_combo = ttk.Combobox(form_frame, values=["Receita", "Despesa"], 
                                      state="readonly", font=("Arial", 11))
        self.type_combo.set("Despesa")
        self.type_combo.pack(fill=tk.X, pady=(0, 15))
        
        # Descrição
        ttk.Label(form_frame, text="Descrição:").pack(anchor=tk.W, pady=(0, 5))
        self.description_entry = ttk.Entry(form_frame, font=("Arial", 11))
        self.description_entry.pack(fill=tk.X, pady=(0, 15))
        
        # Cor
        color_frame = ttk.Frame(form_frame)
        color_frame.pack(fill=tk.X, pady=(0, 15))
        
        ttk.Label(color_frame, text="Cor:").pack(anchor=tk.W, pady=(0, 5))
        
        color_select_frame = ttk.Frame(color_frame)
        color_select_frame.pack(fill=tk.X)
        
        self.color_canvas = tk.Canvas(color_select_frame, width=40, height=25, 
                                     bg=self.selected_color, relief=tk.RAISED, bd=2)
        self.color_canvas.pack(side=tk.LEFT, padx=(0, 10))
        
        ttk.Button(color_select_frame, text="Escolher Cor", 
                  command=self.choose_color).pack(side=tk.LEFT)
        
        self.color_label = ttk.Label(color_select_frame, text=self.selected_color)
        self.color_label.pack(side=tk.LEFT, padx=(10, 0))
        
        # Ícone
        ttk.Label(form_frame, text="Ícone (emoji ou símbolo):").pack(anchor=tk.W, pady=(0, 5))
        self.icon_entry = ttk.Entry(form_frame, font=("Arial", 11))
        self.icon_entry.pack(fill=tk.X, pady=(0, 15))
        
        # Sugestões de ícones
        suggestions_frame = ttk.LabelFrame(form_frame, text="Sugestões de Ícones")
        suggestions_frame.pack(fill=tk.X, pady=(0, 15))
        
        # Ícones para receitas
        income_frame = ttk.Frame(suggestions_frame)
        income_frame.pack(fill=tk.X, pady=5)
        ttk.Label(income_frame, text="Receitas:", font=('Arial', 9, 'bold')).pack(side=tk.LEFT)
        
        income_icons = ["💰", "💵", "💸", "📈", "🏦", "💳", "🎯", "⭐"]
        for icon in income_icons:
            btn = tk.Button(income_frame, text=icon, width=3, height=1,
                           command=lambda i=icon: self.set_icon(i))
            btn.pack(side=tk.LEFT, padx=2)
        
        # Ícones para despesas
        expense_frame = ttk.Frame(suggestions_frame)
        expense_frame.pack(fill=tk.X, pady=5)
        ttk.Label(expense_frame, text="Despesas:", font=('Arial', 9, 'bold')).pack(side=tk.LEFT)
        
        expense_icons = ["🏠", "🚗", "🍔", "⚡", "💊", "🎓", "👕", "🎮"]
        for icon in expense_icons:
            btn = tk.Button(expense_frame, text=icon, width=3, height=1,
                           command=lambda i=icon: self.set_icon(i))
            btn.pack(side=tk.LEFT, padx=2)
        
        # Frame dos botões
        button_frame = ttk.Frame(main_frame)
        button_frame.pack(fill=tk.X, pady=(20, 0))
        
        # Botão cancelar
        cancel_btn = ttk.Button(button_frame, text="Cancelar", command=self.cancel)
        cancel_btn.pack(side=tk.LEFT, padx=(0, 10))
        
        # Botão salvar
        save_text = "Criar Categoria" if not self.category_id else "Salvar Alterações"
        save_btn = ttk.Button(button_frame, text=save_text, command=self.save)
        save_btn.pack(side=tk.RIGHT)
        
        # Bind Enter para salvar
        self.window.bind('<Return>', lambda e: self.save())
        self.window.bind('<Escape>', lambda e: self.cancel())
    
    def set_icon(self, icon):
        """Define o ícone selecionado"""
        self.icon_entry.delete(0, tk.END)
        self.icon_entry.insert(0, icon)
    
    def choose_color(self):
        """Abre seletor de cor"""
        color = colorchooser.askcolor(color=self.selected_color, title="Escolher Cor")
        if color[1]:  # Se uma cor foi selecionada
            self.selected_color = color[1]
            self.color_canvas.config(bg=self.selected_color)
            self.color_label.config(text=self.selected_color)
    
    def load_category_data(self):
        """Carrega dados da categoria para edição"""
        try:
            category = self.category_manager.get_category_by_id(self.category_id, self.user_id)
            if category:
                self.name_entry.insert(0, category['name'])
                self.description_entry.insert(0, category['description'] or '')
                
                # Tipo
                tipo = "Receita" if category['category_type'] == 'income' else "Despesa"
                self.type_combo.set(tipo)
                
                # Cor
                if category['color']:
                    self.selected_color = category['color']
                    self.color_canvas.config(bg=self.selected_color)
                    self.color_label.config(text=self.selected_color)
                
                # Ícone
                if category['icon']:
                    self.icon_entry.insert(0, category['icon'])
                
        except Exception as e:
            messagebox.showerror("Erro", f"Erro ao carregar dados da categoria: {str(e)}")
    
    def validate_form(self):
        """Valida os dados do formulário"""
        name = self.name_entry.get().strip()
        if not name:
            messagebox.showerror("Erro", "Nome da categoria é obrigatório")
            self.name_entry.focus()
            return False
        
        if len(name) > 100:
            messagebox.showerror("Erro", "Nome da categoria deve ter no máximo 100 caracteres")
            self.name_entry.focus()
            return False
        
        return True
    
    def save(self):
        """Salva a categoria"""
        if not self.validate_form():
            return
        
        try:
            name = self.name_entry.get().strip()
            description = self.description_entry.get().strip()
            category_type = 'income' if self.type_combo.get() == 'Receita' else 'expense'
            icon = self.icon_entry.get().strip()
            
            if self.category_id:
                # Editar categoria existente
                self.category_manager.update_category(
                    self.category_id,
                    self.user_id,
                    name=name,
                    description=description,
                    color=self.selected_color,
                    icon=icon
                )
                messagebox.showinfo("Sucesso", "Categoria atualizada com sucesso!")
            else:
                # Criar nova categoria
                self.category_manager.create_category(
                    self.user_id,
                    name=name,
                    description=description,
                    category_type=category_type,
                    color=self.selected_color,
                    icon=icon
                )
                messagebox.showinfo("Sucesso", "Categoria criada com sucesso!")
            
            self.result = True
            self.window.destroy()
            
        except Exception as e:
            messagebox.showerror("Erro", str(e))
    
    def cancel(self):
        """Cancela a operação"""
        self.result = False
        self.window.destroy()
