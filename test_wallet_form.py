#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Teste do formulário de nova carteira
"""

import sys
import os
import tkinter as tk

# Adicionar o diretório src ao path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from database import DatabaseManager
from auth import AuthManager
from gui.wallet_form_new import WalletFormNew

def test_wallet_form():
    """Testa o formulário de carteira"""
    print("💳 TESTE DO FORMULÁRIO DE CARTEIRA")
    print("=" * 50)
    
    try:
        # Inicializar banco de dados
        db_manager = DatabaseManager()
        db_manager.initialize_database()
        
        auth_manager = AuthManager(db_manager)
        
        # Fazer login
        user_data = auth_manager.authenticate_user('admin', 'admin123')
        if not user_data:
            print("❌ Falha no login!")
            return
        
        print(f"✅ Login realizado: {user_data['full_name']}")
        
        # Criar janela principal
        root = tk.Tk()
        root.title("Teste Formulário Carteira")
        root.geometry("1000x700")
        
        print("✅ Janela principal criada")
        
        # Testar formulário de nova carteira
        print("\n🔧 Testando formulário de nova carteira...")
        try:
            form = WalletFormNew(
                root, 
                db_manager, 
                user_data['id']
            )
            
            print("✅ Formulário de carteira criado!")
            print(f"📐 Tamanho da janela: {form.window.geometry()}")
            
            # Verificar botões após renderização
            def check_buttons():
                try:
                    form.window.update_idletasks()
                    
                    # Obter dimensões da janela
                    window_width = form.window.winfo_width()
                    window_height = form.window.winfo_height()
                    
                    print(f"📏 Dimensões da janela: {window_width}x{window_height}")
                    
                    # Procurar botões
                    buttons = []
                    def find_buttons(widget):
                        if isinstance(widget, tk.Button):
                            try:
                                button_info = {
                                    'text': widget['text'],
                                    'width': widget.winfo_width(),
                                    'height': widget.winfo_height(),
                                    'x': widget.winfo_x(),
                                    'y': widget.winfo_y()
                                }
                                buttons.append(button_info)
                            except:
                                pass
                        for child in widget.winfo_children():
                            find_buttons(child)
                    
                    find_buttons(form.window)
                    
                    print(f"🔍 Botões encontrados: {len(buttons)}")
                    for btn in buttons:
                        print(f"   - Texto: '{btn['text']}'")
                        print(f"     Dimensões: {btn['width']}x{btn['height']}")
                        print(f"     Posição: ({btn['x']}, {btn['y']})")
                        
                        # Verificar se o botão está dentro da janela
                        if btn['x'] + btn['width'] > window_width:
                            print(f"     ⚠️  Botão pode estar cortado horizontalmente!")
                        elif btn['width'] < 50:
                            print(f"     ⚠️  Botão pode estar muito estreito!")
                        else:
                            print(f"     ✅ Botão parece estar bem dimensionado!")
                    
                except Exception as e:
                    print(f"❌ Erro ao verificar botões: {str(e)}")
            
            # Aguardar renderização e verificar
            root.after(1000, check_buttons)
            
            # Fechar após teste
            root.after(4000, form.window.destroy)
            
        except Exception as e:
            print(f"❌ Erro ao criar formulário: {str(e)}")
            import traceback
            traceback.print_exc()
        
        print("\n✅ TESTE DO FORMULÁRIO CONCLUÍDO!")
        
        # Executar por tempo limitado
        root.after(5000, root.quit)
        root.mainloop()
        root.destroy()
        
    except Exception as e:
        print(f"❌ ERRO GERAL: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_wallet_form()
