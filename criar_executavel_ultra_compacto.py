#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Cria executável com formulários ultra-compactos e otimizados
"""

import subprocess
import sys
import shutil
import zipfile
from pathlib import Path
import time

def check_dependencies():
    """Verifica e instala dependências necessárias"""
    dependencies = ['pyinstaller', 'tkcalendar']
    
    for dep in dependencies:
        try:
            __import__(dep)
            print(f"✓ {dep} encontrado")
        except ImportError:
            print(f"{dep} não encontrado. Instalando...")
            try:
                subprocess.check_call([sys.executable, "-m", "pip", "install", dep])
                print(f"✓ {dep} instalado com sucesso")
            except:
                print(f"✗ Erro ao instalar {dep}")
                return False
    
    return True

def create_executable():
    """Cria o executável ultra-compacto"""
    print("Criando executável GestaoContasUltraCompacto.exe...")
    
    try:
        # Comando para criar executável
        cmd = [
            sys.executable, "-m", "PyInstaller",
            "--onefile",
            "--windowed",
            "--name=GestaoContasUltraCompacto",
            "--icon=NONE",
            "gestao_contas_corrigido.py"
        ]
        
        print("Executando PyInstaller...")
        result = subprocess.run(cmd, capture_output=True, text=True)
        
        if result.returncode == 0:
            exe_path = Path("dist/GestaoContasUltraCompacto.exe")
            if exe_path.exists():
                size_mb = exe_path.stat().st_size / (1024 * 1024)
                print(f"✓ Executável criado: {exe_path}")
                print(f"✓ Tamanho: {size_mb:.1f} MB")
                return True
            else:
                print("✗ Executável não encontrado após criação")
                return False
        else:
            print("✗ Erro ao criar executável:")
            print(result.stderr)
            return False
            
    except Exception as e:
        print(f"✗ Erro: {str(e)}")
        return False

def create_distribution_package():
    """Cria pacote de distribuição ultra-compacto"""
    print("\nCriando pacote de distribuição ultra-compacto...")
    
    try:
        # Aguardar um pouco para liberar arquivos
        time.sleep(2)
        
        # Criar diretório de distribuição
        dist_dir = Path("GestaoContasUltraCompacto_v1.0.0")
        if dist_dir.exists():
            shutil.rmtree(dist_dir)
        
        dist_dir.mkdir()
        
        # Copiar executável
        exe_source = Path("dist/GestaoContasUltraCompacto.exe")
        if exe_source.exists():
            shutil.copy2(exe_source, dist_dir / "GestaoContasUltraCompacto.exe")
            print("✓ Executável copiado")
        else:
            print("✗ Executável não encontrado")
            return False
        
        # Criar diretórios necessários
        (dist_dir / "data").mkdir()
        (dist_dir / "backups").mkdir()
        print("✓ Diretórios criados")
        
        # Copiar script de atalho corrigido
        atalho_source = Path("criar_atalho_desktop.py")
        if atalho_source.exists():
            shutil.copy2(atalho_source, dist_dir / "Criar Atalho na Área de Trabalho.py")
            print("✓ Script de atalho corrigido copiado")
        
        # Criar arquivo .bat para atalho
        bat_content = '''@echo off
echo Criando atalho na área de trabalho...
python "Criar Atalho na Área de Trabalho.py"
pause'''
        
        with open(dist_dir / "🔗 Criar Atalho na Área de Trabalho.bat", 'w', encoding='utf-8') as f:
            f.write(bat_content)
        print("✓ Arquivo .bat de atalho criado")
        
        print("✓ Pacote criado com sucesso!")
        return True
        
    except Exception as e:
        print(f"✗ Erro ao criar pacote: {str(e)}")
        return False

def main():
    """Função principal"""
    print("CRIADOR DE EXECUTÁVEL - FORMULÁRIOS ULTRA-COMPACTOS")
    print("Sistema com Máxima Compactação + Todos os Componentes Visíveis")
    print("=" * 70)
    
    # Verificar dependências
    if not check_dependencies():
        print("\n✗ Não foi possível instalar todas as dependências")
        print("Alternativa: Use 'python gestao_contas_corrigido.py'")
        input("Pressione Enter para sair...")
        return
    
    # Criar executável
    if not create_executable():
        print("\n✗ Falha na criação do executável")
        print("Alternativa: Use 'python gestao_contas_corrigido.py'")
        input("Pressione Enter para sair...")
        return
    
    # Criar pacote de distribuição
    if not create_distribution_package():
        print("\n✗ Falha na criação do pacote")
        input("Pressione Enter para sair...")
        return
    
    print("\n" + "=" * 70)
    print("🎉 EXECUTÁVEL ULTRA-COMPACTO CRIADO!")
    print("\n📦 ARQUIVOS GERADOS:")
    print("  • dist/GestaoContasUltraCompacto.exe (executável)")
    print("  • GestaoContasUltraCompacto_v1.0.0/ (pasta completa)")
    
    print("\n🚀 COMO USAR:")
    print("  1. Acesse: GestaoContasUltraCompacto_v1.0.0/")
    print("  2. Execute: GestaoContasUltraCompacto.exe")
    print("  3. Login: admin/admin123")
    print("  4. Teste os formulários ultra-compactos!")
    
    print("\n🆕 FORMULÁRIOS ULTRA-COMPACTOS:")
    print("  • 👥 Formulário de Usuário: 550x450px (ultra-compacto)")
    print("  • 💳 Formulário de Carteira: 500x400px (ultra-compacto)")
    print("  • 💸 Formulário de Transação: 680x550px (ultra-compacto)")
    print("  • 📝 Fontes: 8pt (máxima compactação)")
    print("  • 📐 Padding: 8px (mínimo necessário)")
    print("  • 🎯 Campos: ipady=1-2 (altura mínima)")
    print("  • 📊 Layout: 4 linhas organizadas")
    print("  • ✅ TODOS OS COMPONENTES CABEM PERFEITAMENTE")
    
    print("\n📐 LAYOUT ULTRA-OTIMIZADO:")
    print("  • Linha 1: Descrição (70%) + Valor (30%)")
    print("  • Linha 2: Carteira (50%) + Categoria (50%)")
    print("  • Linha 3: Data (35%) + Vencimento (35%) + Parcelas (30%)")
    print("  • Linha 4: Status (30%) + Observações (70%)")
    print("  • Cabeçalho: 35px (mínimo)")
    print("  • Botões: 40px (compactos)")
    
    print("\n✅ FUNCIONALIDADES COMPLETAS:")
    print("  • 📊 Dashboard financeiro moderno")
    print("  • 💳 Gestão completa de carteiras")
    print("  • 💸 Transações avançadas com parcelas")
    print("  • 📅 Controle de vencimentos")
    print("  • 👥 Sistema completo de usuários")
    print("  • 🛡️ Administração avançada")
    print("  • 📊 5 tipos de relatórios funcionais")
    print("  • ⚙️ Backup e restauração")
    print("  • ✏️ Botões de editar funcionando")
    print("  • 🔄 Atualização automática")
    print("  • 🎨 Interface moderna e profissional")
    print("  • 📊 Linhas alternadas em todas as listas")
    print("  • 📅 Calendários nos campos de data")
    
    print("\n🎯 FORMULÁRIOS ULTRA-COMPACTOS PERFEITOS!")
    print("=" * 70)
    
    input("\nPressione Enter para sair...")

if __name__ == "__main__":
    main()
