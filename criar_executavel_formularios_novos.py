#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Cria executável com formulários completamente novos
Criados do zero com design moderno
"""

import subprocess
import sys
import shutil
import zipfile
from pathlib import Path
import time

def check_dependencies():
    """Verifica e instala dependências necessárias"""
    dependencies = ['pyinstaller', 'tkcalendar']
    
    for dep in dependencies:
        try:
            __import__(dep)
            print(f"✓ {dep} encontrado")
        except ImportError:
            print(f"{dep} não encontrado. Instalando...")
            try:
                subprocess.check_call([sys.executable, "-m", "pip", "install", dep])
                print(f"✓ {dep} instalado com sucesso")
            except:
                print(f"✗ Erro ao instalar {dep}")
                return False
    
    return True

def create_executable():
    """Cria o executável com formulários novos"""
    print("Criando executável GestaoContasFormulariosNovos.exe...")
    
    try:
        # Comando para criar executável
        cmd = [
            sys.executable, "-m", "PyInstaller",
            "--onefile",
            "--windowed",
            "--name=GestaoContasFormulariosNovos",
            "--icon=NONE",
            "gestao_contas_corrigido.py"
        ]
        
        print("Executando PyInstaller...")
        result = subprocess.run(cmd, capture_output=True, text=True)
        
        if result.returncode == 0:
            exe_path = Path("dist/GestaoContasFormulariosNovos.exe")
            if exe_path.exists():
                size_mb = exe_path.stat().st_size / (1024 * 1024)
                print(f"✓ Executável criado: {exe_path}")
                print(f"✓ Tamanho: {size_mb:.1f} MB")
                return True
            else:
                print("✗ Executável não encontrado após criação")
                return False
        else:
            print("✗ Erro ao criar executável:")
            print(result.stderr)
            return False
            
    except Exception as e:
        print(f"✗ Erro: {str(e)}")
        return False

def create_distribution_package():
    """Cria pacote de distribuição com formulários novos"""
    print("\nCriando pacote de distribuição com formulários novos...")
    
    try:
        # Aguardar um pouco para liberar arquivos
        time.sleep(2)
        
        # Criar diretório de distribuição
        dist_dir = Path("GestaoContasFormulariosNovos_v1.0.0")
        if dist_dir.exists():
            shutil.rmtree(dist_dir)
        
        dist_dir.mkdir()
        
        # Copiar executável
        exe_source = Path("dist/GestaoContasFormulariosNovos.exe")
        if exe_source.exists():
            shutil.copy2(exe_source, dist_dir / "GestaoContasFormulariosNovos.exe")
            print("✓ Executável copiado")
        else:
            print("✗ Executável não encontrado")
            return False
        
        # Criar diretórios necessários
        (dist_dir / "data").mkdir()
        (dist_dir / "backups").mkdir()
        (dist_dir / "src").mkdir()
        (dist_dir / "src" / "gui").mkdir()
        (dist_dir / "src" / "modules").mkdir()
        print("✓ Diretórios criados")
        
        # Copiar formulários novos
        new_forms = [
            ("src/gui/transaction_form_new.py", "src/gui/transaction_form_new.py"),
            ("src/gui/user_form_new.py", "src/gui/user_form_new.py"),
            ("src/gui/wallet_form_new.py", "src/gui/wallet_form_new.py"),
            ("teste_formularios_novos.py", "🎉 Teste Formulários Novos.py"),
        ]
        
        for src, dst in new_forms:
            src_path = Path(src)
            if src_path.exists():
                shutil.copy2(src_path, dist_dir / dst)
                print(f"✓ {src} copiado")
        
        # Copiar script de atalho corrigido
        atalho_source = Path("criar_atalho_desktop.py")
        if atalho_source.exists():
            shutil.copy2(atalho_source, dist_dir / "Criar Atalho na Área de Trabalho.py")
            print("✓ Script de atalho corrigido copiado")
        
        # Criar arquivo .bat para atalho
        bat_content = '''@echo off
echo Criando atalho na área de trabalho...
python "Criar Atalho na Área de Trabalho.py"
pause'''
        
        with open(dist_dir / "🔗 Criar Atalho na Área de Trabalho.bat", 'w', encoding='utf-8') as f:
            f.write(bat_content)
        print("✓ Arquivo .bat de atalho criado")
        
        print("✓ Pacote criado com sucesso!")
        return True
        
    except Exception as e:
        print(f"✗ Erro ao criar pacote: {str(e)}")
        return False

def main():
    """Função principal"""
    print("CRIADOR DE EXECUTÁVEL - FORMULÁRIOS COMPLETAMENTE NOVOS")
    print("Sistema com Formulários Criados do Zero + Design Moderno + Funcionalidades Avançadas")
    print("=" * 90)
    
    # Verificar dependências
    if not check_dependencies():
        print("\n✗ Não foi possível instalar todas as dependências")
        print("Alternativa: Use 'python gestao_contas_corrigido.py'")
        input("Pressione Enter para sair...")
        return
    
    # Criar executável
    if not create_executable():
        print("\n✗ Falha na criação do executável")
        print("Alternativa: Use 'python gestao_contas_corrigido.py'")
        input("Pressione Enter para sair...")
        return
    
    # Criar pacote de distribuição
    if not create_distribution_package():
        print("\n✗ Falha na criação do pacote")
        input("Pressione Enter para sair...")
        return
    
    print("\n" + "=" * 90)
    print("🎉 EXECUTÁVEL COM FORMULÁRIOS COMPLETAMENTE NOVOS CRIADO!")
    print("\n📦 ARQUIVOS GERADOS:")
    print("  • dist/GestaoContasFormulariosNovos.exe (executável)")
    print("  • GestaoContasFormulariosNovos_v1.0.0/ (pasta completa)")
    
    print("\n🚀 COMO USAR:")
    print("  1. Acesse: GestaoContasFormulariosNovos_v1.0.0/")
    print("  2. Execute: GestaoContasFormulariosNovos.exe")
    print("  3. Login: admin/admin123")
    print("  4. Teste: 🎉 Teste Formulários Novos.py")
    
    print("\n🆕 FORMULÁRIOS COMPLETAMENTE NOVOS:")
    print("  • 📈 Transação: Criado do zero (800x900px)")
    print("    - 📋 Seção 1: Informações Básicas")
    print("    - 🏦 Seção 2: Informações Financeiras")
    print("    - 📅 Seção 3: Datas e Parcelas")
    print("    - 📝 Seção 4: Informações Adicionais")
    
    print("  • 👤 Usuário: Criado do zero (900x700px)")
    print("    - 👤 Seção 1: Informações Pessoais")
    print("    - 🔐 Seção 2: Credenciais de Acesso")
    print("    - 🛡️ Seção 3: Permissões e Configurações")
    print("    - ⚙️ Seção 4: Preferências do Sistema")
    
    print("  • 💳 Carteira: Criado do zero (850x750px)")
    print("    - 💼 Seção 1: Informações Básicas")
    print("    - 💰 Seção 2: Informações Financeiras")
    print("    - ⚙️ Seção 3: Configurações e Observações")
    print("    - ℹ️ Seção 4: Informações e Dicas")
    
    print("\n✨ CARACTERÍSTICAS PRINCIPAIS:")
    print("  • 📊 Design moderno e profissional")
    print("  • 🔄 Scroll suave e responsivo")
    print("  • 📋 Seções organizadas logicamente")
    print("  • 🎨 Cabeçalhos coloridos por tipo")
    print("  • 💡 Placeholders e dicas contextuais")
    print("  • ⌨️ Navegação completa por teclado")
    print("  • 📱 Interface responsiva e intuitiva")
    print("  • 🔒 Validações em tempo real")
    print("  • 💰 Formatação automática de moeda")
    print("  • 🎯 Campos obrigatórios marcados")
    print("  • 📝 Observações com placeholders")
    
    print("\n🎨 DESIGN MODERNO:")
    print("  • 🌈 Cores harmoniosas e profissionais")
    print("  • 📐 Layout responsivo e organizado")
    print("  • 🔤 Tipografia legível e moderna")
    print("  • 🎯 Hierarquia visual clara")
    print("  • 💫 Animações suaves")
    print("  • 🖱️ Cursores interativos")
    print("  • 📱 Interface adaptável")
    
    print("\n🔧 FUNCIONALIDADES AVANÇADAS:")
    print("  • 🔍 Validação em tempo real")
    print("  • 💡 Dicas contextuais inteligentes")
    print("  • 🔒 Verificador de força de senha")
    print("  • 💰 Formatação automática de valores")
    print("  • 📅 Calendários visuais integrados")
    print("  • ⌨️ Atalhos de teclado completos")
    print("  • 🎯 Foco automático inteligente")
    print("  • 📝 Placeholders informativos")
    
    print("\n✅ FUNCIONALIDADES COMPLETAS MANTIDAS:")
    print("  • 📊 Dashboard financeiro moderno")
    print("  • 💳 Gestão completa de carteiras")
    print("  • 💸 Transações avançadas com parcelas")
    print("  • 📅 Controle de vencimentos")
    print("  • 👥 Sistema completo de usuários")
    print("  • 🛡️ Administração avançada")
    print("  • 📊 5 tipos de relatórios funcionais")
    print("  • ⚙️ Backup e restauração")
    print("  • ✏️ Botões de editar funcionando")
    print("  • 🔄 Atualização automática")
    print("  • 🎨 Interface moderna e profissional")
    print("  • 📊 Linhas alternadas em todas as listas")
    print("  • 🎉 FORMULÁRIOS COMPLETAMENTE NOVOS")
    
    print("\n🎉 FORMULÁRIOS COMPLETAMENTE NOVOS CRIADOS DO ZERO!")
    print("DESIGN MODERNO + FUNCIONALIDADES AVANÇADAS + INTERFACE PROFISSIONAL!")
    print("=" * 90)
    
    input("\nPressione Enter para sair...")

if __name__ == "__main__":
    main()
