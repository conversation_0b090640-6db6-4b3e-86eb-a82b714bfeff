#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Diagnóstico completo do sistema
"""

import sys
import os
import traceback
from pathlib import Path

def print_separator(title):
    print("\n" + "="*60)
    print(f" {title}")
    print("="*60)

def test_python():
    """Testa Python básico"""
    print_separator("TESTE DO PYTHON")
    
    try:
        print(f"✓ Python versão: {sys.version}")
        print(f"✓ Executável: {sys.executable}")
        print(f"✓ Plataforma: {sys.platform}")
        return True
    except Exception as e:
        print(f"✗ Erro no Python: {e}")
        return False

def test_basic_imports():
    """Testa importações básicas"""
    print_separator("TESTE DE IMPORTAÇÕES BÁSICAS")
    
    basic_modules = [
        'tkinter',
        'sqlite3', 
        'datetime',
        'pathlib',
        'json',
        'shutil',
        'hashlib'
    ]
    
    success_count = 0
    for module in basic_modules:
        try:
            __import__(module)
            print(f"✓ {module} - OK")
            success_count += 1
        except ImportError as e:
            print(f"✗ {module} - ERRO: {e}")
    
    print(f"\nResultado: {success_count}/{len(basic_modules)} módulos OK")
    return success_count == len(basic_modules)

def test_tkinter():
    """Testa Tkinter especificamente"""
    print_separator("TESTE ESPECÍFICO DO TKINTER")
    
    try:
        import tkinter as tk
        print("✓ tkinter importado")
        
        # Testar criação de janela
        root = tk.Tk()
        root.withdraw()  # Esconder janela
        print("✓ Janela Tkinter criada")
        
        # Testar ttk
        from tkinter import ttk
        print("✓ ttk importado")
        
        root.destroy()
        print("✓ Tkinter funcionando perfeitamente")
        return True
        
    except Exception as e:
        print(f"✗ Erro no Tkinter: {e}")
        print("SOLUÇÃO: Instale tkinter")
        print("Ubuntu/Debian: sudo apt-get install python3-tk")
        print("CentOS/RHEL: sudo yum install tkinter")
        return False

def test_sqlite():
    """Testa SQLite"""
    print_separator("TESTE DO SQLITE")
    
    try:
        import sqlite3
        print("✓ sqlite3 importado")
        
        # Testar criação de banco
        conn = sqlite3.connect(":memory:")
        cursor = conn.cursor()
        cursor.execute("CREATE TABLE test (id INTEGER, name TEXT)")
        cursor.execute("INSERT INTO test VALUES (1, 'teste')")
        cursor.execute("SELECT * FROM test")
        result = cursor.fetchone()
        conn.close()
        
        print(f"✓ SQLite funcionando: {result}")
        return True
        
    except Exception as e:
        print(f"✗ Erro no SQLite: {e}")
        return False

def test_file_system():
    """Testa sistema de arquivos"""
    print_separator("TESTE DO SISTEMA DE ARQUIVOS")
    
    try:
        # Testar diretório atual
        current_dir = Path.cwd()
        print(f"✓ Diretório atual: {current_dir}")
        
        # Testar criação de pasta
        test_dir = Path("test_diagnostico")
        test_dir.mkdir(exist_ok=True)
        print("✓ Criação de pasta OK")
        
        # Testar criação de arquivo
        test_file = test_dir / "test.txt"
        test_file.write_text("teste")
        print("✓ Criação de arquivo OK")
        
        # Testar leitura
        content = test_file.read_text()
        print(f"✓ Leitura de arquivo OK: {content}")
        
        # Limpar
        test_file.unlink()
        test_dir.rmdir()
        print("✓ Limpeza OK")
        
        return True
        
    except Exception as e:
        print(f"✗ Erro no sistema de arquivos: {e}")
        return False

def test_project_files():
    """Testa arquivos do projeto"""
    print_separator("TESTE DOS ARQUIVOS DO PROJETO")
    
    required_files = [
        'main_sem_dependencias.py',
        'main.py',
        'run.py',
        'diagnostico.py'
    ]
    
    optional_files = [
        'src/database.py',
        'src/auth.py',
        'requirements.txt'
    ]
    
    print("Arquivos obrigatórios:")
    required_ok = 0
    for file in required_files:
        if Path(file).exists():
            print(f"✓ {file}")
            required_ok += 1
        else:
            print(f"✗ {file} - FALTANDO")
    
    print("\nArquivos opcionais:")
    for file in optional_files:
        if Path(file).exists():
            print(f"✓ {file}")
        else:
            print(f"- {file} - não encontrado")
    
    return required_ok == len(required_files)

def test_simple_app():
    """Testa aplicação simples"""
    print_separator("TESTE DE APLICAÇÃO SIMPLES")
    
    try:
        import tkinter as tk
        from tkinter import ttk
        
        print("Criando aplicação de teste...")
        
        root = tk.Tk()
        root.title("Teste")
        root.geometry("300x200")
        
        label = ttk.Label(root, text="Teste OK!")
        label.pack(pady=50)
        
        # Fechar automaticamente após 1 segundo
        root.after(1000, root.destroy)
        
        print("✓ Aplicação criada")
        print("✓ Janela será exibida por 1 segundo...")
        
        root.mainloop()
        
        print("✓ Aplicação funcionou perfeitamente!")
        return True
        
    except Exception as e:
        print(f"✗ Erro na aplicação: {e}")
        traceback.print_exc()
        return False

def test_database_creation():
    """Testa criação de banco de dados"""
    print_separator("TESTE DE CRIAÇÃO DE BANCO")
    
    try:
        import sqlite3
        
        # Criar diretório data
        data_dir = Path("data")
        data_dir.mkdir(exist_ok=True)
        print("✓ Diretório data criado")
        
        # Criar banco de teste
        db_path = data_dir / "teste_diagnostico.db"
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # Criar tabela
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS users (
                id INTEGER PRIMARY KEY,
                username TEXT UNIQUE,
                password TEXT
            )
        ''')
        
        # Inserir dados
        cursor.execute("INSERT OR REPLACE INTO users (username, password) VALUES (?, ?)", 
                      ("admin", "admin123"))
        
        conn.commit()
        
        # Testar consulta
        cursor.execute("SELECT * FROM users WHERE username = ?", ("admin",))
        result = cursor.fetchone()
        
        conn.close()
        
        print(f"✓ Banco criado e testado: {result}")
        
        # Limpar
        db_path.unlink()
        print("✓ Banco de teste removido")
        
        return True
        
    except Exception as e:
        print(f"✗ Erro no banco: {e}")
        traceback.print_exc()
        return False

def run_main_sem_dependencias():
    """Tenta executar main_sem_dependencias.py"""
    print_separator("TESTE DO MAIN_SEM_DEPENDENCIAS.PY")
    
    try:
        if not Path("main_sem_dependencias.py").exists():
            print("✗ Arquivo main_sem_dependencias.py não encontrado")
            return False
        
        print("Tentando importar main_sem_dependencias...")
        
        # Tentar importar
        import importlib.util
        spec = importlib.util.spec_from_file_location("main_sem_dependencias", "main_sem_dependencias.py")
        module = importlib.util.module_from_spec(spec)
        
        print("✓ Arquivo carregado")
        
        # Tentar executar (sem interface gráfica)
        spec.loader.exec_module(module)
        
        print("✓ Módulo executado sem erros")
        
        # Testar classe principal
        if hasattr(module, 'GestaoContasSemDependencias'):
            app = module.GestaoContasSemDependencias()
            print("✓ Classe principal criada")
            
            # Testar inicialização do banco
            app.initialize_database()
            print("✓ Banco inicializado")
            
            return True
        else:
            print("✗ Classe principal não encontrada")
            return False
            
    except Exception as e:
        print(f"✗ Erro ao executar: {e}")
        traceback.print_exc()
        return False

def main():
    """Função principal de diagnóstico"""
    print("DIAGNÓSTICO COMPLETO DO SISTEMA DE GESTÃO DE CONTAS")
    print("Este script vai identificar exatamente qual é o problema")
    
    tests = [
        ("Python Básico", test_python),
        ("Importações Básicas", test_basic_imports),
        ("Tkinter", test_tkinter),
        ("SQLite", test_sqlite),
        ("Sistema de Arquivos", test_file_system),
        ("Arquivos do Projeto", test_project_files),
        ("Criação de Banco", test_database_creation),
        ("Aplicação Simples", test_simple_app),
        ("Main Sem Dependências", run_main_sem_dependencias)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"✗ ERRO CRÍTICO em {test_name}: {e}")
            traceback.print_exc()
            results.append((test_name, False))
    
    # Resumo final
    print_separator("RESUMO DO DIAGNÓSTICO")
    
    passed = 0
    failed = 0
    
    for test_name, result in results:
        if result:
            print(f"✓ {test_name}")
            passed += 1
        else:
            print(f"✗ {test_name}")
            failed += 1
    
    print(f"\nResultado: {passed} OK, {failed} FALHOU")
    
    # Recomendações
    print_separator("RECOMENDAÇÕES")
    
    if failed == 0:
        print("🎉 TUDO FUNCIONANDO!")
        print("O sistema deveria funcionar perfeitamente.")
        print("Tente executar: python main_sem_dependencias.py")
    
    elif any(name == "Tkinter" and not result for name, result in results):
        print("❌ PROBLEMA: Tkinter não funciona")
        print("SOLUÇÃO:")
        print("- Ubuntu/Debian: sudo apt-get install python3-tk")
        print("- CentOS/RHEL: sudo yum install tkinter")
        print("- Windows: Reinstale Python com tkinter")
    
    elif any(name == "Python Básico" and not result for name, result in results):
        print("❌ PROBLEMA: Python não funciona corretamente")
        print("SOLUÇÃO: Reinstale Python")
    
    else:
        print("⚠️ PROBLEMAS DETECTADOS")
        print("Verifique os erros acima e:")
        print("1. Instale dependências faltando")
        print("2. Verifique permissões de arquivo")
        print("3. Execute como administrador")
    
    print("\nPara mais ajuda, envie este diagnóstico completo.")
    
    input("\nPressione Enter para sair...")

if __name__ == "__main__":
    main()
