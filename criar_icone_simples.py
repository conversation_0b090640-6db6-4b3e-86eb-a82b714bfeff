#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Cria ícone simples para a aplicação usando tkinter
"""

import tkinter as tk
from tkinter import Canvas
import os

def create_simple_icon():
    """Cria um ícone simples usando tkinter"""
    try:
        # Criar janela invisível
        root = tk.Tk()
        root.withdraw()  # Esconder janela
        
        # Criar canvas para desenhar
        canvas = Canvas(root, width=256, height=256, bg='#2c3e50')
        
        # Desenhar círculo de fundo
        canvas.create_oval(20, 20, 236, 236, fill='#3498db', outline='#2c3e50', width=4)
        
        # Desenhar símbolo de dinheiro
        canvas.create_text(128, 100, text='$', font=('Arial', 80, 'bold'), fill='#27ae60')
        
        # Desenhar texto "GC"
        canvas.create_text(128, 180, text='GC', font=('Arial', 30, 'bold'), fill='white')
        
        # Salvar como PostScript (único formato que tkinter suporta)
        canvas.postscript(file='icon_temp.ps')
        
        print("✓ Arquivo temporário criado")
        
        # Limpar
        root.destroy()
        
        # Criar arquivo ICO básico manualmente
        create_basic_ico()
        
        return True
        
    except Exception as e:
        print(f"Erro ao criar ícone: {e}")
        return False

def create_basic_ico():
    """Cria um arquivo ICO básico"""
    try:
        # Dados de um ícone ICO 16x16 simples (azul)
        ico_data = bytes([
            # ICO Header
            0x00, 0x00,  # Reserved
            0x01, 0x00,  # Type (1 = ICO)
            0x01, 0x00,  # Number of images
            
            # Image Directory Entry
            0x10,        # Width (16)
            0x10,        # Height (16)
            0x00,        # Color count (0 = >256 colors)
            0x00,        # Reserved
            0x01, 0x00,  # Color planes
            0x20, 0x00,  # Bits per pixel (32)
            0x00, 0x04, 0x00, 0x00,  # Size of image data
            0x16, 0x00, 0x00, 0x00,  # Offset to image data
            
            # Bitmap Info Header
            0x28, 0x00, 0x00, 0x00,  # Header size
            0x10, 0x00, 0x00, 0x00,  # Width
            0x20, 0x00, 0x00, 0x00,  # Height (2x for ICO)
            0x01, 0x00,              # Planes
            0x20, 0x00,              # Bits per pixel
            0x00, 0x00, 0x00, 0x00,  # Compression
            0x00, 0x04, 0x00, 0x00,  # Image size
            0x00, 0x00, 0x00, 0x00,  # X pixels per meter
            0x00, 0x00, 0x00, 0x00,  # Y pixels per meter
            0x00, 0x00, 0x00, 0x00,  # Colors used
            0x00, 0x00, 0x00, 0x00,  # Important colors
        ])
        
        # Adicionar dados de pixel (16x16 pixels, 32-bit RGBA)
        # Criar um padrão simples azul com símbolo $
        pixel_data = bytearray()
        
        for y in range(32):  # 32 linhas (16 para imagem + 16 para máscara)
            for x in range(16):
                if y < 16:  # Parte da imagem
                    # Criar um padrão simples
                    if (x >= 2 and x <= 13 and y >= 2 and y <= 13):
                        if (x >= 6 and x <= 9 and y >= 4 and y <= 11):
                            # Símbolo $ (verde)
                            pixel_data.extend([0x60, 0xAE, 0x27, 0xFF])  # Verde
                        else:
                            # Fundo azul
                            pixel_data.extend([0xDB, 0x98, 0x34, 0xFF])  # Azul
                    else:
                        # Borda escura
                        pixel_data.extend([0x50, 0x3E, 0x2C, 0xFF])  # Azul escuro
                else:
                    # Máscara AND (transparência)
                    pixel_data.extend([0x00, 0x00, 0x00, 0x00])
        
        # Combinar header e dados
        full_ico_data = ico_data + pixel_data
        
        # Salvar arquivo ICO
        with open('app_icon.ico', 'wb') as f:
            f.write(full_ico_data)
        
        print("✓ Ícone ICO básico criado: app_icon.ico")
        return True
        
    except Exception as e:
        print(f"Erro ao criar ICO: {e}")
        return False

def create_icon_from_text():
    """Cria um ícone usando apenas caracteres"""
    try:
        # Criar um arquivo de texto que representa o ícone
        icon_ascii = """
        ████████████████
        ██            ██
        ██    ████    ██
        ██   ██  ██   ██
        ██   ██  ██   ██
        ██    ████    ██
        ██     $$     ██
        ██    $$$$    ██
        ██     $$     ██
        ██    $$$$    ██
        ██     $$     ██
        ██     GC     ██
        ██            ██
        ████████████████
        """
        
        with open('icon_ascii.txt', 'w', encoding='utf-8') as f:
            f.write(icon_ascii)
        
        print("✓ Representação ASCII do ícone criada")
        return True
        
    except Exception as e:
        print(f"Erro: {e}")
        return False

def main():
    """Função principal"""
    print("CRIADOR DE ÍCONE SIMPLES")
    print("=" * 40)
    
    success = False
    
    # Tentar criar ícone básico
    print("Tentando criar ícone ICO básico...")
    if create_basic_ico():
        success = True
    
    # Criar representação ASCII como backup
    print("Criando representação ASCII...")
    create_icon_from_text()
    
    print("\n" + "=" * 40)
    if success:
        print("✓ Ícone criado com sucesso!")
        print("📁 Arquivo: app_icon.ico")
    else:
        print("⚠️ Ícone básico criado")
    
    print("\n🚀 PRÓXIMO PASSO:")
    print("Execute: py criar_executavel_com_icone.py")
    
    input("\nPressione Enter para sair...")

if __name__ == "__main__":
    main()
