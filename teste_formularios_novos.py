#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Teste dos Formulários Completamente Novos
Criados do zero com design moderno
"""

import tkinter as tk
from tkinter import ttk
import sys
import os

# Adicionar o diretório src ao path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from modules.database_manager import DatabaseManager
from gui.transaction_form_new import TransactionFormNew
from gui.user_form_new import UserFormNew
from gui.wallet_form_new import WalletFormNew

class FormulariosTester:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("🎉 Teste dos Formulários Completamente Novos")
        self.root.geometry("600x500")
        self.root.configure(bg='#f8f9fa')
        
        # Inicializar banco de dados
        self.db_manager = DatabaseManager()
        
        # Criar interface
        self.create_interface()
        
        # Centralizar janela
        self.center_window()
    
    def center_window(self):
        """Centraliza a janela na tela"""
        self.root.update_idletasks()
        width = self.root.winfo_width()
        height = self.root.winfo_height()
        x = (self.root.winfo_screenwidth() // 2) - (width // 2)
        y = (self.root.winfo_screenheight() // 2) - (height // 2)
        self.root.geometry(f'{width}x{height}+{x}+{y}')
    
    def create_interface(self):
        """Cria a interface de teste"""
        # Cabeçalho
        header_frame = tk.Frame(self.root, bg='#2c3e50', height=80)
        header_frame.pack(fill=tk.X, pady=(0, 20))
        header_frame.pack_propagate(False)
        
        title_label = tk.Label(
            header_frame,
            text="🎉 Formulários Completamente Novos",
            font=("Arial", 18, "bold"),
            fg='white',
            bg='#2c3e50'
        )
        title_label.pack(expand=True)
        
        subtitle_label = tk.Label(
            header_frame,
            text="Criados do zero com design moderno e funcionalidades avançadas",
            font=("Arial", 11),
            fg='white',
            bg='#2c3e50'
        )
        subtitle_label.pack()
        
        # Container principal
        main_container = tk.Frame(self.root, bg='#f8f9fa')
        main_container.pack(fill=tk.BOTH, expand=True, padx=30, pady=(0, 30))
        
        # Seção Transações
        self.create_transaction_section(main_container)
        
        # Seção Usuários
        self.create_user_section(main_container)
        
        # Seção Carteiras
        self.create_wallet_section(main_container)
        
        # Seção Informações
        self.create_info_section(main_container)
        
        # Botão sair
        exit_btn = tk.Button(
            main_container,
            text="🚪 Sair",
            command=self.root.quit,
            font=("Arial", 12, "bold"),
            bg='#e74c3c',
            fg='white',
            relief=tk.RAISED,
            bd=2,
            padx=20,
            pady=8,
            cursor='hand2'
        )
        exit_btn.pack(pady=20)
    
    def create_transaction_section(self, parent):
        """Cria seção de transações"""
        section_frame = tk.LabelFrame(
            parent,
            text="💸 Formulários de Transação",
            font=("Arial", 12, "bold"),
            bg='#f8f9fa',
            fg='#2c3e50',
            relief=tk.GROOVE,
            bd=2
        )
        section_frame.pack(fill=tk.X, pady=(0, 15))
        section_frame.configure(padx=20, pady=15)
        
        # Botões lado a lado
        button_frame = tk.Frame(section_frame, bg='#f8f9fa')
        button_frame.pack(fill=tk.X)
        
        # Nova Receita
        income_btn = tk.Button(
            button_frame,
            text="📈 Nova Receita (Novo)",
            command=self.test_income_form,
            font=("Arial", 11, "bold"),
            bg='#27ae60',
            fg='white',
            relief=tk.RAISED,
            bd=2,
            padx=15,
            pady=8,
            cursor='hand2'
        )
        income_btn.pack(side=tk.LEFT, padx=(0, 10))
        
        # Nova Despesa
        expense_btn = tk.Button(
            button_frame,
            text="📉 Nova Despesa (Novo)",
            command=self.test_expense_form,
            font=("Arial", 11, "bold"),
            bg='#e74c3c',
            fg='white',
            relief=tk.RAISED,
            bd=2,
            padx=15,
            pady=8,
            cursor='hand2'
        )
        expense_btn.pack(side=tk.LEFT)
    
    def create_user_section(self, parent):
        """Cria seção de usuários"""
        section_frame = tk.LabelFrame(
            parent,
            text="👥 Formulário de Usuário",
            font=("Arial", 12, "bold"),
            bg='#f8f9fa',
            fg='#2c3e50',
            relief=tk.GROOVE,
            bd=2
        )
        section_frame.pack(fill=tk.X, pady=(0, 15))
        section_frame.configure(padx=20, pady=15)
        
        user_btn = tk.Button(
            section_frame,
            text="👤 Novo Usuário (Completamente Novo)",
            command=self.test_user_form,
            font=("Arial", 11, "bold"),
            bg='#9b59b6',
            fg='white',
            relief=tk.RAISED,
            bd=2,
            padx=15,
            pady=8,
            cursor='hand2'
        )
        user_btn.pack()
    
    def create_wallet_section(self, parent):
        """Cria seção de carteiras"""
        section_frame = tk.LabelFrame(
            parent,
            text="💳 Formulário de Carteira",
            font=("Arial", 12, "bold"),
            bg='#f8f9fa',
            fg='#2c3e50',
            relief=tk.GROOVE,
            bd=2
        )
        section_frame.pack(fill=tk.X, pady=(0, 15))
        section_frame.configure(padx=20, pady=15)
        
        wallet_btn = tk.Button(
            section_frame,
            text="💳 Nova Carteira (Completamente Nova)",
            command=self.test_wallet_form,
            font=("Arial", 11, "bold"),
            bg='#3498db',
            fg='white',
            relief=tk.RAISED,
            bd=2,
            padx=15,
            pady=8,
            cursor='hand2'
        )
        wallet_btn.pack()
    
    def create_info_section(self, parent):
        """Cria seção de informações"""
        section_frame = tk.LabelFrame(
            parent,
            text="ℹ️ Novidades dos Formulários",
            font=("Arial", 12, "bold"),
            bg='#f8f9fa',
            fg='#2c3e50',
            relief=tk.GROOVE,
            bd=2
        )
        section_frame.pack(fill=tk.X, pady=(0, 15))
        section_frame.configure(padx=20, pady=15)
        
        info_text = """🎉 FORMULÁRIOS COMPLETAMENTE NOVOS:

✨ Características Principais:
• 📊 Design moderno e profissional
• 🔄 Scroll suave e responsivo
• 📋 Seções organizadas logicamente
• 🎨 Cabeçalhos coloridos por tipo
• 💡 Placeholders e dicas contextuais
• ⌨️ Navegação completa por teclado
• 📱 Interface responsiva e intuitiva

🆕 Melhorias Implementadas:
• 📈 Transação: 4 seções organizadas (800x900px)
• 👤 Usuário: 4 seções com validações (900x700px)
• 💳 Carteira: 4 seções com ajuda (850x750px)
• 🔒 Validações em tempo real
• 💰 Formatação automática de moeda
• 🎯 Campos obrigatórios marcados
• 📝 Observações com placeholders"""
        
        info_label = tk.Label(
            section_frame,
            text=info_text,
            font=("Arial", 9),
            bg='#f8f9fa',
            fg='#2c3e50',
            justify=tk.LEFT
        )
        info_label.pack(anchor=tk.W)
    
    def test_income_form(self):
        """Testa formulário de receita"""
        try:
            form = TransactionFormNew(self.root, self.db_manager, 1, 'income')
            self.root.wait_window(form.window)
            print(f"✅ Resultado Nova Receita: {form.result}")
        except Exception as e:
            print(f"❌ Erro ao testar receita: {str(e)}")
    
    def test_expense_form(self):
        """Testa formulário de despesa"""
        try:
            form = TransactionFormNew(self.root, self.db_manager, 1, 'expense')
            self.root.wait_window(form.window)
            print(f"✅ Resultado Nova Despesa: {form.result}")
        except Exception as e:
            print(f"❌ Erro ao testar despesa: {str(e)}")
    
    def test_user_form(self):
        """Testa formulário de usuário"""
        try:
            form = UserFormNew(self.root, self.db_manager)
            self.root.wait_window(form.window)
            print(f"✅ Resultado Novo Usuário: {form.result}")
        except Exception as e:
            print(f"❌ Erro ao testar usuário: {str(e)}")
    
    def test_wallet_form(self):
        """Testa formulário de carteira"""
        try:
            form = WalletFormNew(self.root, self.db_manager, 1)
            self.root.wait_window(form.window)
            print(f"✅ Resultado Nova Carteira: {form.result}")
        except Exception as e:
            print(f"❌ Erro ao testar carteira: {str(e)}")
    
    def run(self):
        """Executa o teste"""
        print("🎉 FORMULÁRIOS COMPLETAMENTE NOVOS DISPONÍVEIS!")
        print("=" * 60)
        print("📈 Nova Receita: Design moderno com 4 seções")
        print("📉 Nova Despesa: Design moderno com 4 seções")
        print("👤 Novo Usuário: Interface completa com validações")
        print("💳 Nova Carteira: Dashboard informativo e intuitivo")
        print("🔄 Todos com scroll, placeholders e navegação por teclado")
        print("📱 Interface responsiva e profissional")
        print("=" * 60)
        
        self.root.mainloop()

def main():
    """Função principal"""
    try:
        tester = FormulariosTester()
        tester.run()
    except Exception as e:
        print(f"❌ Erro ao inicializar teste: {str(e)}")

if __name__ == "__main__":
    main()
