#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Teste do formulário de novo veículo
"""

import sys
import os
import tkinter as tk

# Adicionar o diretório src ao path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from database import DatabaseManager
from auth import AuthManager
from gui.vehicle_form import VehicleForm

def test_vehicle_form():
    """Testa o formulário de novo veículo"""
    print("🚗 TESTE DO FORMULÁRIO DE NOVO VEÍCULO")
    print("=" * 60)
    
    try:
        # Inicializar banco de dados
        db_manager = DatabaseManager()
        db_manager.initialize_database()
        
        auth_manager = AuthManager(db_manager)
        
        # Fazer login
        user_data = auth_manager.authenticate_user('admin', 'admin123')
        if not user_data:
            print("❌ Falha no login!")
            return
        
        print(f"✅ Login realizado: {user_data['full_name']}")
        user_id = user_data['id']
        
        # <PERSON>riar janela principal
        root = tk.Tk()
        root.title("Teste Formulário Veículo")
        root.geometry("800x600")
        
        print("✅ Janela principal criada")
        
        # Criar formulário de veículo
        print("\n🔧 Criando formulário de novo veículo...")
        
        def callback_test():
            print("✅ Callback chamado - veículo salvo com sucesso!")
        
        try:
            vehicle_form = VehicleForm(
                parent=root,
                db_manager=db_manager,
                user_id=user_id,
                vehicle_data=None,  # Novo veículo
                callback=callback_test
            )
            
            print("✅ Formulário de veículo criado!")
            print(f"📐 Geometria da janela: {vehicle_form.window.geometry()}")
            
            # Verificar botões após renderização
            def check_form_buttons():
                try:
                    vehicle_form.window.update_idletasks()
                    
                    # Obter dimensões da janela do formulário
                    form_width = vehicle_form.window.winfo_width()
                    form_height = vehicle_form.window.winfo_height()
                    
                    print(f"📏 Dimensões da janela do formulário: {form_width}x{form_height}")
                    
                    # Procurar botões especificamente no formulário
                    form_buttons = []
                    def find_form_buttons(widget, level=0):
                        indent = "  " * level
                        if hasattr(widget, 'winfo_class') and widget.winfo_class() == 'TButton':
                            try:
                                button_info = {
                                    'text': widget['text'],
                                    'width': widget.winfo_width(),
                                    'height': widget.winfo_height(),
                                    'x': widget.winfo_x(),
                                    'y': widget.winfo_y(),
                                    'widget': widget
                                }
                                form_buttons.append(button_info)
                                print(f"{indent}🔍 Botão ttk.Button: '{button_info['text']}'")
                            except Exception as e:
                                print(f"{indent}❌ Erro ao obter info do botão: {e}")
                        
                        try:
                            for child in widget.winfo_children():
                                find_form_buttons(child, level + 1)
                        except:
                            pass
                    
                    find_form_buttons(vehicle_form.window)
                    
                    print(f"\n📊 Botões do formulário encontrados: {len(form_buttons)}")
                    for i, btn in enumerate(form_buttons):
                        print(f"\n   {i+1}. Botão: '{btn['text']}'")
                        print(f"      Dimensões: {btn['width']}x{btn['height']}")
                        print(f"      Posição: ({btn['x']}, {btn['y']})")
                        
                        # Verificar se o texto está visível
                        text_length = len(btn['text'])
                        min_width_needed = text_length * 8 + 20  # 8px por char + padding
                        
                        if btn['width'] >= min_width_needed:
                            print(f"      ✅ Largura adequada ({btn['width']}px >= {min_width_needed}px)")
                        else:
                            print(f"      ⚠️  Pode estar cortado ({btn['width']}px < {min_width_needed}px)")
                        
                        # Verificar se está dentro da janela
                        if btn['x'] + btn['width'] > form_width:
                            print(f"      ⚠️  Botão pode estar fora da área visível!")
                        else:
                            print(f"      ✅ Botão está dentro da área visível")
                    
                    # Verificar textos esperados
                    expected_texts = ['Cancelar', 'Salvar']
                    found_texts = [btn['text'] for btn in form_buttons]
                    
                    print(f"\n🎯 VERIFICAÇÃO DE TEXTOS:")
                    for expected in expected_texts:
                        if expected in found_texts:
                            print(f"   ✅ '{expected}' - Encontrado")
                        else:
                            print(f"   ❌ '{expected}' - NÃO encontrado")
                    
                    # Testar funcionalidade de cadastro
                    print(f"\n🧪 TESTANDO FUNCIONALIDADE DE CADASTRO:")
                    
                    # Preencher campos obrigatórios
                    try:
                        vehicle_form.name_var.set("Teste Veículo")
                        vehicle_form.brand_var.set("Toyota")
                        vehicle_form.model_var.set("Corolla")
                        vehicle_form.year_var.set("2020")
                        
                        print("   ✅ Campos obrigatórios preenchidos")
                        
                        # Verificar se os métodos existem
                        if hasattr(vehicle_form, 'save'):
                            print("   ✅ Método 'save' existe")
                        else:
                            print("   ❌ Método 'save' não encontrado")
                        
                        if hasattr(vehicle_form, 'cancel'):
                            print("   ✅ Método 'cancel' existe")
                        else:
                            print("   ❌ Método 'cancel' não encontrado")
                        
                        # Verificar se o DatabaseManager tem o método add_vehicle
                        if hasattr(db_manager, 'add_vehicle'):
                            print("   ✅ Método 'add_vehicle' existe no DatabaseManager")
                        else:
                            print("   ❌ Método 'add_vehicle' não encontrado no DatabaseManager")
                        
                    except Exception as e:
                        print(f"   ❌ Erro ao testar funcionalidade: {str(e)}")
                    
                except Exception as e:
                    print(f"❌ Erro ao verificar botões do formulário: {str(e)}")
                    import traceback
                    traceback.print_exc()
            
            # Aguardar renderização e verificar
            root.after(1500, check_form_buttons)
            
            # Fechar após teste
            root.after(8000, vehicle_form.window.destroy)
            
        except Exception as e:
            print(f"❌ Erro ao criar formulário: {str(e)}")
            import traceback
            traceback.print_exc()
        
        print("\n✅ TESTE DO FORMULÁRIO DE VEÍCULO INICIADO!")
        print("🔍 Verificando botões em 1.5 segundos...")
        
        # Executar por tempo limitado
        root.after(10000, root.quit)
        root.mainloop()
        root.destroy()
        
    except Exception as e:
        print(f"❌ ERRO GERAL: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_vehicle_form()
