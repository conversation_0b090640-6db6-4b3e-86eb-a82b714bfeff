#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Teste dos formulários melhorados
"""

import tkinter as tk
from tkinter import ttk
import sys
import os

# Adicionar o diretório src ao path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from modules.database_manager import DatabaseManager
from gui.transaction_dialog import TransactionDialog
from gui.user_dialog_improved import UserDialogImproved
from gui.wallet_dialog_improved import WalletDialogImproved

def test_improved_dialogs():
    """Testa os diálogos melhorados"""
    # Criar janela principal
    root = tk.Tk()
    root.title("Teste Formulários Melhorados")
    root.geometry("500x400")
    
    # Inicializar banco de dados
    db_manager = DatabaseManager()
    
    # Funções de teste
    def test_transaction_income():
        dialog = TransactionDialog(root, db_manager, 1, 'income')
        root.wait_window(dialog.window)
        print(f"Resultado Receita: {dialog.result}")
    
    def test_transaction_expense():
        dialog = TransactionDialog(root, db_manager, 1, 'expense')
        root.wait_window(dialog.window)
        print(f"Resultado Despesa: {dialog.result}")
    
    def test_user_dialog():
        dialog = UserDialogImproved(root, db_manager)
        root.wait_window(dialog.window)
        print(f"Resultado Usuário: {dialog.result}")
    
    def test_wallet_dialog():
        dialog = WalletDialogImproved(root, db_manager, 1)
        root.wait_window(dialog.window)
        print(f"Resultado Carteira: {dialog.result}")
    
    # Interface de teste
    ttk.Label(root, text="Teste dos Formulários Melhorados", 
             font=("Arial", 16, "bold")).pack(pady=20)
    
    # Seção Transações
    trans_frame = tk.LabelFrame(root, text="💸 Transações", font=("Arial", 12, "bold"))
    trans_frame.pack(fill=tk.X, padx=20, pady=10)
    
    ttk.Button(trans_frame, text="📈 Nova Receita (Melhorada)", 
              command=test_transaction_income).pack(pady=5)
    
    ttk.Button(trans_frame, text="📉 Nova Despesa (Melhorada)", 
              command=test_transaction_expense).pack(pady=5)
    
    # Seção Usuários
    user_frame = tk.LabelFrame(root, text="👥 Usuários", font=("Arial", 12, "bold"))
    user_frame.pack(fill=tk.X, padx=20, pady=10)
    
    ttk.Button(user_frame, text="👤 Novo Usuário (Melhorado)", 
              command=test_user_dialog).pack(pady=5)
    
    # Seção Carteiras
    wallet_frame = tk.LabelFrame(root, text="💳 Carteiras", font=("Arial", 12, "bold"))
    wallet_frame.pack(fill=tk.X, padx=20, pady=10)
    
    ttk.Button(wallet_frame, text="💼 Nova Carteira (Melhorada)", 
              command=test_wallet_dialog).pack(pady=5)
    
    # Botão sair
    ttk.Button(root, text="🚪 Sair", command=root.quit).pack(pady=20)
    
    print("🎉 FORMULÁRIOS MELHORADOS DISPONÍVEIS:")
    print("• 📈 Nova Receita: Layout organizado em seções")
    print("• 📉 Nova Despesa: Layout organizado em seções")
    print("• 👤 Novo Usuário: Interface completa com scroll")
    print("• 💼 Nova Carteira: Dashboard informativo")
    print("• 🔄 Todos com scroll e campos organizados")
    print("• 📱 Interface responsiva e profissional")
    
    root.mainloop()

if __name__ == "__main__":
    test_improved_dialogs()
