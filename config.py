#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Configurações do Sistema de Gestão de Contas
"""

import os
from pathlib import Path

# Informações da aplicação
APP_NAME = "Sistema de Gestão de Contas"
APP_VERSION = "1.0.0"
APP_AUTHOR = "Sistema de Gestão Financeira"

# Caminhos
BASE_DIR = Path(__file__).parent
DATA_DIR = BASE_DIR / "data"
BACKUP_DIR = BASE_DIR / "backups"
LOGS_DIR = BASE_DIR / "logs"

# Banco de dados
DATABASE_PATH = DATA_DIR / "gestao_contas.db"

# Configurações de backup
BACKUP_RETENTION_DAYS = 30
AUTO_BACKUP_INTERVAL_HOURS = 24
MAX_BACKUP_FILES = 10

# Configurações de alertas
ALERT_DAYS_AHEAD = 7
LOW_BALANCE_THRESHOLD_PERCENT = 10

# Configurações de interface
WINDOW_TITLE = f"{APP_NAME} v{APP_VERSION}"
WINDOW_SIZE = "1200x800"
THEME = "clam"

# Cores padrão
COLORS = {
    'primary': '#007ACC',
    'success': '#28a745',
    'warning': '#ffc107',
    'danger': '#dc3545',
    'info': '#17a2b8',
    'light': '#f8f9fa',
    'dark': '#343a40'
}

# Configurações de categorias padrão
DEFAULT_INCOME_CATEGORIES = [
    ('Salário', 'Salário mensal', '#28a745'),
    ('Freelance', 'Trabalhos extras', '#17a2b8'),
    ('Investimentos', 'Rendimentos de investimentos', '#ffc107'),
    ('Outros', 'Outras receitas', '#6c757d')
]

DEFAULT_EXPENSE_CATEGORIES = [
    ('Alimentação', 'Gastos com comida', '#dc3545'),
    ('Transporte', 'Gastos com transporte', '#fd7e14'),
    ('Moradia', 'Aluguel, condomínio, etc.', '#6f42c1'),
    ('Saúde', 'Gastos médicos', '#e83e8c'),
    ('Educação', 'Cursos, livros, etc.', '#20c997'),
    ('Lazer', 'Entretenimento', '#0dcaf0'),
    ('Outros', 'Outras despesas', '#6c757d')
]

# Tipos de carteira
WALLET_TYPES = {
    'checking': 'Conta Corrente',
    'savings': 'Poupança',
    'credit': 'Cartão de Crédito',
    'cash': 'Dinheiro'
}

# Configurações de recorrência
RECURRING_TYPES = {
    'daily': 'Diário',
    'weekly': 'Semanal',
    'monthly': 'Mensal',
    'yearly': 'Anual'
}

# Configurações de log
LOG_LEVEL = "INFO"
LOG_FORMAT = "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
LOG_MAX_SIZE = 10 * 1024 * 1024  # 10MB
LOG_BACKUP_COUNT = 5

def ensure_directories():
    """Garante que os diretórios necessários existem"""
    directories = [DATA_DIR, BACKUP_DIR, LOGS_DIR]
    
    for directory in directories:
        directory.mkdir(exist_ok=True)

def get_database_url():
    """Retorna URL do banco de dados"""
    ensure_directories()
    return str(DATABASE_PATH)

def get_backup_directory():
    """Retorna diretório de backup"""
    ensure_directories()
    return str(BACKUP_DIR)

def get_logs_directory():
    """Retorna diretório de logs"""
    ensure_directories()
    return str(LOGS_DIR)

# Configurações específicas do sistema operacional
import platform

SYSTEM = platform.system()

if SYSTEM == "Windows":
    # Configurações específicas do Windows
    FONT_FAMILY = "Segoe UI"
    FONT_SIZE = 9
elif SYSTEM == "Darwin":  # macOS
    # Configurações específicas do macOS
    FONT_FAMILY = "SF Pro Display"
    FONT_SIZE = 13
else:  # Linux e outros
    # Configurações específicas do Linux
    FONT_FAMILY = "Ubuntu"
    FONT_SIZE = 10

# Configurações de formatação
CURRENCY_SYMBOL = "R$"
DATE_FORMAT = "%d/%m/%Y"
DATETIME_FORMAT = "%d/%m/%Y %H:%M"
DECIMAL_PLACES = 2

# Configurações de validação
MIN_PASSWORD_LENGTH = 6
MAX_USERNAME_LENGTH = 50
MAX_DESCRIPTION_LENGTH = 200
MAX_CATEGORY_NAME_LENGTH = 100

# Configurações de paginação
DEFAULT_PAGE_SIZE = 50
MAX_PAGE_SIZE = 1000

# Configurações de exportação
EXPORT_FORMATS = ['CSV', 'JSON', 'PDF']
DEFAULT_EXPORT_FORMAT = 'CSV'

# Configurações de importação
IMPORT_FORMATS = ['CSV', 'JSON']
DEFAULT_IMPORT_FORMAT = 'CSV'

# Mensagens padrão
MESSAGES = {
    'login_success': 'Login realizado com sucesso!',
    'login_error': 'Usuário ou senha incorretos.',
    'logout_success': 'Logout realizado com sucesso!',
    'save_success': 'Dados salvos com sucesso!',
    'save_error': 'Erro ao salvar dados.',
    'delete_success': 'Item excluído com sucesso!',
    'delete_error': 'Erro ao excluir item.',
    'backup_success': 'Backup criado com sucesso!',
    'backup_error': 'Erro ao criar backup.',
    'restore_success': 'Dados restaurados com sucesso!',
    'restore_error': 'Erro ao restaurar dados.',
    'validation_error': 'Dados inválidos. Verifique os campos.',
    'permission_error': 'Você não tem permissão para esta ação.',
    'network_error': 'Erro de conexão. Tente novamente.',
    'unknown_error': 'Erro desconhecido. Contate o suporte.'
}

# Configurações de desenvolvimento
DEBUG = False
TESTING = False

if DEBUG:
    LOG_LEVEL = "DEBUG"
    DATABASE_PATH = DATA_DIR / "gestao_contas_debug.db"

if TESTING:
    DATABASE_PATH = DATA_DIR / "gestao_contas_test.db"
