#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Teste dos relatórios
"""

import sys
import os
import tkinter as tk

# Adicionar o diretório src ao path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from database import DatabaseManager
from auth import AuthManager
from gui.main_window import MainWindow

def test_reports():
    """Testa os relatórios"""
    print("🧪 TESTE DOS RELATÓRIOS")
    print("=" * 60)
    
    try:
        # Inicializar banco de dados
        db_manager = DatabaseManager()
        db_manager.initialize_database()
        
        auth_manager = AuthManager(db_manager)
        
        # Fazer login
        user_data = auth_manager.authenticate_user('admin', 'admin123')
        if not user_data:
            print("❌ Falha no login!")
            return
        
        print(f"✅ Login realizado: {user_data['full_name']}")
        
        # Criar janela principal
        root = tk.Tk()
        root.title("Teste Relatórios")
        root.geometry("800x600")
        
        # Função de logout mock
        def mock_logout():
            print("Logout chamado")
        
        # Criar MainWindow
        main_window = MainWindow(root, db_manager, auth_manager, user_data, mock_logout)
        
        print("✅ Janela principal criada com sucesso!")
        
        # Testar relatório por categoria
        print("\n🔧 Testando relatório por categoria...")
        try:
            main_window.show_category_report()
            print("✅ Relatório por categoria funcionou!")
        except Exception as e:
            print(f"❌ Erro no relatório por categoria: {str(e)}")
        
        # Testar relatório de fluxo de caixa
        print("\n🔧 Testando relatório de fluxo de caixa...")
        try:
            main_window.show_cashflow_report()
            print("✅ Relatório de fluxo de caixa funcionou!")
        except Exception as e:
            print(f"❌ Erro no relatório de fluxo de caixa: {str(e)}")
        
        # Testar contas a vencer
        print("\n🔧 Testando contas a vencer...")
        try:
            main_window.show_due_bills()
            print("✅ Contas a vencer funcionou!")
        except Exception as e:
            print(f"❌ Erro em contas a vencer: {str(e)}")
        
        # Testar gerenciamento de categorias
        print("\n🔧 Testando gerenciamento de categorias...")
        try:
            main_window.manage_categories()
            print("✅ Gerenciamento de categorias funcionou!")
        except Exception as e:
            print(f"❌ Erro no gerenciamento de categorias: {str(e)}")
        
        # Testar relatório de veículos
        print("\n🔧 Testando relatório de veículos...")
        try:
            main_window.show_vehicle_report()
            print("✅ Relatório de veículos funcionou!")
        except Exception as e:
            print(f"❌ Erro no relatório de veículos: {str(e)}")
        
        print("\n✅ TODOS OS TESTES CONCLUÍDOS!")
        print("💡 Se não houve erros, os relatórios estão funcionando.")
        
        # Fechar janela após um tempo
        root.after(3000, root.destroy)  # Fechar após 3 segundos
        root.mainloop()
        
    except Exception as e:
        print(f"❌ ERRO GERAL: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_reports()
