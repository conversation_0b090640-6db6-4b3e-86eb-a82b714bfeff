#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Script para criar executável do Sistema de Gestão de Contas
"""

import os
import sys
import subprocess
import shutil
from pathlib import Path

def check_pyinstaller():
    """Verifica se PyInstaller está instalado"""
    try:
        import PyInstaller
        print("✓ PyInstaller encontrado")
        return True
    except ImportError:
        print("✗ PyInstaller não encontrado")
        print("Instalando PyInstaller...")
        try:
            subprocess.check_call([sys.executable, "-m", "pip", "install", "pyinstaller"])
            print("✓ PyInstaller instalado com sucesso")
            return True
        except subprocess.CalledProcessError:
            print("✗ Erro ao instalar PyInstaller")
            return False

def create_spec_file():
    """Cria arquivo .spec personalizado"""
    spec_content = '''# -*- mode: python ; coding: utf-8 -*-

block_cipher = None

a = Analysis(
    ['main.py'],
    pathex=[],
    binaries=[],
    datas=[
        ('src', 'src'),
        ('README.md', '.'),
    ],
    hiddenimports=[
        'tkinter',
        'tkinter.ttk',
        'tkinter.messagebox',
        'tkinter.filedialog',
        'sqlite3',
        'datetime',
        'pathlib',
        'json',
        'shutil',
        'zipfile',
        'calendar',
    ],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='GestaoContas',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=False,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon=None,
)
'''
    
    with open('GestaoContas.spec', 'w', encoding='utf-8') as f:
        f.write(spec_content)
    
    print("✓ Arquivo .spec criado")

def build_executable():
    """Constrói o executável"""
    print("Iniciando construção do executável...")
    
    try:
        # Usar arquivo .spec personalizado
        cmd = [sys.executable, "-m", "PyInstaller", "--clean", "GestaoContas.spec"]
        
        print("Executando PyInstaller...")
        result = subprocess.run(cmd, capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✓ Executável criado com sucesso!")
            
            # Verificar se arquivo foi criado
            exe_path = Path("dist/GestaoContas.exe")
            if exe_path.exists():
                size_mb = exe_path.stat().st_size / (1024 * 1024)
                print(f"✓ Arquivo: {exe_path}")
                print(f"✓ Tamanho: {size_mb:.1f} MB")
                return True
            else:
                print("✗ Arquivo executável não encontrado")
                return False
        else:
            print("✗ Erro ao criar executável:")
            print(result.stderr)
            return False
            
    except Exception as e:
        print(f"✗ Erro durante a construção: {str(e)}")
        return False

def create_distribution_package():
    """Cria pacote de distribuição"""
    print("\nCriando pacote de distribuição...")
    
    try:
        # Criar diretório de distribuição
        dist_dir = Path("GestaoContas_v1.0.0")
        if dist_dir.exists():
            shutil.rmtree(dist_dir)
        
        dist_dir.mkdir()
        
        # Copiar executável
        exe_source = Path("dist/GestaoContas.exe")
        if exe_source.exists():
            shutil.copy2(exe_source, dist_dir / "GestaoContas.exe")
            print("✓ Executável copiado")
        
        # Copiar documentação
        shutil.copy2("README.md", dist_dir / "README.md")
        print("✓ README copiado")
        
        # Criar diretórios necessários
        (dist_dir / "data").mkdir()
        (dist_dir / "backups").mkdir()
        print("✓ Diretórios criados")
        
        # Criar arquivo de instruções
        instructions = """SISTEMA DE GESTÃO DE CONTAS v1.0.0

INSTRUÇÕES DE USO:

1. Execute GestaoContas.exe
2. Use o login padrão:
   - Usuário: admin
   - Senha: admin123

3. Altere a senha após o primeiro acesso

ESTRUTURA:
- GestaoContas.exe: Aplicação principal
- data/: Banco de dados (criado automaticamente)
- backups/: Backups automáticos
- README.md: Documentação completa

SUPORTE:
- Consulte README.md para documentação completa
- Em caso de problemas, delete a pasta data/ para reiniciar

Desenvolvido em Python com Tkinter
© 2024 - Sistema de Gestão Financeira
"""
        
        with open(dist_dir / "INSTRUÇÕES.txt", 'w', encoding='utf-8') as f:
            f.write(instructions)
        
        print("✓ Instruções criadas")
        
        # Criar arquivo ZIP
        import zipfile
        zip_path = "GestaoContas_v1.0.0.zip"
        
        with zipfile.ZipFile(zip_path, 'w', zipfile.ZIP_DEFLATED) as zipf:
            for file_path in dist_dir.rglob('*'):
                if file_path.is_file():
                    arcname = file_path.relative_to(dist_dir.parent)
                    zipf.write(file_path, arcname)
        
        zip_size_mb = Path(zip_path).stat().st_size / (1024 * 1024)
        print(f"✓ Pacote ZIP criado: {zip_path} ({zip_size_mb:.1f} MB)")
        
        return True
        
    except Exception as e:
        print(f"✗ Erro ao criar pacote: {str(e)}")
        return False

def cleanup():
    """Limpa arquivos temporários"""
    print("\nLimpando arquivos temporários...")
    
    temp_files = [
        "GestaoContas.spec",
        "build",
        "__pycache__"
    ]
    
    for item in temp_files:
        path = Path(item)
        try:
            if path.is_file():
                path.unlink()
                print(f"✓ Removido: {item}")
            elif path.is_dir():
                shutil.rmtree(path)
                print(f"✓ Removido: {item}/")
        except:
            pass

def main():
    """Função principal"""
    print("Sistema de Gestão de Contas - Gerador de Executável")
    print("=" * 55)
    
    # Verificar PyInstaller
    if not check_pyinstaller():
        print("\n✗ Não foi possível instalar PyInstaller")
        return
    
    # Criar arquivo .spec
    create_spec_file()
    
    # Construir executável
    if not build_executable():
        print("\n✗ Falha na construção do executável")
        return
    
    # Criar pacote de distribuição
    if not create_distribution_package():
        print("\n✗ Falha na criação do pacote")
        return
    
    # Limpeza
    cleanup()
    
    print("\n" + "=" * 55)
    print("✓ EXECUTÁVEL CRIADO COM SUCESSO!")
    print("\nArquivos gerados:")
    print("- dist/GestaoContas.exe (executável)")
    print("- GestaoContas_v1.0.0/ (pasta de distribuição)")
    print("- GestaoContas_v1.0.0.zip (pacote completo)")
    print("\nO sistema está pronto para distribuição!")

if __name__ == "__main__":
    main()
