#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Script de Verificação do Sistema de Atualizações Automáticas
Verifica se tudo está funcionando corretamente
"""

import sys
import os
from pathlib import Path

def verificar_arquivos():
    """Verifica se todos os arquivos necessários existem"""
    print("🔍 Verificando arquivos necessários...")
    
    arquivos_necessarios = [
        "src/modules/auto_update_manager.py",
        "src/modules/task_scheduler.py", 
        "src/gui/auto_update_config.py",
        "config/auto_update_config.json"
    ]
    
    todos_ok = True
    for arquivo in arquivos_necessarios:
        if Path(arquivo).exists():
            print(f"   ✓ {arquivo}")
        else:
            print(f"   ✗ {arquivo} - ARQUIVO FALTANDO!")
            todos_ok = False
    
    return todos_ok

def verificar_importacoes():
    """Verifica se as importações funcionam"""
    print("\n📦 Verificando importações...")
    
    sys.path.append('src')
    
    try:
        from database import DatabaseManager
        print("   ✓ DatabaseManager")
    except Exception as e:
        print(f"   ✗ DatabaseManager: {e}")
        return False
    
    try:
        from modules.auto_update_manager import AutoUpdateManager
        print("   ✓ AutoUpdateManager")
    except Exception as e:
        print(f"   ✗ AutoUpdateManager: {e}")
        return False
    
    try:
        from gui.auto_update_config import AutoUpdateConfigWindow
        print("   ✓ AutoUpdateConfigWindow")
    except Exception as e:
        print(f"   ✗ AutoUpdateConfigWindow: {e}")
        return False
    
    return True

def verificar_criacao_objetos():
    """Verifica se os objetos podem ser criados"""
    print("\n🏗️ Verificando criação de objetos...")
    
    sys.path.append('src')
    
    try:
        from database import DatabaseManager
        from modules.auto_update_manager import AutoUpdateManager
        
        # Criar database manager
        db_manager = DatabaseManager("data/verificacao_test.db")
        db_manager.initialize_database()
        print("   ✓ DatabaseManager criado")
        
        # Criar auto update manager
        auto_update_manager = AutoUpdateManager(db_manager)
        print("   ✓ AutoUpdateManager criado")
        
        # Verificar configuração
        config = auto_update_manager.config
        print(f"   ✓ Configuração carregada ({len(config['tasks'])} tarefas)")
        
        # Limpar arquivo de teste
        test_file = Path("data/verificacao_test.db")
        if test_file.exists():
            test_file.unlink()
        
        return True
        
    except Exception as e:
        print(f"   ✗ Erro na criação: {e}")
        import traceback
        traceback.print_exc()
        return False

def verificar_integracao():
    """Verifica se a integração com MainWindow funciona"""
    print("\n🔗 Verificando integração com MainWindow...")
    
    sys.path.append('src')
    
    try:
        import tkinter as tk
        from database import DatabaseManager
        from auth import AuthManager
        from modules.auto_update_manager import AutoUpdateManager
        from gui.main_window import MainWindow
        
        # Criar componentes
        db_manager = DatabaseManager("data/integracao_test.db")
        db_manager.initialize_database()
        
        auth_manager = AuthManager(db_manager)
        auto_update_manager = AutoUpdateManager(db_manager)
        
        # Criar usuário de teste se não existir
        if not auth_manager.user_exists('test_user'):
            auth_manager.create_user(
                username='test_user',
                password='test123',
                email='<EMAIL>',
                full_name='Usuário de Teste'
            )
        
        # Simular dados de usuário
        user_data = {
            'id': 1,
            'username': 'test_user',
            'full_name': 'Usuário de Teste',
            'is_admin': False
        }
        
        # Criar janela (sem mostrar)
        root = tk.Tk()
        root.withdraw()
        
        def dummy_logout():
            pass
        
        # Criar MainWindow
        main_window = MainWindow(
            root, db_manager, auth_manager, user_data, 
            dummy_logout, auto_update_manager
        )
        
        # Verificar se auto_update_manager foi passado corretamente
        if main_window.auto_update_manager is not None:
            print("   ✓ auto_update_manager passado corretamente para MainWindow")
        else:
            print("   ✗ auto_update_manager é None no MainWindow")
            return False
        
        # Verificar se método de configuração existe
        if hasattr(main_window, 'show_auto_update_config'):
            print("   ✓ Método show_auto_update_config existe")
        else:
            print("   ✗ Método show_auto_update_config não encontrado")
            return False
        
        # Limpar
        root.destroy()
        
        # Limpar arquivo de teste
        test_file = Path("data/integracao_test.db")
        if test_file.exists():
            test_file.unlink()
        
        return True
        
    except Exception as e:
        print(f"   ✗ Erro na integração: {e}")
        import traceback
        traceback.print_exc()
        return False

def verificar_configuracao():
    """Verifica se a configuração está correta"""
    print("\n⚙️ Verificando configuração...")
    
    config_file = Path("config/auto_update_config.json")
    if not config_file.exists():
        print("   ✗ Arquivo de configuração não encontrado")
        return False
    
    try:
        import json
        with open(config_file, 'r', encoding='utf-8') as f:
            config = json.load(f)
        
        # Verificar estrutura básica
        required_keys = ['enabled', 'update_interval_seconds', 'tasks']
        for key in required_keys:
            if key not in config:
                print(f"   ✗ Chave '{key}' faltando na configuração")
                return False
        
        print(f"   ✓ Configuração válida ({len(config['tasks'])} tarefas)")
        
        # Listar tarefas
        for task_name, task_config in config['tasks'].items():
            status = "✓" if task_config.get('enabled', False) else "○"
            interval = task_config.get('interval_minutes', 0)
            print(f"      {status} {task_name}: {interval} min")
        
        return True
        
    except Exception as e:
        print(f"   ✗ Erro ao ler configuração: {e}")
        return False

def executar_teste_rapido():
    """Executa um teste rápido do sistema"""
    print("\n🧪 Executando teste rápido...")
    
    sys.path.append('src')
    
    try:
        from database import DatabaseManager
        from modules.auto_update_manager import AutoUpdateManager
        
        # Criar componentes
        db_manager = DatabaseManager("data/teste_rapido.db")
        db_manager.initialize_database()
        
        auto_update_manager = AutoUpdateManager(db_manager)
        
        # Testar execução de uma tarefa
        print("   Testando execução de tarefa...")
        success = auto_update_manager.force_run_task('fuel_efficiency_update')
        
        if success:
            print("   ✓ Tarefa executada com sucesso")
        else:
            print("   ✗ Falha na execução da tarefa")
            return False
        
        # Verificar status
        status = auto_update_manager.get_task_status()
        if status['tasks']:
            print(f"   ✓ Status obtido ({len(status['tasks'])} tarefas)")
        else:
            print("   ✗ Erro ao obter status")
            return False
        
        # Limpar arquivo de teste
        test_file = Path("data/teste_rapido.db")
        if test_file.exists():
            test_file.unlink()
        
        return True
        
    except Exception as e:
        print(f"   ✗ Erro no teste: {e}")
        return False

def main():
    """Função principal"""
    print("=" * 60)
    print("    🔍 VERIFICAÇÃO DO SISTEMA DE ATUALIZAÇÕES AUTOMÁTICAS")
    print("=" * 60)
    
    # Executar verificações
    verificacoes = [
        ("Arquivos", verificar_arquivos),
        ("Importações", verificar_importacoes),
        ("Criação de Objetos", verificar_criacao_objetos),
        ("Integração", verificar_integracao),
        ("Configuração", verificar_configuracao),
        ("Teste Rápido", executar_teste_rapido)
    ]
    
    resultados = []
    
    for nome, funcao in verificacoes:
        try:
            resultado = funcao()
            resultados.append((nome, resultado))
        except Exception as e:
            print(f"\n❌ Erro em {nome}: {e}")
            resultados.append((nome, False))
    
    # Resumo final
    print("\n" + "=" * 60)
    print("    📊 RESUMO DA VERIFICAÇÃO")
    print("=" * 60)
    
    sucessos = 0
    for nome, resultado in resultados:
        status = "✅ PASSOU" if resultado else "❌ FALHOU"
        print(f"{status:12} {nome}")
        if resultado:
            sucessos += 1
    
    print(f"\n📈 Resultado: {sucessos}/{len(resultados)} verificações passaram")
    
    if sucessos == len(resultados):
        print("\n🎉 SISTEMA COMPLETAMENTE FUNCIONAL!")
        print("\n💡 Para acessar as configurações:")
        print("   1. Execute: py executar_terminal.py")
        print("   2. Faça login")
        print("   3. Vá em: Configurações → Sistema → ⚙️ Atualizações Automáticas")
        
        print("\n🧪 Scripts de teste disponíveis:")
        print("   • py test_auto_updates.py - Teste completo")
        print("   • py demo_auto_updates.py - Demonstração interativa")
        print("   • py test_login_auto.py - Teste com login automático")
        
    else:
        print("\n⚠️ ALGUNS PROBLEMAS ENCONTRADOS!")
        print("\n🔧 Soluções:")
        print("   • Verifique se todos os arquivos foram criados corretamente")
        print("   • Execute: py test_import.py para diagnóstico detalhado")
        print("   • Consulte COMO_ACESSAR_ATUALIZACOES.md para mais ajuda")
    
    return sucessos == len(resultados)

if __name__ == "__main__":
    sucesso = main()
    sys.exit(0 if sucesso else 1)
