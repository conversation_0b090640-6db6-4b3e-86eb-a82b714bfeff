#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Janela principal do sistema de gestão de contas
"""

import tkinter as tk
from tkinter import ttk, messagebox
from tkinter import font as tkFont
from datetime import datetime, date, timedelta
import calendar

class MainWindow:
    def __init__(self, root, db_manager, auth_manager, current_user, logout_callback):
        self.root = root
        self.db_manager = db_manager
        self.auth_manager = auth_manager
        self.current_user = current_user
        self.logout_callback = logout_callback
        
        # Configurar janela principal
        self.setup_main_window()
        
        # Criar interface
        self.create_widgets()
        
        # Carregar dados iniciais
        self.load_initial_data()
    
    def setup_main_window(self):
        """Configura a janela principal"""
        self.root.title(f"Sistema de Gestão de Contas - {self.current_user['full_name']}")
        self.root.geometry("1200x800")
        self.root.state('zoomed')  # Maximizar no Windows
        
        # Configurar estilo
        style = ttk.Style()
        style.theme_use('clam')
        
        # Configurar cores personalizadas
        style.configure('Title.TLabel', font=('Arial', 14, 'bold'))
        style.configure('Subtitle.TLabel', font=('Arial', 10))
        style.configure('Header.TFrame', background='#f0f0f0')
    
    def create_widgets(self):
        """Cria os widgets da interface principal"""
        # Frame principal
        self.main_frame = ttk.Frame(self.root)
        self.main_frame.pack(fill=tk.BOTH, expand=True)
        
        # Criar barra de menu
        self.create_menu_bar()
        
        # Criar header
        self.create_header()
        
        # Criar área de conteúdo
        self.create_content_area()
        
        # Criar barra de status
        self.create_status_bar()
    
    def create_menu_bar(self):
        """Cria a barra de menu"""
        menubar = tk.Menu(self.root)
        self.root.config(menu=menubar)
        
        # Menu Arquivo
        file_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="Arquivo", menu=file_menu)
        file_menu.add_command(label="Backup", command=self.backup_database)
        file_menu.add_command(label="Restaurar", command=self.restore_database)
        file_menu.add_separator()
        file_menu.add_command(label="Sair", command=self.logout)
        
        # Menu Carteiras
        wallet_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="Carteiras", menu=wallet_menu)
        wallet_menu.add_command(label="Gerenciar Carteiras", command=self.show_wallets)
        wallet_menu.add_command(label="Nova Carteira", command=self.new_wallet)
        
        # Menu Transações
        transaction_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="Transações", menu=transaction_menu)
        transaction_menu.add_command(label="Nova Receita", command=self.new_income)
        transaction_menu.add_command(label="Nova Despesa", command=self.new_expense)
        transaction_menu.add_separator()
        transaction_menu.add_command(label="Histórico", command=self.show_transactions)
        
        # Menu Veículos
        vehicles_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="Veículos", menu=vehicles_menu)
        vehicles_menu.add_command(label="Gerenciar Veículos", command=self.show_vehicle_manager)
        vehicles_menu.add_separator()
        vehicles_menu.add_command(label="Novo Veículo", command=self.new_vehicle)
        vehicles_menu.add_command(label="Nova Manutenção", command=self.new_maintenance)
        vehicles_menu.add_command(label="Novo Abastecimento", command=self.new_fuel_record)

        # Menu Relatórios
        reports_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="Relatórios", menu=reports_menu)
        reports_menu.add_command(label="Por Categoria", command=self.show_category_report)
        reports_menu.add_command(label="Fluxo de Caixa", command=self.show_cashflow_report)
        reports_menu.add_command(label="Contas a Vencer", command=self.show_due_bills)
        reports_menu.add_separator()
        reports_menu.add_command(label="Relatório de Veículos", command=self.show_vehicle_report)
        
        # Menu Configurações
        config_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="Configurações", menu=config_menu)
        config_menu.add_command(label="Categorias", command=self.manage_categories)
        config_menu.add_command(label="Preferências", command=self.show_preferences)
        config_menu.add_command(label="Alterar Senha", command=self.change_password)
        
        # Menu Administração (apenas para admin)
        if self.current_user['is_admin']:
            admin_menu = tk.Menu(menubar, tearoff=0)
            menubar.add_cascade(label="Administração", menu=admin_menu)
            admin_menu.add_command(label="Gerenciar Usuários", command=self.manage_users)
            admin_menu.add_command(label="Logs do Sistema", command=self.show_system_logs)
        
        # Menu Ajuda
        help_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="Ajuda", menu=help_menu)
        help_menu.add_command(label="Sobre", command=self.show_about)
    
    def create_header(self):
        """Cria o cabeçalho da aplicação"""
        header_frame = ttk.Frame(self.main_frame, style='Header.TFrame')
        header_frame.pack(fill=tk.X, padx=10, pady=(10, 0))
        
        # Título e informações do usuário
        title_frame = ttk.Frame(header_frame)
        title_frame.pack(fill=tk.X, pady=10)
        
        # Título
        title_label = ttk.Label(title_frame, text="Dashboard Financeiro", 
                              style='Title.TLabel')
        title_label.pack(side=tk.LEFT)
        
        # Informações do usuário
        user_info = f"Usuário: {self.current_user['full_name']}"
        if self.current_user['is_admin']:
            user_info += " (Administrador)"
        
        user_label = ttk.Label(title_frame, text=user_info, style='Subtitle.TLabel')
        user_label.pack(side=tk.RIGHT)
        
        # Data atual
        today = datetime.now().strftime("%d/%m/%Y")
        date_label = ttk.Label(title_frame, text=f"Data: {today}", style='Subtitle.TLabel')
        date_label.pack(side=tk.RIGHT, padx=(0, 20))
    
    def create_content_area(self):
        """Cria a área de conteúdo principal"""
        # Notebook para abas
        self.notebook = ttk.Notebook(self.main_frame)
        self.notebook.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # Aba Dashboard
        self.create_dashboard_tab()
        
        # Aba Carteiras
        self.create_wallets_tab()
        
        # Aba Transações
        self.create_transactions_tab()
        
        # Aba Relatórios
        self.create_reports_tab()
    
    def create_status_bar(self):
        """Cria a barra de status"""
        self.status_bar = ttk.Frame(self.main_frame)
        self.status_bar.pack(fill=tk.X, side=tk.BOTTOM)
        
        self.status_label = ttk.Label(self.status_bar, text="Pronto")
        self.status_label.pack(side=tk.LEFT, padx=10, pady=5)
        
        # Indicador de alertas
        self.alerts_label = ttk.Label(self.status_bar, text="", foreground="red")
        self.alerts_label.pack(side=tk.RIGHT, padx=10, pady=5)
    
    def create_dashboard_tab(self):
        """Cria a aba do dashboard"""
        dashboard_frame = ttk.Frame(self.notebook)
        self.notebook.add(dashboard_frame, text="Dashboard")
        
        # Frame para cards de resumo
        cards_frame = ttk.Frame(dashboard_frame)
        cards_frame.pack(fill=tk.X, padx=20, pady=20)
        
        # Cards de resumo financeiro
        self.create_summary_cards(cards_frame)
        
        # Frame para gráficos e listas
        content_frame = ttk.Frame(dashboard_frame)
        content_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=(0, 20))
        
        # Frame esquerdo - Transações recentes
        left_frame = ttk.LabelFrame(content_frame, text="Transações Recentes", padding=10)
        left_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=(0, 10))
        
        # Lista de transações recentes
        self.create_recent_transactions_list(left_frame)
        
        # Frame direito - Alertas e contas a vencer
        right_frame = ttk.LabelFrame(content_frame, text="Alertas e Lembretes", padding=10)
        right_frame.pack(side=tk.RIGHT, fill=tk.BOTH, expand=True, padx=(10, 0))
        
        # Lista de alertas
        self.create_alerts_list(right_frame)
    
    def create_summary_cards(self, parent):
        """Cria os cards de resumo financeiro"""
        # Card Saldo Total
        total_card = ttk.LabelFrame(parent, text="Saldo Total", padding=10)
        total_card.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=(0, 10))
        
        self.total_balance_label = ttk.Label(total_card, text="R$ 0,00", 
                                           font=('Arial', 16, 'bold'), foreground='blue')
        self.total_balance_label.pack()
        
        # Card Receitas do Mês
        income_card = ttk.LabelFrame(parent, text="Receitas do Mês", padding=10)
        income_card.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=(0, 10))
        
        self.month_income_label = ttk.Label(income_card, text="R$ 0,00", 
                                          font=('Arial', 16, 'bold'), foreground='green')
        self.month_income_label.pack()
        
        # Card Despesas do Mês
        expense_card = ttk.LabelFrame(parent, text="Despesas do Mês", padding=10)
        expense_card.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=(0, 10))
        
        self.month_expense_label = ttk.Label(expense_card, text="R$ 0,00", 
                                           font=('Arial', 16, 'bold'), foreground='red')
        self.month_expense_label.pack()
        
        # Card Saldo do Mês
        balance_card = ttk.LabelFrame(parent, text="Saldo do Mês", padding=10)
        balance_card.pack(side=tk.LEFT, fill=tk.X, expand=True)
        
        self.month_balance_label = ttk.Label(balance_card, text="R$ 0,00", 
                                           font=('Arial', 16, 'bold'))
        self.month_balance_label.pack()
    
    def create_recent_transactions_list(self, parent):
        """Cria a lista de transações recentes"""
        # Treeview para transações
        columns = ('Data', 'Descrição', 'Categoria', 'Valor', 'Tipo')
        self.recent_tree = ttk.Treeview(parent, columns=columns, show='headings', height=10)
        
        # Configurar colunas
        self.recent_tree.heading('Data', text='Data')
        self.recent_tree.heading('Descrição', text='Descrição')
        self.recent_tree.heading('Categoria', text='Categoria')
        self.recent_tree.heading('Valor', text='Valor')
        self.recent_tree.heading('Tipo', text='Tipo')
        
        self.recent_tree.column('Data', width=80)
        self.recent_tree.column('Descrição', width=200)
        self.recent_tree.column('Categoria', width=120)
        self.recent_tree.column('Valor', width=100)
        self.recent_tree.column('Tipo', width=80)
        
        # Scrollbar
        recent_scroll = ttk.Scrollbar(parent, orient=tk.VERTICAL, command=self.recent_tree.yview)
        self.recent_tree.configure(yscrollcommand=recent_scroll.set)
        
        # Pack
        self.recent_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        recent_scroll.pack(side=tk.RIGHT, fill=tk.Y)
    
    def create_alerts_list(self, parent):
        """Cria a lista de alertas"""
        # Treeview para alertas
        columns = ('Tipo', 'Descrição', 'Data')
        self.alerts_tree = ttk.Treeview(parent, columns=columns, show='headings', height=10)
        
        # Configurar colunas
        self.alerts_tree.heading('Tipo', text='Tipo')
        self.alerts_tree.heading('Descrição', text='Descrição')
        self.alerts_tree.heading('Data', text='Data')
        
        self.alerts_tree.column('Tipo', width=80)
        self.alerts_tree.column('Descrição', width=250)
        self.alerts_tree.column('Data', width=80)
        
        # Scrollbar
        alerts_scroll = ttk.Scrollbar(parent, orient=tk.VERTICAL, command=self.alerts_tree.yview)
        self.alerts_tree.configure(yscrollcommand=alerts_scroll.set)
        
        # Pack
        self.alerts_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        alerts_scroll.pack(side=tk.RIGHT, fill=tk.Y)

    def create_wallets_tab(self):
        """Cria a aba de carteiras"""
        wallets_frame = ttk.Frame(self.notebook)
        self.notebook.add(wallets_frame, text="Carteiras")

        # Toolbar
        toolbar_frame = ttk.Frame(wallets_frame)
        toolbar_frame.pack(fill=tk.X, padx=10, pady=10)

        ttk.Button(toolbar_frame, text="Nova Carteira", command=self.new_wallet).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(toolbar_frame, text="Editar", command=self.edit_wallet).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(toolbar_frame, text="Excluir", command=self.delete_wallet).pack(side=tk.LEFT)

        # Lista de carteiras
        columns = ('Nome', 'Tipo', 'Saldo Inicial', 'Saldo Atual', 'Status')
        self.wallets_tree = ttk.Treeview(wallets_frame, columns=columns, show='headings')

        for col in columns:
            self.wallets_tree.heading(col, text=col)
            self.wallets_tree.column(col, width=150)

        # Scrollbars
        wallet_v_scroll = ttk.Scrollbar(wallets_frame, orient=tk.VERTICAL, command=self.wallets_tree.yview)
        wallet_h_scroll = ttk.Scrollbar(wallets_frame, orient=tk.HORIZONTAL, command=self.wallets_tree.xview)
        self.wallets_tree.configure(yscrollcommand=wallet_v_scroll.set, xscrollcommand=wallet_h_scroll.set)

        # Pack
        self.wallets_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=(10, 0), pady=(0, 10))
        wallet_v_scroll.pack(side=tk.RIGHT, fill=tk.Y, pady=(0, 10))
        wallet_h_scroll.pack(side=tk.BOTTOM, fill=tk.X, padx=(10, 0))

    def create_transactions_tab(self):
        """Cria a aba de transações"""
        transactions_frame = ttk.Frame(self.notebook)
        self.notebook.add(transactions_frame, text="Transações")

        # Toolbar
        toolbar_frame = ttk.Frame(transactions_frame)
        toolbar_frame.pack(fill=tk.X, padx=10, pady=10)

        ttk.Button(toolbar_frame, text="Nova Receita", command=self.new_income).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(toolbar_frame, text="Nova Despesa", command=self.new_expense).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(toolbar_frame, text="Editar", command=self.edit_transaction).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(toolbar_frame, text="Excluir", command=self.delete_transaction).pack(side=tk.LEFT)

        # Filtros
        filter_frame = ttk.LabelFrame(transactions_frame, text="Filtros", padding=10)
        filter_frame.pack(fill=tk.X, padx=10, pady=(0, 10))

        # Filtro por período
        ttk.Label(filter_frame, text="Período:").pack(side=tk.LEFT, padx=(0, 5))
        self.period_combo = ttk.Combobox(filter_frame, values=["Todos", "Este Mês", "Últimos 30 dias", "Este Ano"],
                                       state="readonly", width=15)
        self.period_combo.set("Este Mês")
        self.period_combo.pack(side=tk.LEFT, padx=(0, 20))

        # Filtro por tipo
        ttk.Label(filter_frame, text="Tipo:").pack(side=tk.LEFT, padx=(0, 5))
        self.type_combo = ttk.Combobox(filter_frame, values=["Todos", "Receitas", "Despesas"],
                                     state="readonly", width=15)
        self.type_combo.set("Todos")
        self.type_combo.pack(side=tk.LEFT, padx=(0, 20))

        # Botão filtrar
        ttk.Button(filter_frame, text="Filtrar", command=self.filter_transactions).pack(side=tk.LEFT)

        # Lista de transações
        columns = ('Data', 'Descrição', 'Categoria', 'Carteira', 'Valor', 'Tipo', 'Status')
        self.transactions_tree = ttk.Treeview(transactions_frame, columns=columns, show='headings')

        for col in columns:
            self.transactions_tree.heading(col, text=col)
            if col == 'Descrição':
                self.transactions_tree.column(col, width=200)
            else:
                self.transactions_tree.column(col, width=100)

        # Scrollbars
        trans_v_scroll = ttk.Scrollbar(transactions_frame, orient=tk.VERTICAL, command=self.transactions_tree.yview)
        trans_h_scroll = ttk.Scrollbar(transactions_frame, orient=tk.HORIZONTAL, command=self.transactions_tree.xview)
        self.transactions_tree.configure(yscrollcommand=trans_v_scroll.set, xscrollcommand=trans_h_scroll.set)

        # Pack
        self.transactions_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=(10, 0), pady=(0, 10))
        trans_v_scroll.pack(side=tk.RIGHT, fill=tk.Y, pady=(0, 10))
        trans_h_scroll.pack(side=tk.BOTTOM, fill=tk.X, padx=(10, 0))

    def create_reports_tab(self):
        """Cria a aba de relatórios"""
        reports_frame = ttk.Frame(self.notebook)
        self.notebook.add(reports_frame, text="Relatórios")

        # Notebook para sub-relatórios
        reports_notebook = ttk.Notebook(reports_frame)
        reports_notebook.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        # Relatório por categoria
        self.create_category_report_tab(reports_notebook)

        # Relatório de fluxo de caixa
        self.create_cashflow_report_tab(reports_notebook)

        # Contas a vencer
        self.create_due_bills_tab(reports_notebook)

        # Aba de Configurações
        self.create_settings_tab()

        # Aba de Administração (se for admin)
        if self.current_user['is_admin']:
            self.create_admin_tab()

    def create_category_report_tab(self, parent_notebook):
        """Cria aba de relatório por categoria"""
        category_frame = ttk.Frame(parent_notebook)
        parent_notebook.add(category_frame, text="Por Categoria")

        # Toolbar
        toolbar = ttk.Frame(category_frame)
        toolbar.pack(fill=tk.X, padx=10, pady=10)

        ttk.Label(toolbar, text="Período:").pack(side=tk.LEFT, padx=(0, 5))
        period_combo = ttk.Combobox(toolbar, values=["Este Mês", "Últimos 3 Meses", "Este Ano"],
                                   state="readonly", width=15)
        period_combo.set("Este Mês")
        period_combo.pack(side=tk.LEFT, padx=(0, 10))

        ttk.Button(toolbar, text="Atualizar",
                  command=lambda: self.update_category_report(category_tree, period_combo.get())).pack(side=tk.LEFT)

        # Treeview para relatório
        columns = ('Categoria', 'Tipo', 'Transações', 'Total', 'Média')
        category_tree = ttk.Treeview(category_frame, columns=columns, show='headings')

        for col in columns:
            category_tree.heading(col, text=col)
            category_tree.column(col, width=120)

        # Scrollbar
        scrollbar = ttk.Scrollbar(category_frame, orient=tk.VERTICAL, command=category_tree.yview)
        category_tree.configure(yscrollcommand=scrollbar.set)

        # Pack
        category_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=(10, 0), pady=(0, 10))
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y, pady=(0, 10))

        # Carregar dados iniciais
        self.update_category_report(category_tree, "Este Mês")

    def create_cashflow_report_tab(self, parent_notebook):
        """Cria aba de fluxo de caixa"""
        cashflow_frame = ttk.Frame(parent_notebook)
        parent_notebook.add(cashflow_frame, text="Fluxo de Caixa")

        # Cards de resumo
        cards_frame = ttk.Frame(cashflow_frame)
        cards_frame.pack(fill=tk.X, padx=20, pady=20)

        # Card Receitas
        income_card = ttk.LabelFrame(cards_frame, text="Total de Receitas", padding=10)
        income_card.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=(0, 10))

        self.income_total_label = ttk.Label(income_card, text="R$ 0,00",
                                          font=('Arial', 14, 'bold'), foreground='green')
        self.income_total_label.pack()

        # Card Despesas
        expense_card = ttk.LabelFrame(cards_frame, text="Total de Despesas", padding=10)
        expense_card.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=(0, 10))

        self.expense_total_label = ttk.Label(expense_card, text="R$ 0,00",
                                           font=('Arial', 14, 'bold'), foreground='red')
        self.expense_total_label.pack()

        # Card Saldo
        balance_card = ttk.LabelFrame(cards_frame, text="Saldo Líquido", padding=10)
        balance_card.pack(side=tk.LEFT, fill=tk.X, expand=True)

        self.balance_total_label = ttk.Label(balance_card, text="R$ 0,00",
                                           font=('Arial', 14, 'bold'))
        self.balance_total_label.pack()

        # Atualizar dados
        self.update_cashflow_report()

    def create_due_bills_tab(self, parent_notebook):
        """Cria aba de contas a vencer"""
        due_bills_frame = ttk.Frame(parent_notebook)
        parent_notebook.add(due_bills_frame, text="Contas a Vencer")

        # Toolbar
        toolbar = ttk.Frame(due_bills_frame)
        toolbar.pack(fill=tk.X, padx=10, pady=10)

        ttk.Label(toolbar, text="Próximos:").pack(side=tk.LEFT, padx=(0, 5))
        days_combo = ttk.Combobox(toolbar, values=["7 dias", "15 dias", "30 dias"],
                                 state="readonly", width=10)
        days_combo.set("7 dias")
        days_combo.pack(side=tk.LEFT, padx=(0, 10))

        ttk.Button(toolbar, text="Atualizar",
                  command=lambda: self.update_due_bills(due_tree, days_combo.get())).pack(side=tk.LEFT)

        # Treeview para contas a vencer
        columns = ('Vencimento', 'Descrição', 'Categoria', 'Valor', 'Status')
        due_tree = ttk.Treeview(due_bills_frame, columns=columns, show='headings')

        for col in columns:
            due_tree.heading(col, text=col)
            due_tree.column(col, width=120)

        # Scrollbar
        due_scrollbar = ttk.Scrollbar(due_bills_frame, orient=tk.VERTICAL, command=due_tree.yview)
        due_tree.configure(yscrollcommand=due_scrollbar.set)

        # Pack
        due_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=(10, 0), pady=(0, 10))
        due_scrollbar.pack(side=tk.RIGHT, fill=tk.Y, pady=(0, 10))

        # Carregar dados iniciais
        self.update_due_bills(due_tree, "7 dias")

    def create_settings_tab(self):
        """Cria aba de configurações"""
        settings_frame = ttk.Frame(self.notebook)
        self.notebook.add(settings_frame, text="Configurações")

        # Frame principal
        main_settings_frame = ttk.Frame(settings_frame, padding="20")
        main_settings_frame.pack(fill=tk.BOTH, expand=True)

        # Título
        title_label = ttk.Label(main_settings_frame, text="Configurações do Sistema",
                               font=("Arial", 16, "bold"))
        title_label.pack(pady=(0, 20))

        # Seção Categorias
        categories_section = ttk.LabelFrame(main_settings_frame, text="Categorias", padding=10)
        categories_section.pack(fill=tk.X, pady=(0, 15))

        ttk.Button(categories_section, text="Gerenciar Categorias",
                  command=self.show_categories_manager).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(categories_section, text="Importar Categorias Padrão",
                  command=self.import_default_categories).pack(side=tk.LEFT)

        # Seção Usuário
        user_section = ttk.LabelFrame(main_settings_frame, text="Conta do Usuário", padding=10)
        user_section.pack(fill=tk.X, pady=(0, 15))

        ttk.Button(user_section, text="Alterar Senha",
                  command=self.show_change_password).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(user_section, text="Editar Perfil",
                  command=self.show_edit_profile).pack(side=tk.LEFT)

        # Seção Backup
        backup_section = ttk.LabelFrame(main_settings_frame, text="Backup e Restauração", padding=10)
        backup_section.pack(fill=tk.X, pady=(0, 15))

        ttk.Button(backup_section, text="Criar Backup",
                  command=self.backup_database).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(backup_section, text="Restaurar Backup",
                  command=self.restore_database).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(backup_section, text="Gerenciar Backups",
                  command=self.show_backup_manager).pack(side=tk.LEFT)

    def create_admin_tab(self):
        """Cria aba de administração (apenas para admin)"""
        admin_frame = ttk.Frame(self.notebook)
        self.notebook.add(admin_frame, text="Administração")

        # Frame principal
        main_admin_frame = ttk.Frame(admin_frame, padding="20")
        main_admin_frame.pack(fill=tk.BOTH, expand=True)

        # Título
        title_label = ttk.Label(main_admin_frame, text="Administração do Sistema",
                               font=("Arialblack", 14, "bold"))
        title_label.pack(pady=(0, 20))

        # Seção Usuários
        users_section = ttk.LabelFrame(main_admin_frame, text="Gerenciamento de Usuários", padding=10)
        users_section.pack(fill=tk.X, pady=(0, 15))

        ttk.Button(users_section, text="Gerenciar Usuários",
                  command=self.show_users_manager).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(users_section, text="Criar Novo Usuário",
                  command=self.show_create_user).pack(side=tk.LEFT)

        # Seção Logs
        logs_section = ttk.LabelFrame(main_admin_frame, text="Logs do Sistema", padding=10)
        logs_section.pack(fill=tk.X, pady=(0, 15))

        ttk.Button(logs_section, text="Ver Logs",
                  command=self.show_system_logs).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(logs_section, text="Limpar Logs",
                  command=self.clear_system_logs).pack(side=tk.LEFT)

        # Seção Sistema
        system_section = ttk.LabelFrame(main_admin_frame, text="Sistema", padding=10)
        system_section.pack(fill=tk.X, pady=(0, 15))

        ttk.Button(system_section, text="Estatísticas do Sistema",
                  command=self.show_system_stats).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(system_section, text="Manutenção do Banco",
                  command=self.show_database_maintenance).pack(side=tk.LEFT)

    def load_initial_data(self):
        """Carrega dados iniciais da aplicação"""
        try:
            self.update_summary_cards()
            self.load_recent_transactions()
            self.load_alerts()
            self.load_wallets()
            self.load_transactions()
            self.check_due_bills()
        except Exception as e:
            messagebox.showerror("Erro", f"Erro ao carregar dados: {str(e)}")

    def update_summary_cards(self):
        """Atualiza os cards de resumo financeiro"""
        try:
            user_id = self.current_user['id']

            # Saldo total das carteiras
            query = "SELECT SUM(current_balance) FROM wallets WHERE user_id = ? AND is_active = TRUE"
            result = self.db_manager.execute_query(query, (user_id,))
            total_balance = result[0][0] if result[0][0] else 0
            self.total_balance_label.config(text=f"R$ {total_balance:,.2f}")

            # Receitas do mês atual
            current_month = datetime.now().strftime('%Y-%m')
            query = '''
                SELECT SUM(amount) FROM transactions
                WHERE user_id = ? AND transaction_type = 'income'
                AND strftime('%Y-%m', transaction_date) = ?
            '''
            result = self.db_manager.execute_query(query, (user_id, current_month))
            month_income = result[0][0] if result[0][0] else 0
            self.month_income_label.config(text=f"R$ {month_income:,.2f}")

            # Despesas do mês atual
            query = '''
                SELECT SUM(amount) FROM transactions
                WHERE user_id = ? AND transaction_type = 'expense'
                AND strftime('%Y-%m', transaction_date) = ?
            '''
            result = self.db_manager.execute_query(query, (user_id, current_month))
            month_expense = result[0][0] if result[0][0] else 0
            self.month_expense_label.config(text=f"R$ {month_expense:,.2f}")

            # Saldo do mês
            month_balance = month_income - month_expense
            color = 'green' if month_balance >= 0 else 'red'
            self.month_balance_label.config(text=f"R$ {month_balance:,.2f}", foreground=color)

        except Exception as e:
            print(f"Erro ao atualizar cards de resumo: {str(e)}")

    def load_recent_transactions(self):
        """Carrega transações recentes"""
        try:
            # Limpar lista atual
            for item in self.recent_tree.get_children():
                self.recent_tree.delete(item)

            user_id = self.current_user['id']
            query = '''
                SELECT t.transaction_date, t.description, c.name, t.amount, t.transaction_type
                FROM transactions t
                JOIN categories c ON t.category_id = c.id
                WHERE t.user_id = ?
                ORDER BY t.created_at DESC
                LIMIT 10
            '''

            transactions = self.db_manager.execute_query(query, (user_id,))

            for trans in transactions:
                date_str = trans['transaction_date']
                description = trans['description']
                category = trans['name']
                amount = f"R$ {trans['amount']:,.2f}"
                trans_type = "Receita" if trans['transaction_type'] == 'income' else "Despesa"

                # Inserir na árvore
                item = self.recent_tree.insert('', 'end', values=(date_str, description, category, amount, trans_type))

                # Colorir baseado no tipo
                if trans['transaction_type'] == 'income':
                    self.recent_tree.set(item, 'Valor', amount)
                    self.recent_tree.item(item, tags=('income',))
                else:
                    self.recent_tree.item(item, tags=('expense',))

            # Configurar tags de cor
            self.recent_tree.tag_configure('income', foreground='green')
            self.recent_tree.tag_configure('expense', foreground='red')

        except Exception as e:
            print(f"Erro ao carregar transações recentes: {str(e)}")

    def load_alerts(self):
        """Carrega alertas e lembretes"""
        try:
            # Limpar lista atual
            for item in self.alerts_tree.get_children():
                self.alerts_tree.delete(item)

            user_id = self.current_user['id']
            today = date.today()

            # Contas vencendo nos próximos 7 dias
            query = '''
                SELECT t.description, t.due_date, t.amount, c.name
                FROM transactions t
                JOIN categories c ON t.category_id = c.id
                WHERE t.user_id = ? AND t.is_paid = FALSE
                AND t.due_date IS NOT NULL
                AND t.due_date BETWEEN ? AND ?
                ORDER BY t.due_date
            '''

            end_date = today + timedelta(days=7)
            due_bills = self.db_manager.execute_query(query, (user_id, today.isoformat(), end_date.isoformat()))

            alert_count = 0
            for bill in due_bills:
                due_date = datetime.strptime(bill['due_date'], '%Y-%m-%d').date()
                days_left = (due_date - today).days

                if days_left == 0:
                    alert_type = "VENCE HOJE"
                elif days_left == 1:
                    alert_type = "VENCE AMANHÃ"
                else:
                    alert_type = f"VENCE EM {days_left} DIAS"

                description = f"{bill['description']} - R$ {bill['amount']:,.2f}"
                date_str = due_date.strftime('%d/%m')

                self.alerts_tree.insert('', 'end', values=(alert_type, description, date_str))
                alert_count += 1

            # Atualizar indicador de alertas na barra de status
            if alert_count > 0:
                self.alerts_label.config(text=f"{alert_count} alerta(s)")
            else:
                self.alerts_label.config(text="")

        except Exception as e:
            print(f"Erro ao carregar alertas: {str(e)}")

    def load_wallets(self):
        """Carrega lista de carteiras"""
        try:
            # Limpar lista atual
            for item in self.wallets_tree.get_children():
                self.wallets_tree.delete(item)

            user_id = self.current_user['id']
            query = '''
                SELECT id, name, wallet_type, initial_balance, current_balance, is_active
                FROM wallets
                WHERE user_id = ?
                ORDER BY name
            '''

            wallets = self.db_manager.execute_query(query, (user_id,))

            for wallet in wallets:
                wallet_type = {
                    'checking': 'Conta Corrente',
                    'savings': 'Poupança',
                    'credit': 'Cartão de Crédito',
                    'cash': 'Dinheiro'
                }.get(wallet['wallet_type'], wallet['wallet_type'])

                status = "Ativa" if wallet['is_active'] else "Inativa"

                values = (
                    wallet['name'],
                    wallet_type,
                    f"R$ {wallet['initial_balance']:,.2f}",
                    f"R$ {wallet['current_balance']:,.2f}",
                    status
                )

                self.wallets_tree.insert('', 'end', values=values)

        except Exception as e:
            print(f"Erro ao carregar carteiras: {str(e)}")

    def load_transactions(self):
        """Carrega lista de transações"""
        try:
            # Limpar lista atual
            for item in self.transactions_tree.get_children():
                self.transactions_tree.delete(item)

            user_id = self.current_user['id']

            # Construir query baseada nos filtros
            base_query = '''
                SELECT t.id, t.transaction_date, t.description, c.name as category_name,
                       w.name as wallet_name, t.amount, t.transaction_type, t.is_paid
                FROM transactions t
                JOIN categories c ON t.category_id = c.id
                JOIN wallets w ON t.wallet_id = w.id
                WHERE t.user_id = ?
            '''

            params = [user_id]

            # Aplicar filtros
            period_filter = self.period_combo.get()
            if period_filter == "Este Mês":
                current_month = datetime.now().strftime('%Y-%m')
                base_query += " AND strftime('%Y-%m', t.transaction_date) = ?"
                params.append(current_month)
            elif period_filter == "Últimos 30 dias":
                thirty_days_ago = (datetime.now() - timedelta(days=30)).date()
                base_query += " AND t.transaction_date >= ?"
                params.append(thirty_days_ago.isoformat())
            elif period_filter == "Este Ano":
                current_year = datetime.now().strftime('%Y')
                base_query += " AND strftime('%Y', t.transaction_date) = ?"
                params.append(current_year)

            type_filter = self.type_combo.get()
            if type_filter == "Receitas":
                base_query += " AND t.transaction_type = 'income'"
            elif type_filter == "Despesas":
                base_query += " AND t.transaction_type = 'expense'"

            base_query += " ORDER BY t.transaction_date DESC, t.created_at DESC"

            transactions = self.db_manager.execute_query(base_query, params)

            for trans in transactions:
                date_str = datetime.strptime(trans['transaction_date'], '%Y-%m-%d').strftime('%d/%m/%Y')
                trans_type = "Receita" if trans['transaction_type'] == 'income' else "Despesa"
                status = "Pago" if trans['is_paid'] else "Pendente"

                values = (
                    date_str,
                    trans['description'],
                    trans['category_name'],
                    trans['wallet_name'],
                    f"R$ {trans['amount']:,.2f}",
                    trans_type,
                    status
                )

                item = self.transactions_tree.insert('', 'end', values=values)

                # Colorir baseado no tipo
                if trans['transaction_type'] == 'income':
                    self.transactions_tree.item(item, tags=('income',))
                else:
                    self.transactions_tree.item(item, tags=('expense',))

            # Configurar tags de cor
            self.transactions_tree.tag_configure('income', foreground='green')
            self.transactions_tree.tag_configure('expense', foreground='red')

        except Exception as e:
            print(f"Erro ao carregar transações: {str(e)}")

    def check_due_bills(self):
        """Verifica contas vencendo e atualiza status"""
        try:
            user_id = self.current_user['id']
            today = date.today()

            # Contar contas vencendo hoje
            query = '''
                SELECT COUNT(*) FROM transactions
                WHERE user_id = ? AND is_paid = FALSE
                AND due_date = ?
            '''
            result = self.db_manager.execute_query(query, (user_id, today.isoformat()))
            due_today = result[0][0]

            if due_today > 0:
                self.status_label.config(text=f"Atenção: {due_today} conta(s) vencem hoje!")

        except Exception as e:
            print(f"Erro ao verificar contas vencendo: {str(e)}")

    # Métodos de callback para os menus e botões
    def logout(self):
        """Faz logout do usuário"""
        if messagebox.askyesno("Confirmar", "Deseja realmente sair do sistema?"):
            self.logout_callback()

    def backup_database(self):
        """Cria backup do banco de dados"""
        from tkinter import filedialog

        try:
            backup_path = filedialog.asksaveasfilename(
                title="Salvar Backup",
                defaultextension=".db",
                filetypes=[("Banco de Dados", "*.db"), ("Todos os arquivos", "*.*")]
            )

            if backup_path:
                self.db_manager.backup_database(backup_path)
                messagebox.showinfo("Sucesso", "Backup criado com sucesso!")

        except Exception as e:
            messagebox.showerror("Erro", f"Erro ao criar backup: {str(e)}")

    def restore_database(self):
        """Restaura banco de dados a partir de backup"""
        from tkinter import filedialog

        if not messagebox.askyesno("Confirmar",
                                 "Esta operação substituirá todos os dados atuais. Continuar?"):
            return

        try:
            backup_path = filedialog.askopenfilename(
                title="Selecionar Backup",
                filetypes=[("Banco de Dados", "*.db"), ("Todos os arquivos", "*.*")]
            )

            if backup_path:
                self.db_manager.restore_database(backup_path)
                messagebox.showinfo("Sucesso", "Banco de dados restaurado com sucesso!")
                self.load_initial_data()  # Recarregar dados

        except Exception as e:
            messagebox.showerror("Erro", f"Erro ao restaurar backup: {str(e)}")

    # Métodos implementados para funcionalidades completas
    def show_wallets(self):
        """Mostra gerenciamento de carteiras"""
        self.notebook.select(1)  # Selecionar aba de carteiras

    def new_wallet(self):
        """Cria nova carteira"""
        from src.gui.wallet_dialog import WalletDialog
        dialog = WalletDialog(self.root, self.db_manager, self.current_user['id'])
        if dialog.result:
            self.load_wallets()
            self.update_summary_cards()

    def edit_wallet(self):
        """Edita carteira selecionada"""
        selection = self.wallets_tree.selection()
        if not selection:
            messagebox.showwarning("Aviso", "Selecione uma carteira para editar")
            return

        # Obter ID da carteira selecionada
        item = selection[0]
        wallet_name = self.wallets_tree.item(item)['values'][0]

        # Buscar carteira no banco
        query = "SELECT id FROM wallets WHERE user_id = ? AND name = ?"
        result = self.db_manager.execute_query(query, (self.current_user['id'], wallet_name))

        if result:
            wallet_id = result[0]['id']
            from src.gui.wallet_dialog import WalletDialog
            dialog = WalletDialog(self.root, self.db_manager, self.current_user['id'], wallet_id)
            if dialog.result:
                self.load_wallets()
                self.update_summary_cards()

    def delete_wallet(self):
        """Exclui carteira selecionada"""
        selection = self.wallets_tree.selection()
        if not selection:
            messagebox.showwarning("Aviso", "Selecione uma carteira para excluir")
            return

        if messagebox.askyesno("Confirmar", "Deseja realmente excluir esta carteira?"):
            item = selection[0]
            wallet_name = self.wallets_tree.item(item)['values'][0]

            try:
                from src.modules.wallet_manager import WalletManager
                wallet_manager = WalletManager(self.db_manager)

                # Buscar ID da carteira
                query = "SELECT id FROM wallets WHERE user_id = ? AND name = ?"
                result = self.db_manager.execute_query(query, (self.current_user['id'], wallet_name))

                if result:
                    wallet_id = result[0]['id']
                    wallet_manager.delete_wallet(wallet_id, self.current_user['id'])
                    messagebox.showinfo("Sucesso", "Carteira excluída com sucesso!")
                    self.load_wallets()
                    self.update_summary_cards()

            except Exception as e:
                messagebox.showerror("Erro", str(e))

    def new_income(self):
        """Cria nova receita"""
        try:
            # Importar o formulário correto de transação
            from src.gui.transaction_form_new import TransactionFormNew
            form = TransactionFormNew(self.root, self.db_manager, self.current_user['id'], 'income')
            self.root.wait_window(form.window)
            if form.result:
                self.load_transactions()
                self.load_recent_transactions()
                self.update_summary_cards()
        except Exception as e:
            messagebox.showerror("Erro", f"Erro ao abrir formulário de receita: {str(e)}")

    def new_expense(self):
        """Cria nova despesa"""
        try:
            # Importar o formulário correto de transação
            from src.gui.transaction_form_new import TransactionFormNew
            form = TransactionFormNew(self.root, self.db_manager, self.current_user['id'], 'expense')
            self.root.wait_window(form.window)
            if form.result:
                self.load_transactions()
                self.load_recent_transactions()
                self.update_summary_cards()
        except Exception as e:
            messagebox.showerror("Erro", f"Erro ao abrir formulário de despesa: {str(e)}")

    def edit_transaction(self):
        """Edita transação selecionada"""
        selection = self.transactions_tree.selection()
        if not selection:
            messagebox.showwarning("Aviso", "Selecione uma transação para editar")
            return
        messagebox.showinfo("Em Desenvolvimento", "Funcionalidade em desenvolvimento")

    def delete_transaction(self):
        """Exclui transação selecionada"""
        selection = self.transactions_tree.selection()
        if not selection:
            messagebox.showwarning("Aviso", "Selecione uma transação para excluir")
            return
        messagebox.showinfo("Em Desenvolvimento", "Funcionalidade em desenvolvimento")

    def filter_transactions(self):
        """Aplica filtros às transações"""
        self.load_transactions()

    def show_transactions(self):
        """Mostra aba de transações"""
        self.notebook.select(2)  # Selecionar aba de transações

    def show_category_report(self):
        """Mostra relatório por categoria"""
        self.notebook.select(3)  # Selecionar aba de relatórios
        messagebox.showinfo("Em Desenvolvimento", "Relatório por categoria em desenvolvimento")

    def show_cashflow_report(self):
        """Mostra relatório de fluxo de caixa"""
        self.notebook.select(3)  # Selecionar aba de relatórios
        messagebox.showinfo("Em Desenvolvimento", "Relatório de fluxo de caixa em desenvolvimento")

    def show_due_bills(self):
        """Mostra contas a vencer"""
        self.notebook.select(3)  # Selecionar aba de relatórios
        messagebox.showinfo("Em Desenvolvimento", "Relatório de contas a vencer em desenvolvimento")

    def manage_categories(self):
        """Gerencia categorias"""
        messagebox.showinfo("Em Desenvolvimento", "Gerenciamento de categorias em desenvolvimento")

    def show_preferences(self):
        """Mostra preferências do sistema"""
        messagebox.showinfo("Em Desenvolvimento", "Configurações em desenvolvimento")

    def change_password(self):
        """Altera senha do usuário"""
        messagebox.showinfo("Em Desenvolvimento", "Alteração de senha em desenvolvimento")

    def manage_users(self):
        """Gerencia usuários (apenas admin)"""
        if not self.current_user['is_admin']:
            messagebox.showerror("Erro", "Acesso negado. Apenas administradores podem acessar esta função.")
            return
        messagebox.showinfo("Em Desenvolvimento", "Gerenciamento de usuários em desenvolvimento")

    def show_system_logs(self):
        """Mostra logs do sistema (apenas admin)"""
        if not self.current_user['is_admin']:
            messagebox.showerror("Erro", "Acesso negado. Apenas administradores podem acessar esta função.")
            return
        messagebox.showinfo("Em Desenvolvimento", "Logs do sistema em desenvolvimento")

    def show_about(self):
        """Mostra informações sobre o sistema"""
        about_text = """Sistema de Gestão de Contas

Versão: 1.0.0
Desenvolvido em Python com Tkinter

Funcionalidades:
• Gestão de carteiras e contas
• Controle de receitas e despesas
• Relatórios e análises
• Sistema de alertas
• Backup e restauração
• Controle de usuários

© 2024 - Sistema de Gestão Financeira"""

        messagebox.showinfo("Sobre", about_text)

    # Métodos para relatórios
    def update_category_report(self, tree, period):
        """Atualiza relatório por categoria"""
        try:
            # Limpar árvore
            for item in tree.get_children():
                tree.delete(item)

            from src.modules.category_manager import CategoryManager
            category_manager = CategoryManager(self.db_manager)

            # Definir período
            from datetime import datetime, timedelta
            today = datetime.now().date()

            if period == "Este Mês":
                start_date = today.replace(day=1)
                end_date = today
            elif period == "Últimos 3 Meses":
                start_date = today - timedelta(days=90)
                end_date = today
            elif period == "Este Ano":
                start_date = today.replace(month=1, day=1)
                end_date = today
            else:
                start_date = None
                end_date = None

            # Obter estatísticas
            stats = category_manager.get_category_usage_stats(
                self.current_user['id'],
                period_start=start_date,
                period_end=end_date
            )

            for stat in stats:
                trans_type = "Receita" if stat['category_type'] == 'income' else "Despesa"
                tree.insert('', 'end', values=(
                    stat['name'],
                    trans_type,
                    stat['transaction_count'],
                    f"R$ {stat['total_amount']:,.2f}",
                    f"R$ {stat['avg_amount']:,.2f}"
                ))

        except Exception as e:
            print(f"Erro ao atualizar relatório por categoria: {str(e)}")

    def update_cashflow_report(self):
        """Atualiza relatório de fluxo de caixa"""
        try:
            from src.modules.transaction_manager import TransactionManager
            transaction_manager = TransactionManager(self.db_manager)

            # Obter resumo do mês atual
            from datetime import datetime
            today = datetime.now().date()
            start_month = today.replace(day=1)

            summary = transaction_manager.get_transactions_summary(
                self.current_user['id'],
                period_start=start_month,
                period_end=today
            )

            # Atualizar labels
            income_total = summary['income']['paid_total']
            expense_total = summary['expense']['paid_total']
            balance = income_total - expense_total

            self.income_total_label.config(text=f"R$ {income_total:,.2f}")
            self.expense_total_label.config(text=f"R$ {expense_total:,.2f}")

            color = 'green' if balance >= 0 else 'red'
            self.balance_total_label.config(text=f"R$ {balance:,.2f}", foreground=color)

        except Exception as e:
            print(f"Erro ao atualizar fluxo de caixa: {str(e)}")

    def update_due_bills(self, tree, period):
        """Atualiza contas a vencer"""
        try:
            # Limpar árvore
            for item in tree.get_children():
                tree.delete(item)

            from src.modules.alert_manager import AlertManager
            alert_manager = AlertManager(self.db_manager)

            # Extrair número de dias
            days = int(period.split()[0])

            # Obter alertas de vencimento
            alerts = alert_manager.get_due_bills_alerts(self.current_user['id'], days)

            for alert in alerts:
                status = alert['alert_type']
                tree.insert('', 'end', values=(
                    alert['due_date'],
                    alert['description'].split(' - ')[0],  # Apenas descrição
                    alert['category'],
                    f"R$ {alert['amount']:,.2f}",
                    status
                ))

        except Exception as e:
            print(f"Erro ao atualizar contas a vencer: {str(e)}")

    # Métodos para configurações
    def show_categories_manager(self):
        """Mostra gerenciador de categorias"""
        messagebox.showinfo("Em Desenvolvimento", "Gerenciador de categorias em desenvolvimento")

    def import_default_categories(self):
        """Importa categorias padrão"""
        try:
            from src.modules.category_manager import CategoryManager
            category_manager = CategoryManager(self.db_manager)

            count = category_manager.copy_default_categories_to_user(self.current_user['id'])
            messagebox.showinfo("Sucesso", f"{count} categorias importadas com sucesso!")

        except Exception as e:
            messagebox.showerror("Erro", str(e))

    def show_change_password(self):
        """Mostra diálogo para alterar senha"""
        messagebox.showinfo("Em Desenvolvimento", "Alteração de senha em desenvolvimento")

    def show_edit_profile(self):
        """Mostra diálogo para editar perfil"""
        messagebox.showinfo("Em Desenvolvimento", "Edição de perfil em desenvolvimento")

    def show_backup_manager(self):
        """Mostra gerenciador de backups"""
        messagebox.showinfo("Em Desenvolvimento", "Gerenciador de backups em desenvolvimento")

    # Métodos para administração
    def show_users_manager(self):
        """Mostra gerenciador de usuários"""
        messagebox.showinfo("Em Desenvolvimento", "Gerenciador de usuários em desenvolvimento")

    def show_create_user(self):
        """Mostra diálogo para criar usuário"""
        messagebox.showinfo("Em Desenvolvimento", "Criação de usuário em desenvolvimento")

    def show_system_logs(self):
        """Mostra logs do sistema"""
        messagebox.showinfo("Em Desenvolvimento", "Visualização de logs em desenvolvimento")

    def clear_system_logs(self):
        """Limpa logs do sistema"""
        if messagebox.askyesno("Confirmar", "Deseja realmente limpar todos os logs?"):
            messagebox.showinfo("Em Desenvolvimento", "Limpeza de logs em desenvolvimento")

    def show_system_stats(self):
        """Mostra estatísticas do sistema"""
        messagebox.showinfo("Em Desenvolvimento", "Estatísticas do sistema em desenvolvimento")

    def show_database_maintenance(self):
        """Mostra manutenção do banco"""
        messagebox.showinfo("Em Desenvolvimento", "Manutenção do banco em desenvolvimento")

    # Métodos para gerenciamento de veículos
    def show_vehicle_manager(self):
        """Mostra o gerenciador de veículos"""
        try:
            from .vehicle_manager import VehicleManager
            VehicleManager(self.root, self.db_manager, self.current_user['id'])
        except Exception as e:
            messagebox.showerror("Erro", f"Erro ao abrir gerenciador de veículos: {str(e)}")

    def new_vehicle(self):
        """Abre formulário para novo veículo"""
        try:
            from .vehicle_form import VehicleForm
            VehicleForm(self.root, self.db_manager, self.current_user['id'])
        except Exception as e:
            messagebox.showerror("Erro", f"Erro ao abrir formulário de veículo: {str(e)}")

    def new_maintenance(self):
        """Abre formulário para nova manutenção"""
        try:
            from .maintenance_form import MaintenanceForm
            MaintenanceForm(self.root, self.db_manager, self.current_user['id'])
        except Exception as e:
            messagebox.showerror("Erro", f"Erro ao abrir formulário de manutenção: {str(e)}")

    def new_fuel_record(self):
        """Abre formulário para novo abastecimento"""
        try:
            from .fuel_form import FuelForm
            FuelForm(self.root, self.db_manager, self.current_user['id'])
        except Exception as e:
            messagebox.showerror("Erro", f"Erro ao abrir formulário de combustível: {str(e)}")

    def show_vehicle_report(self):
        """Mostra relatório de veículos"""
        messagebox.showinfo("Em Desenvolvimento", "Relatório de veículos em desenvolvimento")

    def show_about(self):
        """Mostra informações sobre o sistema"""
        about_text = """
Sistema de Gestão de Contas
Versão 1.0

Desenvolvido para controle financeiro pessoal
com recursos avançados de gerenciamento.

Inclui gerenciamento de veículos, manutenção e combustível.

© 2024 - Todos os direitos reservados
        """
        messagebox.showinfo("Sobre", about_text)
