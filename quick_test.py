#!/usr/bin/env python3
# -*- coding: utf-8 -*-

print("Iniciando teste rápido...")

try:
    import sqlite3
    print("✓ SQLite disponível")
    
    # Testar criação de banco
    conn = sqlite3.connect("test_quick.db")
    cursor = conn.cursor()
    cursor.execute("CREATE TABLE IF NOT EXISTS test (id INTEGER PRIMARY KEY, name TEXT)")
    cursor.execute("INSERT INTO test (name) VALUES ('teste')")
    conn.commit()
    
    cursor.execute("SELECT * FROM test")
    result = cursor.fetchall()
    print(f"✓ Banco funcionando: {len(result)} registro(s)")
    
    conn.close()
    
    # Testar importação dos módulos
    import sys
    sys.path.insert(0, 'src')
    
    from database import DatabaseManager
    print("✓ DatabaseManager importado")
    
    from auth import AuthManager
    print("✓ AuthManager importado")
    
    print("\n✓ TESTE BÁSICO PASSOU!")
    print("O sistema parece estar funcionando corretamente.")
    
except Exception as e:
    print(f"✗ Erro: {str(e)}")
    import traceback
    traceback.print_exc()
