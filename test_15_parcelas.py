#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Teste específico para 15 parcelas
"""

import sys
import os
from datetime import datetime, timedelta

# Adicionar o diretório src ao path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from database import DatabaseManager
from auth import AuthManager
from modules.transaction_manager import TransactionManager

def test_15_parcelas():
    """Testa especificamente o problema com 15 parcelas"""
    print("🔍 TESTE ESPECÍFICO: 15 PARCELAS")
    print("=" * 50)
    
    try:
        # Inicializar banco de dados
        db_manager = DatabaseManager()
        db_manager.initialize_database()
        
        auth_manager = AuthManager(db_manager)
        transaction_manager = TransactionManager(db_manager)
        
        # Fazer login
        user_data = auth_manager.authenticate_user('admin', 'admin123')
        if not user_data:
            print("❌ Falha no login!")
            return
        
        print(f"✅ Login realizado: {user_data['full_name']}")
        user_id = user_data['id']
        
        # Obter carteira e categoria
        wallets = db_manager.execute_query("SELECT * FROM wallets WHERE user_id = ? LIMIT 1", (user_id,))
        if not wallets:
            print("❌ Nenhuma carteira encontrada!")
            return
        
        wallet_id = wallets[0]['id']
        print(f"✅ Usando carteira: {wallets[0]['name']} (ID: {wallet_id})")
        
        # Buscar ou criar categoria
        categories = db_manager.execute_query("SELECT * FROM categories WHERE user_id = ? AND category_type = 'expense' LIMIT 1", (user_id,))
        if not categories:
            db_manager.execute_query("""
                INSERT INTO categories (user_id, name, category_type, color, description)
                VALUES (?, 'Teste 15x', 'expense', '#e74c3c', 'Categoria para teste de 15 parcelas')
            """, (user_id,))
            categories = db_manager.execute_query("SELECT * FROM categories WHERE user_id = ? AND category_type = 'expense' LIMIT 1", (user_id,))
        
        category_id = categories[0]['id']
        print(f"✅ Usando categoria: {categories[0]['name']} (ID: {category_id})")
        
        # Contar transações antes
        count_before = db_manager.execute_query("SELECT COUNT(*) FROM transactions WHERE user_id = ?", (user_id,))[0][0]
        print(f"\n📊 Transações antes: {count_before}")
        
        # Teste com 15 parcelas - com debug detalhado
        print(f"\n🔧 CRIANDO TRANSAÇÃO COM 15 PARCELAS...")
        print(f"   Valor total: R$ 1.500,00")
        print(f"   Valor por parcela: R$ {1500.00/15:.2f}")
        
        # Modificar temporariamente o método para debug
        original_create_transaction = transaction_manager.create_transaction
        
        def debug_create_transaction(*args, **kwargs):
            try:
                result = original_create_transaction(*args, **kwargs)
                if result:
                    print(f"   ✅ Parcela criada: {args[4]} - {args[5]}")  # amount e description
                else:
                    print(f"   ❌ Falha na parcela: {args[4]} - {args[5]}")
                return result
            except Exception as e:
                print(f"   ❌ Erro na parcela: {args[5]} - {str(e)}")
                return False
        
        transaction_manager.create_transaction = debug_create_transaction
        
        # Criar transação parcelada
        success = transaction_manager.create_installment_transactions(
            user_id=user_id,
            wallet_id=wallet_id,
            category_id=category_id,
            transaction_type='expense',
            total_amount=1500.00,
            description='Teste 15 parcelas',
            transaction_date=datetime.now().strftime('%Y-%m-%d'),
            installments=15
        )
        
        # Restaurar método original
        transaction_manager.create_transaction = original_create_transaction
        
        print(f"\n📊 Resultado da criação: {'Sucesso' if success else 'Falha'}")
        
        # Contar transações depois
        count_after = db_manager.execute_query("SELECT COUNT(*) FROM transactions WHERE user_id = ?", (user_id,))[0][0]
        print(f"📊 Transações depois: {count_after}")
        print(f"📈 Transações criadas: {count_after - count_before}")
        
        # Verificar parcelas criadas especificamente
        parcelas_criadas = db_manager.execute_query("""
            SELECT installment_number, description, amount, transaction_date
            FROM transactions 
            WHERE user_id = ? AND description LIKE '%Teste 15 parcelas%'
            ORDER BY installment_number
        """, (user_id,))
        
        print(f"\n📋 PARCELAS ENCONTRADAS NO BANCO: {len(parcelas_criadas)}")
        
        if parcelas_criadas:
            print("   Parcelas criadas:")
            for parcela in parcelas_criadas:
                print(f"   {parcela['installment_number']:2d}/15: R$ {parcela['amount']:7.2f} - {parcela['transaction_date']}")
            
            # Verificar se há parcelas faltando
            numeros_criados = [p['installment_number'] for p in parcelas_criadas]
            numeros_esperados = list(range(1, 16))
            faltando = [n for n in numeros_esperados if n not in numeros_criados]
            
            if faltando:
                print(f"\n⚠️  PARCELAS FALTANDO: {faltando}")
            else:
                print(f"\n✅ TODAS AS 15 PARCELAS FORAM CRIADAS!")
        
        # Verificar se há algum erro específico
        if count_after - count_before != 15:
            print(f"\n🔍 INVESTIGAÇÃO DO PROBLEMA:")
            print(f"   Esperado: 15 parcelas")
            print(f"   Criado: {count_after - count_before} parcelas")
            print(f"   Diferença: {15 - (count_after - count_before)} parcelas perdidas")
            
            # Verificar logs de erro
            print(f"\n📋 Verificando possíveis causas:")
            print(f"   1. Erro na criação de transações individuais")
            print(f"   2. Problema com validações")
            print(f"   3. Limite de transações no banco")
            print(f"   4. Erro de data/formato")
        
    except Exception as e:
        print(f"❌ ERRO GERAL: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_15_parcelas()
