#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Módulo de Gerenciamento de Atualizações Automáticas
Responsável por executar tarefas automáticas de atualização de dados
"""

import threading
import time
import logging
from datetime import datetime, date, timedelta
from typing import Dict, List, Callable, Optional
import json
from pathlib import Path

class AutoUpdateManager:
    """Gerenciador de atualizações automáticas de dados"""
    
    def __init__(self, db_manager, config_path="config/auto_update_config.json"):
        self.db_manager = db_manager
        self.config_path = Path(config_path)
        self.config = self.load_config()
        
        # Threading
        self.update_thread = None
        self.stop_event = threading.Event()
        self.is_running = False
        
        # Tarefas registradas
        self.tasks = {}
        self.last_run_times = {}
        
        # Logger
        self.setup_logging()
        
        # Registrar tarefas padrão
        self.register_default_tasks()
    
    def setup_logging(self):
        """Configura logging para o sistema de atualizações"""
        log_dir = Path("logs")
        log_dir.mkdir(exist_ok=True)
        
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler(log_dir / "auto_updates.log"),
                logging.StreamHandler()
            ]
        )
        self.logger = logging.getLogger("AutoUpdateManager")
    
    def load_config(self) -> Dict:
        """Carrega configurações do arquivo JSON"""
        default_config = {
            "enabled": True,
            "update_interval_seconds": 300,  # 5 minutos
            "tasks": {
                "fuel_efficiency_update": {
                    "enabled": True,
                    "interval_minutes": 60,
                    "description": "Atualiza cálculos de eficiência de combustível"
                },
                "maintenance_reminders": {
                    "enabled": True,
                    "interval_minutes": 1440,  # 24 horas
                    "description": "Verifica lembretes de manutenção"
                },
                "insurance_expiry_check": {
                    "enabled": True,
                    "interval_minutes": 1440,  # 24 horas
                    "description": "Verifica vencimento de seguros"
                },
                "financial_calculations": {
                    "enabled": True,
                    "interval_minutes": 30,
                    "description": "Atualiza cálculos financeiros"
                },
                "data_cleanup": {
                    "enabled": True,
                    "interval_minutes": 10080,  # 7 dias
                    "description": "Limpeza de dados antigos"
                }
            }
        }
        
        try:
            if self.config_path.exists():
                with open(self.config_path, 'r', encoding='utf-8') as f:
                    config = json.load(f)
                # Mesclar com configurações padrão
                for key, value in default_config.items():
                    if key not in config:
                        config[key] = value
                return config
            else:
                self.save_config(default_config)
                return default_config
        except Exception as e:
            self.logger.error(f"Erro ao carregar configuração: {e}")
            return default_config
    
    def save_config(self, config: Dict = None):
        """Salva configurações no arquivo JSON"""
        if config is None:
            config = self.config
        
        try:
            self.config_path.parent.mkdir(exist_ok=True)
            with open(self.config_path, 'w', encoding='utf-8') as f:
                json.dump(config, f, indent=2, ensure_ascii=False)
        except Exception as e:
            self.logger.error(f"Erro ao salvar configuração: {e}")
    
    def register_task(self, task_name: str, task_function: Callable, 
                     interval_minutes: int = 60, enabled: bool = True):
        """Registra uma nova tarefa de atualização automática"""
        self.tasks[task_name] = {
            'function': task_function,
            'interval_minutes': interval_minutes,
            'enabled': enabled,
            'last_run': None
        }
        
        # Atualizar configuração
        if task_name not in self.config['tasks']:
            self.config['tasks'][task_name] = {
                'enabled': enabled,
                'interval_minutes': interval_minutes,
                'description': f"Tarefa personalizada: {task_name}"
            }
            self.save_config()
        
        self.logger.info(f"Tarefa registrada: {task_name}")
    
    def register_default_tasks(self):
        """Registra tarefas padrão do sistema"""
        # Atualização de eficiência de combustível
        self.register_task(
            "fuel_efficiency_update",
            self.update_fuel_efficiency,
            self.config['tasks']['fuel_efficiency_update']['interval_minutes']
        )
        
        # Lembretes de manutenção
        self.register_task(
            "maintenance_reminders",
            self.check_maintenance_reminders,
            self.config['tasks']['maintenance_reminders']['interval_minutes']
        )
        
        # Verificação de vencimento de seguros
        self.register_task(
            "insurance_expiry_check",
            self.check_insurance_expiry,
            self.config['tasks']['insurance_expiry_check']['interval_minutes']
        )
        
        # Cálculos financeiros
        self.register_task(
            "financial_calculations",
            self.update_financial_calculations,
            self.config['tasks']['financial_calculations']['interval_minutes']
        )
        
        # Limpeza de dados
        self.register_task(
            "data_cleanup",
            self.cleanup_old_data,
            self.config['tasks']['data_cleanup']['interval_minutes']
        )
    
    def start(self):
        """Inicia o sistema de atualizações automáticas"""
        if not self.config['enabled']:
            self.logger.info("Sistema de atualizações automáticas desabilitado")
            return
        
        if self.is_running:
            self.logger.warning("Sistema de atualizações já está rodando")
            return
        
        self.is_running = True
        self.stop_event.clear()
        
        self.update_thread = threading.Thread(target=self._update_loop, daemon=True)
        self.update_thread.start()
        
        self.logger.info("Sistema de atualizações automáticas iniciado")
    
    def stop(self):
        """Para o sistema de atualizações automáticas"""
        if not self.is_running:
            return
        
        self.is_running = False
        self.stop_event.set()
        
        if self.update_thread and self.update_thread.is_alive():
            self.update_thread.join(timeout=5)
        
        self.logger.info("Sistema de atualizações automáticas parado")
    
    def _update_loop(self):
        """Loop principal de atualizações"""
        while not self.stop_event.is_set():
            try:
                self.run_scheduled_tasks()
                
                # Aguardar próximo ciclo
                self.stop_event.wait(self.config['update_interval_seconds'])
                
            except Exception as e:
                self.logger.error(f"Erro no loop de atualizações: {e}")
                time.sleep(60)  # Aguardar 1 minuto antes de tentar novamente
    
    def run_scheduled_tasks(self):
        """Executa tarefas agendadas que estão prontas para rodar"""
        current_time = datetime.now()
        
        for task_name, task_info in self.tasks.items():
            if not task_info['enabled']:
                continue
            
            if not self.config['tasks'].get(task_name, {}).get('enabled', True):
                continue
            
            # Verificar se é hora de executar a tarefa
            last_run = task_info['last_run']
            interval_minutes = task_info['interval_minutes']
            
            if last_run is None or (current_time - last_run).total_seconds() >= (interval_minutes * 60):
                try:
                    self.logger.info(f"Executando tarefa: {task_name}")
                    task_info['function']()
                    task_info['last_run'] = current_time
                    self.logger.info(f"Tarefa concluída: {task_name}")
                    
                except Exception as e:
                    self.logger.error(f"Erro ao executar tarefa {task_name}: {e}")
    
    def force_run_task(self, task_name: str):
        """Força a execução de uma tarefa específica"""
        if task_name not in self.tasks:
            raise ValueError(f"Tarefa não encontrada: {task_name}")
        
        try:
            self.logger.info(f"Execução forçada da tarefa: {task_name}")
            self.tasks[task_name]['function']()
            self.tasks[task_name]['last_run'] = datetime.now()
            self.logger.info(f"Execução forçada concluída: {task_name}")
            return True
        except Exception as e:
            self.logger.error(f"Erro na execução forçada de {task_name}: {e}")
            return False
    
    def get_task_status(self) -> Dict:
        """Retorna status de todas as tarefas"""
        status = {
            'system_running': self.is_running,
            'system_enabled': self.config['enabled'],
            'tasks': {}
        }
        
        for task_name, task_info in self.tasks.items():
            status['tasks'][task_name] = {
                'enabled': task_info['enabled'] and self.config['tasks'].get(task_name, {}).get('enabled', True),
                'interval_minutes': task_info['interval_minutes'],
                'last_run': task_info['last_run'].isoformat() if task_info['last_run'] else None,
                'description': self.config['tasks'].get(task_name, {}).get('description', '')
            }
        
        return status

    # ===== IMPLEMENTAÇÃO DAS TAREFAS AUTOMÁTICAS =====

    def update_fuel_efficiency(self):
        """Atualiza cálculos de eficiência de combustível para todos os veículos"""
        try:
            # Buscar todos os registros de combustível sem eficiência calculada
            query = '''
                SELECT id, vehicle_id, mileage, liters, fuel_date
                FROM fuel_records
                WHERE fuel_efficiency IS NULL AND mileage IS NOT NULL AND liters > 0
                ORDER BY vehicle_id, fuel_date
            '''
            records = self.db_manager.execute_query(query)

            updated_count = 0
            for record in records:
                efficiency = self.db_manager.calculate_fuel_efficiency(
                    record['vehicle_id'],
                    record['mileage'],
                    record['liters']
                )

                if efficiency:
                    update_query = '''
                        UPDATE fuel_records
                        SET fuel_efficiency = ?, updated_at = CURRENT_TIMESTAMP
                        WHERE id = ?
                    '''
                    self.db_manager.execute_query(update_query, (efficiency, record['id']))
                    updated_count += 1

            self.logger.info(f"Eficiência de combustível atualizada para {updated_count} registros")

        except Exception as e:
            self.logger.error(f"Erro ao atualizar eficiência de combustível: {e}")

    def check_maintenance_reminders(self):
        """Verifica e cria lembretes de manutenção baseados em data e quilometragem"""
        try:
            today = date.today()

            # Verificar manutenções vencidas por data
            date_query = '''
                SELECT vm.*, v.name as vehicle_name, v.mileage as current_mileage
                FROM vehicle_maintenance vm
                JOIN vehicles v ON vm.vehicle_id = v.id
                WHERE vm.next_service_date IS NOT NULL
                AND vm.next_service_date <= ?
                AND vm.is_completed = 1
                AND vm.is_scheduled = 0
            '''
            date_reminders = self.db_manager.execute_query(date_query, (today.isoformat(),))

            # Verificar manutenções vencidas por quilometragem
            mileage_query = '''
                SELECT vm.*, v.name as vehicle_name, v.mileage as current_mileage
                FROM vehicle_maintenance vm
                JOIN vehicles v ON vm.vehicle_id = v.id
                WHERE vm.next_service_mileage IS NOT NULL
                AND v.mileage >= vm.next_service_mileage
                AND vm.is_completed = 1
                AND vm.is_scheduled = 0
            '''
            mileage_reminders = self.db_manager.execute_query(mileage_query)

            # Criar lembretes automáticos
            reminder_count = 0

            for reminder in date_reminders:
                self._create_maintenance_reminder(reminder, 'data')
                reminder_count += 1

            for reminder in mileage_reminders:
                self._create_maintenance_reminder(reminder, 'quilometragem')
                reminder_count += 1

            self.logger.info(f"Criados {reminder_count} lembretes de manutenção")

        except Exception as e:
            self.logger.error(f"Erro ao verificar lembretes de manutenção: {e}")

    def _create_maintenance_reminder(self, maintenance_record, reminder_type):
        """Cria um lembrete de manutenção específico"""
        try:
            # Criar novo registro de manutenção agendada
            reminder_data = {
                'vehicle_id': maintenance_record['vehicle_id'],
                'maintenance_type': maintenance_record['maintenance_type'],
                'description': f"Lembrete automático: {maintenance_record['description']} ({reminder_type})",
                'service_date': date.today().isoformat(),
                'cost': 0.0,
                'is_scheduled': True,
                'is_completed': False,
                'notes': f"Lembrete criado automaticamente baseado em {reminder_type}"
            }

            # Adicionar ao banco
            self.db_manager.add_maintenance_record(
                maintenance_record['user_id'],
                reminder_data
            )

        except Exception as e:
            self.logger.error(f"Erro ao criar lembrete de manutenção: {e}")

    def check_insurance_expiry(self):
        """Verifica vencimento de seguros e documentos de veículos"""
        try:
            today = date.today()
            warning_date = today + timedelta(days=30)  # Avisar 30 dias antes

            # Verificar seguros vencendo
            query = '''
                SELECT id, user_id, name, brand, model, insurance_company,
                       insurance_expiry, license_plate
                FROM vehicles
                WHERE insurance_expiry IS NOT NULL
                AND insurance_expiry <= ?
                AND is_active = 1
            '''
            expiring_insurance = self.db_manager.execute_query(query, (warning_date.isoformat(),))

            alert_count = 0
            for vehicle in expiring_insurance:
                self._create_insurance_alert(vehicle, today)
                alert_count += 1

            self.logger.info(f"Criados {alert_count} alertas de vencimento de seguro")

        except Exception as e:
            self.logger.error(f"Erro ao verificar vencimento de seguros: {e}")

    def _create_insurance_alert(self, vehicle, today):
        """Cria alerta de vencimento de seguro"""
        try:
            expiry_date = datetime.strptime(vehicle['insurance_expiry'], '%Y-%m-%d').date()
            days_until_expiry = (expiry_date - today).days

            if days_until_expiry <= 0:
                alert_type = "VENCIDO"
            elif days_until_expiry <= 7:
                alert_type = "VENCE EM BREVE"
            else:
                alert_type = "VENCIMENTO PRÓXIMO"

            # Aqui você pode implementar um sistema de alertas/notificações
            # Por enquanto, vamos apenas logar
            self.logger.warning(
                f"Seguro {alert_type}: {vehicle['name']} ({vehicle['license_plate']}) - "
                f"Vence em {days_until_expiry} dias"
            )

        except Exception as e:
            self.logger.error(f"Erro ao criar alerta de seguro: {e}")

    def update_financial_calculations(self):
        """Atualiza cálculos financeiros automáticos"""
        try:
            # Processar transações recorrentes
            self._update_recurring_transactions()

            # Atualizar saldos de carteiras
            self._update_wallet_balances()

            # Recalcular totais de categorias
            self._update_category_totals()

            # Atualizar estatísticas mensais
            self._update_monthly_statistics()

            # Verificar contas vencidas
            self._check_overdue_bills()

            self.logger.info("Cálculos financeiros atualizados")

        except Exception as e:
            self.logger.error(f"Erro ao atualizar cálculos financeiros: {e}")

    def _update_wallet_balances(self):
        """Atualiza saldos das carteiras baseado nas transações"""
        try:
            # Buscar todas as carteiras ativas
            wallets = self.db_manager.execute_query(
                "SELECT id, user_id FROM wallets WHERE is_active = 1"
            )

            for wallet in wallets:
                # Calcular saldo baseado nas transações
                balance_query = '''
                    SELECT
                        COALESCE(SUM(CASE WHEN transaction_type = 'income' THEN amount ELSE 0 END), 0) as total_income,
                        COALESCE(SUM(CASE WHEN transaction_type = 'expense' THEN amount ELSE 0 END), 0) as total_expense
                    FROM transactions
                    WHERE wallet_id = ? AND is_paid = 1
                '''
                result = self.db_manager.execute_query(balance_query, (wallet['id'],))

                if result:
                    total_income = result[0]['total_income']
                    total_expense = result[0]['total_expense']
                    current_balance = total_income - total_expense

                    # Atualizar saldo na carteira
                    update_query = '''
                        UPDATE wallets
                        SET balance = ?, updated_at = CURRENT_TIMESTAMP
                        WHERE id = ?
                    '''
                    self.db_manager.execute_query(update_query, (current_balance, wallet['id']))

        except Exception as e:
            self.logger.error(f"Erro ao atualizar saldos das carteiras: {e}")

    def _update_category_totals(self):
        """Atualiza totais gastos por categoria no mês atual"""
        try:
            current_month = date.today().replace(day=1).isoformat()

            # Buscar categorias ativas
            categories = self.db_manager.execute_query(
                "SELECT id, user_id FROM categories WHERE is_active = 1"
            )

            for category in categories:
                # Calcular total gasto na categoria no mês atual
                total_query = '''
                    SELECT COALESCE(SUM(amount), 0) as monthly_total
                    FROM transactions
                    WHERE category_id = ?
                    AND transaction_type = 'expense'
                    AND transaction_date >= ?
                    AND is_paid = 1
                '''
                result = self.db_manager.execute_query(total_query, (category['id'], current_month))

                if result:
                    monthly_total = result[0]['monthly_total']

                    # Atualizar total mensal na categoria (se a coluna existir)
                    # Nota: Isso requer adicionar uma coluna monthly_total na tabela categories
                    # Por enquanto, apenas logamos a informação
                    self.logger.debug(f"Categoria {category['id']}: Total mensal = R$ {monthly_total:.2f}")

        except Exception as e:
            self.logger.error(f"Erro ao atualizar totais de categorias: {e}")

    def _update_monthly_statistics(self):
        """Atualiza estatísticas mensais do sistema"""
        try:
            current_month = date.today().replace(day=1).isoformat()

            # Buscar usuários ativos
            users = self.db_manager.execute_query(
                "SELECT id FROM users WHERE is_active = 1"
            )

            for user in users:
                # Calcular estatísticas do mês para o usuário
                stats_query = '''
                    SELECT
                        COUNT(*) as total_transactions,
                        COALESCE(SUM(CASE WHEN transaction_type = 'income' THEN amount ELSE 0 END), 0) as total_income,
                        COALESCE(SUM(CASE WHEN transaction_type = 'expense' THEN amount ELSE 0 END), 0) as total_expense
                    FROM transactions
                    WHERE user_id = ?
                    AND transaction_date >= ?
                    AND is_paid = 1
                '''
                result = self.db_manager.execute_query(stats_query, (user['id'], current_month))

                if result:
                    stats = result[0]
                    balance = stats['total_income'] - stats['total_expense']

                    self.logger.debug(
                        f"Usuário {user['id']} - Mês atual: "
                        f"Receitas: R$ {stats['total_income']:.2f}, "
                        f"Despesas: R$ {stats['total_expense']:.2f}, "
                        f"Saldo: R$ {balance:.2f}"
                    )

                    # Verificar alertas de orçamento
                    self._check_budget_alerts(user['id'], stats)

        except Exception as e:
            self.logger.error(f"Erro ao atualizar estatísticas mensais: {e}")

    def _check_budget_alerts(self, user_id, monthly_stats):
        """Verifica alertas de orçamento baseados nos gastos mensais"""
        try:
            # Verificar se gastos mensais excedem média dos últimos 3 meses
            avg_query = '''
                SELECT AVG(monthly_expense) as avg_expense
                FROM (
                    SELECT
                        strftime('%Y-%m', transaction_date) as month,
                        SUM(amount) as monthly_expense
                    FROM transactions
                    WHERE user_id = ?
                    AND transaction_type = 'expense'
                    AND is_paid = 1
                    AND transaction_date >= date('now', '-3 months')
                    GROUP BY strftime('%Y-%m', transaction_date)
                )
            '''

            result = self.db_manager.execute_query(avg_query, (user_id,))
            if result and result[0]['avg_expense']:
                avg_expense = result[0]['avg_expense']
                current_expense = monthly_stats['total_expense']

                # Alerta se gastos atuais são 20% maiores que a média
                if current_expense > avg_expense * 1.2:
                    self.logger.warning(
                        f"Alerta de orçamento - Usuário {user_id}: "
                        f"Gastos atuais (R$ {current_expense:.2f}) excedem "
                        f"média dos últimos 3 meses (R$ {avg_expense:.2f}) em "
                        f"{((current_expense / avg_expense - 1) * 100):.1f}%"
                    )

        except Exception as e:
            self.logger.error(f"Erro ao verificar alertas de orçamento: {e}")

    def _update_recurring_transactions(self):
        """Processa transações recorrentes que devem ser criadas"""
        try:
            today = date.today()

            # Buscar transações recorrentes ativas
            recurring_query = '''
                SELECT * FROM transactions
                WHERE is_recurring = 1
                AND is_paid = 1
                AND recurring_type IS NOT NULL
            '''

            recurring_transactions = self.db_manager.execute_query(recurring_query)
            created_count = 0

            for transaction in recurring_transactions:
                # Calcular próxima data baseada no tipo de recorrência
                next_date = self._calculate_next_recurring_date(
                    transaction['transaction_date'],
                    transaction['recurring_type'],
                    transaction.get('recurring_day')
                )

                if next_date and next_date <= today:
                    # Verificar se já existe transação para esta data
                    existing_query = '''
                        SELECT COUNT(*) FROM transactions
                        WHERE user_id = ?
                        AND description = ?
                        AND amount = ?
                        AND transaction_date = ?
                        AND transaction_type = ?
                    '''

                    existing = self.db_manager.execute_query(existing_query, (
                        transaction['user_id'],
                        transaction['description'],
                        transaction['amount'],
                        next_date.isoformat(),
                        transaction['transaction_type']
                    ))

                    if existing[0][0] == 0:
                        # Criar nova transação recorrente
                        new_transaction = {
                            'wallet_id': transaction['wallet_id'],
                            'category_id': transaction['category_id'],
                            'transaction_type': transaction['transaction_type'],
                            'amount': transaction['amount'],
                            'description': f"{transaction['description']} (Recorrente)",
                            'transaction_date': next_date.isoformat(),
                            'due_date': next_date.isoformat(),
                            'is_paid': False,
                            'is_recurring': False,  # A nova não é recorrente
                            'notes': f"Criada automaticamente de transação recorrente"
                        }

                        self.db_manager.add_transaction(transaction['user_id'], new_transaction)
                        created_count += 1

            if created_count > 0:
                self.logger.info(f"Criadas {created_count} transações recorrentes")

        except Exception as e:
            self.logger.error(f"Erro ao processar transações recorrentes: {e}")

    def _calculate_next_recurring_date(self, last_date, recurring_type, recurring_day=None):
        """Calcula próxima data para transação recorrente"""
        try:
            from datetime import datetime, timedelta
            import calendar

            if isinstance(last_date, str):
                last_date = datetime.strptime(last_date, '%Y-%m-%d').date()

            today = date.today()

            if recurring_type == 'monthly':
                # Próximo mês, mesmo dia
                if last_date.month == 12:
                    next_month = last_date.replace(year=last_date.year + 1, month=1)
                else:
                    next_month = last_date.replace(month=last_date.month + 1)

                # Ajustar para último dia do mês se necessário
                if recurring_day:
                    try:
                        next_month = next_month.replace(day=min(recurring_day,
                                                               calendar.monthrange(next_month.year, next_month.month)[1]))
                    except:
                        pass

                return next_month

            elif recurring_type == 'weekly':
                return last_date + timedelta(weeks=1)

            elif recurring_type == 'yearly':
                try:
                    return last_date.replace(year=last_date.year + 1)
                except ValueError:
                    # Caso de 29 de fevereiro em ano não bissexto
                    return last_date.replace(year=last_date.year + 1, day=28)

            return None

        except Exception as e:
            self.logger.error(f"Erro ao calcular próxima data recorrente: {e}")
            return None

    def _check_overdue_bills(self):
        """Verifica e alerta sobre contas vencidas"""
        try:
            today = date.today()

            # Buscar contas vencidas
            overdue_query = '''
                SELECT
                    t.id,
                    t.user_id,
                    t.description,
                    t.amount,
                    t.due_date,
                    t.transaction_type,
                    c.name as category_name,
                    w.name as wallet_name,
                    (julianday('now') - julianday(t.due_date)) as days_overdue
                FROM transactions t
                JOIN categories c ON t.category_id = c.id
                JOIN wallets w ON t.wallet_id = w.id
                WHERE t.is_paid = 0
                AND t.due_date < ?
                ORDER BY t.due_date ASC
            '''

            overdue_bills = self.db_manager.execute_query(overdue_query, (today.isoformat(),))

            if overdue_bills:
                total_overdue = sum(bill['amount'] for bill in overdue_bills)

                self.logger.warning(
                    f"Encontradas {len(overdue_bills)} contas vencidas "
                    f"totalizando R$ {total_overdue:.2f}"
                )

                # Agrupar por usuário para alertas específicos
                user_overdue = {}
                for bill in overdue_bills:
                    user_id = bill['user_id']
                    if user_id not in user_overdue:
                        user_overdue[user_id] = []
                    user_overdue[user_id].append(bill)

                # Log detalhado por usuário
                for user_id, bills in user_overdue.items():
                    user_total = sum(bill['amount'] for bill in bills)
                    self.logger.warning(
                        f"Usuário {user_id}: {len(bills)} contas vencidas "
                        f"(R$ {user_total:.2f})"
                    )

                    # Log das 3 contas mais antigas
                    for bill in bills[:3]:
                        days_overdue = int(bill['days_overdue'])
                        self.logger.warning(
                            f"  - {bill['description']}: R$ {bill['amount']:.2f} "
                            f"(vencida há {days_overdue} dias)"
                        )

        except Exception as e:
            self.logger.error(f"Erro ao verificar contas vencidas: {e}")

    def cleanup_old_data(self):
        """Limpa dados antigos e desnecessários"""
        try:
            cleanup_count = 0

            # Limpar logs antigos (mais de 90 dias)
            cutoff_date = (date.today() - timedelta(days=90)).isoformat()

            # Limpar transações muito antigas e não pagas (mais de 1 ano)
            old_transactions_query = '''
                DELETE FROM transactions
                WHERE is_paid = 0
                AND transaction_date < ?
                AND created_at < datetime('now', '-1 year')
            '''
            old_cutoff = (date.today() - timedelta(days=365)).isoformat()
            result = self.db_manager.execute_query(old_transactions_query, (old_cutoff,))

            # Limpar registros de combustível duplicados (mesmo veículo, data e valor)
            duplicate_fuel_query = '''
                DELETE FROM fuel_records
                WHERE id NOT IN (
                    SELECT MIN(id)
                    FROM fuel_records
                    GROUP BY vehicle_id, fuel_date, total_cost, liters
                )
            '''
            self.db_manager.execute_query(duplicate_fuel_query)

            # Vacuum do banco para otimizar espaço
            self.db_manager.execute_query("VACUUM")

            self.logger.info(f"Limpeza de dados concluída")

        except Exception as e:
            self.logger.error(f"Erro na limpeza de dados: {e}")
