#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Teste dos botões de reset na aba de configurações
"""

import sys
import os
import tkinter as tk

# Adicionar o diretório src ao path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from database import DatabaseManager
from auth import AuthManager
from gui.main_window import MainWindow

def test_botao_reset():
    """Testa os botões de reset na aba de configurações"""
    print("🔄 TESTE DOS BOTÕES DE RESET")
    print("=" * 50)
    
    try:
        # Inicializar banco de dados
        db_manager = DatabaseManager()
        db_manager.initialize_database()
        
        auth_manager = AuthManager(db_manager)
        
        # Fazer login
        user_data = auth_manager.authenticate_user('admin', 'admin123')
        if not user_data:
            print("❌ Falha no login!")
            return
        
        print(f"✅ Login realizado: {user_data['full_name']}")
        
        # Criar janela principal
        root = tk.Tk()
        root.title("Teste Botões Reset")
        root.geometry("1200x800")
        
        # Função de logout mock
        def mock_logout():
            print("Logout chamado")
        
        # Criar MainWindow
        main_window = MainWindow(root, db_manager, auth_manager, user_data, mock_logout)
        
        print("✅ Janela principal criada com sucesso!")
        
        # Navegar para a aba de configurações
        main_window.notebook.select(4)  # Aba de configurações
        
        def test_reset_buttons():
            try:
                root.update_idletasks()
                
                print("\n🔍 VERIFICANDO BOTÕES DE RESET...")
                
                # Procurar botões de reset
                reset_buttons = []
                def find_reset_buttons(widget, level=0):
                    indent = "  " * level
                    if hasattr(widget, 'winfo_class') and widget.winfo_class() == 'TButton':
                        try:
                            text = widget['text']
                            if 'reset' in text.lower() or 'limpar' in text.lower() or '🧹' in text or '🔄' in text:
                                reset_buttons.append({
                                    'text': text,
                                    'widget': widget,
                                    'width': widget.winfo_width(),
                                    'height': widget.winfo_height()
                                })
                                print(f"{indent}🔍 Botão de reset: '{text}'")
                        except Exception as e:
                            pass
                    
                    try:
                        for child in widget.winfo_children():
                            find_reset_buttons(child, level + 1)
                    except:
                        pass
                
                find_reset_buttons(root)
                
                print(f"\n📊 Botões de reset encontrados: {len(reset_buttons)}")
                
                for i, btn in enumerate(reset_buttons):
                    print(f"   {i+1}. '{btn['text']}' - {btn['width']}x{btn['height']}")
                
                # Testar funcionalidade dos botões
                print(f"\n🧪 TESTANDO FUNCIONALIDADES...")
                
                # Testar botão de limpeza de dados de teste
                cleanup_button = None
                full_reset_button = None
                
                for btn in reset_buttons:
                    if '🧹' in btn['text'] or 'Limpar' in btn['text']:
                        cleanup_button = btn
                    elif '🔄' in btn['text'] or 'Reset Completo' in btn['text']:
                        full_reset_button = btn
                
                if cleanup_button:
                    print(f"   ✅ Botão 'Limpar Dados de Teste' encontrado")
                    try:
                        # Testar se o método existe
                        if hasattr(main_window, 'show_reset_options'):
                            print(f"   ✅ Método 'show_reset_options' existe")
                        else:
                            print(f"   ❌ Método 'show_reset_options' não encontrado")
                    except Exception as e:
                        print(f"   ❌ Erro ao verificar método: {str(e)}")
                else:
                    print(f"   ❌ Botão 'Limpar Dados de Teste' não encontrado")
                
                if full_reset_button:
                    print(f"   ✅ Botão 'Reset Completo' encontrado")
                    try:
                        # Testar se o método existe
                        if hasattr(main_window, 'show_full_reset'):
                            print(f"   ✅ Método 'show_full_reset' existe")
                        else:
                            print(f"   ❌ Método 'show_full_reset' não encontrado")
                    except Exception as e:
                        print(f"   ❌ Erro ao verificar método: {str(e)}")
                else:
                    print(f"   ❌ Botão 'Reset Completo' não encontrado")
                
                # Verificar seção Sistema
                print(f"\n🔍 VERIFICANDO SEÇÃO SISTEMA...")
                
                # Procurar LabelFrame com texto "Sistema"
                system_section_found = False
                def find_system_section(widget):
                    nonlocal system_section_found
                    if hasattr(widget, 'winfo_class') and widget.winfo_class() == 'TLabelframe':
                        try:
                            # Verificar se é a seção Sistema
                            for child in widget.winfo_children():
                                if hasattr(child, 'winfo_class') and child.winfo_class() == 'TLabel':
                                    if hasattr(child, 'cget') and 'Sistema' in str(child.cget('text')):
                                        system_section_found = True
                                        print(f"   ✅ Seção 'Sistema' encontrada")
                                        break
                        except:
                            pass
                    
                    try:
                        for child in widget.winfo_children():
                            find_system_section(child)
                    except:
                        pass
                
                find_system_section(root)
                
                if not system_section_found:
                    print(f"   ❌ Seção 'Sistema' não encontrada")
                
                # Resumo do teste
                print(f"\n📊 RESUMO DO TESTE:")
                print(f"   Botões de reset encontrados: {len(reset_buttons)}")
                print(f"   Botão 'Limpar Dados': {'✅' if cleanup_button else '❌'}")
                print(f"   Botão 'Reset Completo': {'✅' if full_reset_button else '❌'}")
                print(f"   Seção 'Sistema': {'✅' if system_section_found else '❌'}")
                
                if len(reset_buttons) >= 2 and cleanup_button and full_reset_button and system_section_found:
                    print(f"\n🎉 TODOS OS BOTÕES DE RESET IMPLEMENTADOS COM SUCESSO!")
                else:
                    print(f"\n⚠️  Alguns botões ou seções podem estar faltando")
                
            except Exception as e:
                print(f"❌ Erro ao verificar botões: {str(e)}")
                import traceback
                traceback.print_exc()
        
        # Aguardar renderização e verificar
        root.after(1000, test_reset_buttons)
        
        print("\n✅ TESTE DOS BOTÕES DE RESET INICIADO!")
        print("🔍 Verificando botões em 1 segundo...")
        
        # Executar por tempo limitado
        root.after(5000, root.quit)
        root.mainloop()
        root.destroy()
        
    except Exception as e:
        print(f"❌ ERRO GERAL: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_botao_reset()
