#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Teste do layout do formulário de transações
"""

import sys
import os
import tkinter as tk

# Adicionar o diretório src ao path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from database import DatabaseManager
from auth import AuthManager
from gui.transaction_form_new import TransactionFormNew

def test_form_layout():
    """Testa o layout do formulário"""
    print("🎨 TESTE DO LAYOUT DO FORMULÁRIO")
    print("=" * 50)
    
    try:
        # Inicializar banco de dados
        db_manager = DatabaseManager()
        db_manager.initialize_database()
        
        auth_manager = AuthManager(db_manager)
        
        # Fazer login
        user_data = auth_manager.authenticate_user('admin', 'admin123')
        if not user_data:
            print("❌ Falha no login!")
            return
        
        print(f"✅ Login realizado: {user_data['full_name']}")
        
        # Criar janela principal
        root = tk.Tk()
        root.title("Teste Layout Formulário")
        root.geometry("1000x700")
        
        print("✅ Janela principal criada")
        
        # Testar formulário de despesa
        print("\n🔧 Testando formulário de nova despesa...")
        try:
            form = TransactionFormNew(
                root, 
                db_manager, 
                user_data['id'], 
                'expense'  # Nova despesa
            )
            
            print("✅ Formulário de despesa criado!")
            print(f"📐 Tamanho da janela: {form.window.geometry()}")
            
            # Verificar se os botões existem
            def check_buttons():
                try:
                    # Procurar por botões na janela
                    buttons_found = []
                    
                    def find_buttons(widget):
                        if isinstance(widget, tk.Button):
                            buttons_found.append(widget['text'])
                        for child in widget.winfo_children():
                            find_buttons(child)
                    
                    find_buttons(form.window)
                    
                    print(f"🔍 Botões encontrados: {buttons_found}")
                    
                    if len(buttons_found) >= 2:
                        print("✅ Botões estão presentes no formulário!")
                    else:
                        print("❌ Botões não encontrados ou incompletos")
                    
                    # Verificar posição dos botões
                    form.window.update_idletasks()
                    window_height = form.window.winfo_height()
                    print(f"📏 Altura da janela: {window_height}px")
                    
                except Exception as e:
                    print(f"❌ Erro ao verificar botões: {str(e)}")
            
            # Aguardar um pouco para a janela ser renderizada
            root.after(1000, check_buttons)
            
            # Fechar formulário após teste
            root.after(3000, form.window.destroy)
            
        except Exception as e:
            print(f"❌ Erro ao criar formulário: {str(e)}")
            import traceback
            traceback.print_exc()
        
        print("\n✅ TESTE DE LAYOUT CONCLUÍDO!")
        
        # Executar loop principal por um tempo limitado
        root.after(4000, root.quit)
        root.mainloop()
        root.destroy()
        
    except Exception as e:
        print(f"❌ ERRO GERAL: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_form_layout()
