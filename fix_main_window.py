#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Script para corrigir os métodos da janela principal
"""

import os
import shutil

def fix_main_window():
    """Corrige os métodos problemáticos na janela principal"""
    print("🔧 CORRIGINDO JANELA PRINCIPAL")
    print("=" * 50)
    
    # Fazer backup
    backup_file = "src/gui/main_window.py.backup"
    if not os.path.exists(backup_file):
        shutil.copy2("src/gui/main_window.py", backup_file)
        print("✅ Backup criado")
    
    # Ler arquivo atual
    with open("src/gui/main_window.py", "r", encoding="utf-8") as f:
        content = f.read()
    
    # Substituições necessárias
    replacements = [
        (
            "from src.gui.transaction_dialog import TransactionDialog",
            "from src.gui.transaction_form_new import TransactionFormNew"
        ),
        (
            "dialog = TransactionDialog(self.root, self.db_manager, self.current_user['id'], 'income')",
            "form = TransactionFormNew(self.root, self.db_manager, self.current_user['id'], 'income')\n            self.root.wait_window(form.window)"
        ),
        (
            "dialog = TransactionDialog(self.root, self.db_manager, self.current_user['id'], 'expense')",
            "form = TransactionFormNew(self.root, self.db_manager, self.current_user['id'], 'expense')\n            self.root.wait_window(form.window)"
        ),
        (
            "if dialog.result:",
            "if form.result:"
        )
    ]
    
    # Aplicar substituições
    modified = False
    for old, new in replacements:
        if old in content:
            content = content.replace(old, new)
            modified = True
            print(f"✅ Substituído: {old[:50]}...")
    
    if modified:
        # Salvar arquivo corrigido
        with open("src/gui/main_window.py", "w", encoding="utf-8") as f:
            f.write(content)
        print("✅ Arquivo corrigido e salvo!")
        
        # Limpar cache
        cache_dirs = ["__pycache__", "src/__pycache__", "src/gui/__pycache__", "src/modules/__pycache__"]
        for cache_dir in cache_dirs:
            if os.path.exists(cache_dir):
                shutil.rmtree(cache_dir)
        print("✅ Cache limpo!")
        
    else:
        print("ℹ️  Nenhuma modificação necessária")
    
    print("\n🎉 CORREÇÃO CONCLUÍDA!")

if __name__ == "__main__":
    fix_main_window()
