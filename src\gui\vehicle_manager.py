#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Gerenciador principal de veículos, manutenção e combustível
"""

import tkinter as tk
from tkinter import ttk, messagebox
from datetime import datetime, date
from .vehicle_form import VehicleForm
from .maintenance_form import MaintenanceForm
from .fuel_form import FuelForm

class VehicleManager:
    def __init__(self, parent, db_manager, user_id):
        self.parent = parent
        self.db_manager = db_manager
        self.user_id = user_id
        
        # Criar janela
        self.window = tk.Toplevel(parent)
        self.window.title("Gerenciamento de Veículos")
        self.window.geometry("1000x700")
        self.window.transient(parent)
        self.window.grab_set()
        
        # Centralizar janela
        self.center_window()
        
        # Criar interface
        self.create_widgets()
        
        # Carregar dados iniciais
        self.load_vehicles()
    
    def center_window(self):
        """Centraliza a janela na tela"""
        self.window.update_idletasks()
        x = (self.window.winfo_screenwidth() // 2) - (1000 // 2)
        y = (self.window.winfo_screenheight() // 2) - (700 // 2)
        self.window.geometry(f"1000x700+{x}+{y}")
    
    def create_widgets(self):
        """Cria os widgets da interface"""
        # Frame principal
        main_frame = ttk.Frame(self.window, padding="10")
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # Título
        title = ttk.Label(main_frame, text="Gerenciamento de Veículos", 
                         font=('Arial', 16, 'bold'))
        title.pack(pady=(0, 20))
        
        # Notebook para abas
        self.notebook = ttk.Notebook(main_frame)
        self.notebook.pack(fill=tk.BOTH, expand=True)
        
        # Aba de veículos
        self.create_vehicles_tab()
        
        # Aba de manutenção
        self.create_maintenance_tab()
        
        # Aba de combustível
        self.create_fuel_tab()
        
        # Frame para botões
        buttons_frame = ttk.Frame(main_frame)
        buttons_frame.pack(fill=tk.X, pady=(10, 0))
        
        ttk.Button(buttons_frame, text="Fechar", command=self.close).pack(side=tk.RIGHT)
    
    def create_vehicles_tab(self):
        """Cria a aba de gerenciamento de veículos"""
        vehicles_frame = ttk.Frame(self.notebook)
        self.notebook.add(vehicles_frame, text="Veículos")
        
        # Frame para botões da aba veículos
        vehicles_buttons_frame = ttk.Frame(vehicles_frame)
        vehicles_buttons_frame.pack(fill=tk.X, pady=(0, 10))
        
        ttk.Button(vehicles_buttons_frame, text="Novo Veículo", 
                  command=self.new_vehicle).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(vehicles_buttons_frame, text="Editar", 
                  command=self.edit_vehicle).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(vehicles_buttons_frame, text="Excluir", 
                  command=self.delete_vehicle).pack(side=tk.LEFT)
        
        # Treeview para veículos
        columns = ('ID', 'Nome', 'Marca', 'Modelo', 'Ano', 'Placa', 'Combustível', 'Quilometragem')
        self.vehicles_tree = ttk.Treeview(vehicles_frame, columns=columns, show='headings', height=15)
        
        # Configurar colunas
        for col in columns:
            self.vehicles_tree.heading(col, text=col)
            if col == 'ID':
                self.vehicles_tree.column(col, width=50, minwidth=50)
            elif col in ['Nome', 'Marca', 'Modelo']:
                self.vehicles_tree.column(col, width=120, minwidth=100)
            elif col == 'Ano':
                self.vehicles_tree.column(col, width=60, minwidth=60)
            elif col == 'Placa':
                self.vehicles_tree.column(col, width=80, minwidth=80)
            elif col == 'Combustível':
                self.vehicles_tree.column(col, width=100, minwidth=80)
            else:
                self.vehicles_tree.column(col, width=100, minwidth=80)
        
        # Scrollbar para veículos
        vehicles_scrollbar = ttk.Scrollbar(vehicles_frame, orient=tk.VERTICAL, command=self.vehicles_tree.yview)
        self.vehicles_tree.configure(yscrollcommand=vehicles_scrollbar.set)
        
        # Pack treeview e scrollbar
        self.vehicles_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        vehicles_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
    
    def create_maintenance_tab(self):
        """Cria a aba de manutenção"""
        maintenance_frame = ttk.Frame(self.notebook)
        self.notebook.add(maintenance_frame, text="Manutenção")
        
        # Frame para filtros e botões
        maintenance_top_frame = ttk.Frame(maintenance_frame)
        maintenance_top_frame.pack(fill=tk.X, pady=(0, 10))
        
        # Filtro por veículo
        ttk.Label(maintenance_top_frame, text="Veículo:").pack(side=tk.LEFT, padx=(0, 5))
        self.maintenance_vehicle_var = tk.StringVar()
        self.maintenance_vehicle_combo = ttk.Combobox(maintenance_top_frame, 
                                                     textvariable=self.maintenance_vehicle_var,
                                                     width=30, state='readonly')
        self.maintenance_vehicle_combo.pack(side=tk.LEFT, padx=(0, 10))
        self.maintenance_vehicle_combo.bind('<<ComboboxSelected>>', self.filter_maintenance)
        
        # Botões
        ttk.Button(maintenance_top_frame, text="Nova Manutenção", 
                  command=self.new_maintenance).pack(side=tk.LEFT, padx=(10, 5))
        ttk.Button(maintenance_top_frame, text="Editar", 
                  command=self.edit_maintenance).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(maintenance_top_frame, text="Excluir", 
                  command=self.delete_maintenance).pack(side=tk.LEFT)
        
        # Treeview para manutenção
        maintenance_columns = ('ID', 'Veículo', 'Tipo', 'Descrição', 'Data', 'Custo', 'Prestador')
        self.maintenance_tree = ttk.Treeview(maintenance_frame, columns=maintenance_columns, 
                                           show='headings', height=15)
        
        # Configurar colunas de manutenção
        for col in maintenance_columns:
            self.maintenance_tree.heading(col, text=col)
            if col == 'ID':
                self.maintenance_tree.column(col, width=50, minwidth=50)
            elif col in ['Veículo', 'Prestador']:
                self.maintenance_tree.column(col, width=120, minwidth=100)
            elif col == 'Tipo':
                self.maintenance_tree.column(col, width=100, minwidth=80)
            elif col == 'Descrição':
                self.maintenance_tree.column(col, width=200, minwidth=150)
            elif col == 'Data':
                self.maintenance_tree.column(col, width=80, minwidth=80)
            else:
                self.maintenance_tree.column(col, width=80, minwidth=70)
        
        # Scrollbar para manutenção
        maintenance_scrollbar = ttk.Scrollbar(maintenance_frame, orient=tk.VERTICAL, 
                                            command=self.maintenance_tree.yview)
        self.maintenance_tree.configure(yscrollcommand=maintenance_scrollbar.set)
        
        # Pack treeview e scrollbar de manutenção
        self.maintenance_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        maintenance_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
    
    def create_fuel_tab(self):
        """Cria a aba de combustível"""
        fuel_frame = ttk.Frame(self.notebook)
        self.notebook.add(fuel_frame, text="Combustível")
        
        # Frame para filtros e botões
        fuel_top_frame = ttk.Frame(fuel_frame)
        fuel_top_frame.pack(fill=tk.X, pady=(0, 10))
        
        # Filtro por veículo
        ttk.Label(fuel_top_frame, text="Veículo:").pack(side=tk.LEFT, padx=(0, 5))
        self.fuel_vehicle_var = tk.StringVar()
        self.fuel_vehicle_combo = ttk.Combobox(fuel_top_frame, 
                                              textvariable=self.fuel_vehicle_var,
                                              width=30, state='readonly')
        self.fuel_vehicle_combo.pack(side=tk.LEFT, padx=(0, 10))
        self.fuel_vehicle_combo.bind('<<ComboboxSelected>>', self.filter_fuel)
        
        # Botões
        ttk.Button(fuel_top_frame, text="Novo Abastecimento", 
                  command=self.new_fuel_record).pack(side=tk.LEFT, padx=(10, 5))
        ttk.Button(fuel_top_frame, text="Editar", 
                  command=self.edit_fuel_record).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(fuel_top_frame, text="Excluir", 
                  command=self.delete_fuel_record).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(fuel_top_frame, text="Estatísticas", 
                  command=self.show_fuel_statistics).pack(side=tk.LEFT)
        
        # Treeview para combustível
        fuel_columns = ('ID', 'Veículo', 'Data', 'Combustível', 'Litros', 'Preço/L', 'Total', 'Km', 'Eficiência')
        self.fuel_tree = ttk.Treeview(fuel_frame, columns=fuel_columns, show='headings', height=15)
        
        # Configurar colunas de combustível
        for col in fuel_columns:
            self.fuel_tree.heading(col, text=col)
            if col == 'ID':
                self.fuel_tree.column(col, width=50, minwidth=50)
            elif col == 'Veículo':
                self.fuel_tree.column(col, width=120, minwidth=100)
            elif col in ['Data', 'Combustível']:
                self.fuel_tree.column(col, width=80, minwidth=70)
            elif col in ['Litros', 'Preço/L', 'Total', 'Km', 'Eficiência']:
                self.fuel_tree.column(col, width=70, minwidth=60)
        
        # Scrollbar para combustível
        fuel_scrollbar = ttk.Scrollbar(fuel_frame, orient=tk.VERTICAL, command=self.fuel_tree.yview)
        self.fuel_tree.configure(yscrollcommand=fuel_scrollbar.set)
        
        # Pack treeview e scrollbar de combustível
        self.fuel_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        fuel_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
    
    def load_vehicles(self):
        """Carrega a lista de veículos"""
        try:
            # Limpar treeview
            for item in self.vehicles_tree.get_children():
                self.vehicles_tree.delete(item)
            
            # Carregar veículos
            vehicles = self.db_manager.get_user_vehicles(self.user_id)
            
            # Atualizar combos de filtro
            vehicle_options = ["Todos"] + [f"{v['name']} - {v['brand']} {v['model']}" for v in vehicles]
            self.maintenance_vehicle_combo['values'] = vehicle_options
            self.fuel_vehicle_combo['values'] = vehicle_options
            
            # Definir "Todos" como padrão
            self.maintenance_vehicle_var.set("Todos")
            self.fuel_vehicle_var.set("Todos")
            
            # Preencher treeview de veículos
            for vehicle in vehicles:
                self.vehicles_tree.insert('', 'end', values=(
                    vehicle['id'],
                    vehicle['name'],
                    vehicle['brand'],
                    vehicle['model'],
                    vehicle['year'],
                    vehicle['license_plate'] or '',
                    vehicle['fuel_type'] or '',
                    vehicle['mileage'] or 0
                ))
            
            # Carregar dados das outras abas
            self.load_maintenance()
            self.load_fuel_records()
            
        except Exception as e:
            messagebox.showerror("Erro", f"Erro ao carregar veículos: {str(e)}")
    
    def load_maintenance(self):
        """Carrega registros de manutenção"""
        try:
            # Limpar treeview
            for item in self.maintenance_tree.get_children():
                self.maintenance_tree.delete(item)
            
            # Carregar todos os registros de manutenção do usuário
            vehicles = self.db_manager.get_user_vehicles(self.user_id)
            for vehicle in vehicles:
                maintenance_records = self.db_manager.get_vehicle_maintenance(vehicle['id'], self.user_id)
                for record in maintenance_records:
                    self.maintenance_tree.insert('', 'end', values=(
                        record['id'],
                        record['vehicle_name'],
                        record['maintenance_type'],
                        record['description'][:50] + '...' if len(record['description']) > 50 else record['description'],
                        record['service_date'],
                        f"R$ {record['cost']:.2f}",
                        record['service_provider'] or ''
                    ))
                    
        except Exception as e:
            messagebox.showerror("Erro", f"Erro ao carregar manutenção: {str(e)}")
    
    def load_fuel_records(self):
        """Carrega registros de combustível"""
        try:
            # Limpar treeview
            for item in self.fuel_tree.get_children():
                self.fuel_tree.delete(item)
            
            # Carregar todos os registros de combustível do usuário
            vehicles = self.db_manager.get_user_vehicles(self.user_id)
            for vehicle in vehicles:
                fuel_records = self.db_manager.get_vehicle_fuel_records(vehicle['id'], self.user_id, limit=50)
                for record in fuel_records:
                    efficiency = f"{record['fuel_efficiency']:.2f}" if record['fuel_efficiency'] else ""
                    self.fuel_tree.insert('', 'end', values=(
                        record['id'],
                        record['vehicle_name'],
                        record['fuel_date'],
                        record['fuel_type'],
                        f"{record['liters']:.2f}",
                        f"R$ {record['price_per_liter']:.3f}",
                        f"R$ {record['total_cost']:.2f}",
                        record['mileage'] or '',
                        efficiency
                    ))
                    
        except Exception as e:
            messagebox.showerror("Erro", f"Erro ao carregar combustível: {str(e)}")
    
    def new_vehicle(self):
        """Abre formulário para novo veículo"""
        VehicleForm(self.window, self.db_manager, self.user_id, callback=self.load_vehicles)
    
    def edit_vehicle(self):
        """Edita veículo selecionado"""
        selection = self.vehicles_tree.selection()
        if not selection:
            messagebox.showwarning("Aviso", "Selecione um veículo para editar!")
            return
        
        item = self.vehicles_tree.item(selection[0])
        vehicle_id = item['values'][0]
        
        # Buscar dados completos do veículo
        try:
            vehicles = self.db_manager.get_user_vehicles(self.user_id, active_only=False)
            vehicle_data = next((v for v in vehicles if v['id'] == vehicle_id), None)
            
            if vehicle_data:
                VehicleForm(self.window, self.db_manager, self.user_id, 
                           vehicle_data=vehicle_data, callback=self.load_vehicles)
            else:
                messagebox.showerror("Erro", "Veículo não encontrado!")
                
        except Exception as e:
            messagebox.showerror("Erro", f"Erro ao carregar dados do veículo: {str(e)}")
    
    def delete_vehicle(self):
        """Exclui veículo selecionado"""
        selection = self.vehicles_tree.selection()
        if not selection:
            messagebox.showwarning("Aviso", "Selecione um veículo para excluir!")
            return
        
        item = self.vehicles_tree.item(selection[0])
        vehicle_id = item['values'][0]
        vehicle_name = item['values'][1]
        
        if messagebox.askyesno("Confirmar", f"Deseja realmente excluir o veículo '{vehicle_name}'?"):
            try:
                self.db_manager.delete_vehicle(vehicle_id)
                messagebox.showinfo("Sucesso", "Veículo excluído com sucesso!")
                self.load_vehicles()
            except Exception as e:
                messagebox.showerror("Erro", f"Erro ao excluir veículo: {str(e)}")
    
    def new_maintenance(self):
        """Abre formulário para nova manutenção"""
        MaintenanceForm(self.window, self.db_manager, self.user_id, callback=self.load_maintenance)
    
    def edit_maintenance(self):
        """Edita manutenção selecionada"""
        selection = self.maintenance_tree.selection()
        if not selection:
            messagebox.showwarning("Aviso", "Selecione uma manutenção para editar!")
            return
        
        # Implementar edição de manutenção
        messagebox.showinfo("Info", "Funcionalidade em desenvolvimento")
    
    def delete_maintenance(self):
        """Exclui manutenção selecionada"""
        selection = self.maintenance_tree.selection()
        if not selection:
            messagebox.showwarning("Aviso", "Selecione uma manutenção para excluir!")
            return
        
        # Implementar exclusão de manutenção
        messagebox.showinfo("Info", "Funcionalidade em desenvolvimento")
    
    def new_fuel_record(self):
        """Abre formulário para novo abastecimento"""
        FuelForm(self.window, self.db_manager, self.user_id, callback=self.load_fuel_records)
    
    def edit_fuel_record(self):
        """Edita registro de combustível selecionado"""
        selection = self.fuel_tree.selection()
        if not selection:
            messagebox.showwarning("Aviso", "Selecione um abastecimento para editar!")
            return
        
        # Implementar edição de combustível
        messagebox.showinfo("Info", "Funcionalidade em desenvolvimento")
    
    def delete_fuel_record(self):
        """Exclui registro de combustível selecionado"""
        selection = self.fuel_tree.selection()
        if not selection:
            messagebox.showwarning("Aviso", "Selecione um abastecimento para excluir!")
            return
        
        # Implementar exclusão de combustível
        messagebox.showinfo("Info", "Funcionalidade em desenvolvimento")
    
    def show_fuel_statistics(self):
        """Mostra estatísticas de combustível"""
        messagebox.showinfo("Info", "Funcionalidade em desenvolvimento")
    
    def filter_maintenance(self, event=None):
        """Filtra manutenção por veículo"""
        # Implementar filtro
        pass
    
    def filter_fuel(self, event=None):
        """Filtra combustível por veículo"""
        # Implementar filtro
        pass
    
    def close(self):
        """Fecha a janela"""
        self.window.destroy()
