#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Módulo de gerenciamento de alertas e notificações
"""

from datetime import datetime, date, timedelta
import sqlite3

class AlertManager:
    def __init__(self, db_manager):
        self.db_manager = db_manager
    
    def get_due_bills_alerts(self, user_id, days_ahead=7):
        """Retorna alertas de contas vencendo"""
        try:
            today = date.today()
            end_date = today + timedelta(days=days_ahead)
            
            query = '''
                SELECT 
                    t.id, t.description, t.amount, t.due_date, t.transaction_type,
                    c.name as category_name, c.color as category_color,
                    w.name as wallet_name
                FROM transactions t
                JOIN categories c ON t.category_id = c.id
                JOIN wallets w ON t.wallet_id = w.id
                WHERE t.user_id = ? 
                AND t.is_paid = FALSE 
                AND t.due_date IS NOT NULL
                AND t.due_date BETWEEN ? AND ?
                ORDER BY t.due_date ASC
            '''
            
            results = self.db_manager.execute_query(query, (user_id, today.isoformat(), end_date.isoformat()))
            
            alerts = []
            for row in results:
                due_date = datetime.strptime(row['due_date'], '%Y-%m-%d').date()
                days_left = (due_date - today).days
                
                # Determinar prioridade do alerta
                if days_left == 0:
                    priority = "CRITICAL"
                    alert_type = "VENCE HOJE"
                elif days_left == 1:
                    priority = "HIGH"
                    alert_type = "VENCE AMANHÃ"
                elif days_left <= 3:
                    priority = "MEDIUM"
                    alert_type = f"VENCE EM {days_left} DIAS"
                else:
                    priority = "LOW"
                    alert_type = f"VENCE EM {days_left} DIAS"
                
                alerts.append({
                    'id': row['id'],
                    'type': 'DUE_BILL',
                    'priority': priority,
                    'alert_type': alert_type,
                    'title': f"{row['description']} - {alert_type}",
                    'description': f"R$ {row['amount']:,.2f} - {row['category_name']} ({row['wallet_name']})",
                    'due_date': row['due_date'],
                    'days_left': days_left,
                    'amount': row['amount'],
                    'category': row['category_name'],
                    'wallet': row['wallet_name'],
                    'color': row['category_color']
                })
            
            return alerts
            
        except Exception as e:
            raise Exception(f"Erro ao obter alertas de vencimento: {str(e)}")
    
    def get_overdue_bills_alerts(self, user_id):
        """Retorna alertas de contas em atraso"""
        try:
            today = date.today()
            
            query = '''
                SELECT 
                    t.id, t.description, t.amount, t.due_date, t.transaction_type,
                    c.name as category_name, c.color as category_color,
                    w.name as wallet_name
                FROM transactions t
                JOIN categories c ON t.category_id = c.id
                JOIN wallets w ON t.wallet_id = w.id
                WHERE t.user_id = ? 
                AND t.is_paid = FALSE 
                AND t.due_date IS NOT NULL
                AND t.due_date < ?
                ORDER BY t.due_date ASC
            '''
            
            results = self.db_manager.execute_query(query, (user_id, today.isoformat()))
            
            alerts = []
            for row in results:
                due_date = datetime.strptime(row['due_date'], '%Y-%m-%d').date()
                days_overdue = (today - due_date).days
                
                alerts.append({
                    'id': row['id'],
                    'type': 'OVERDUE_BILL',
                    'priority': 'CRITICAL',
                    'alert_type': f"EM ATRASO HÁ {days_overdue} DIAS",
                    'title': f"{row['description']} - EM ATRASO",
                    'description': f"R$ {row['amount']:,.2f} - {row['category_name']} ({row['wallet_name']})",
                    'due_date': row['due_date'],
                    'days_overdue': days_overdue,
                    'amount': row['amount'],
                    'category': row['category_name'],
                    'wallet': row['wallet_name'],
                    'color': '#dc3545'  # Vermelho para atraso
                })
            
            return alerts
            
        except Exception as e:
            raise Exception(f"Erro ao obter alertas de atraso: {str(e)}")
    
    def get_low_balance_alerts(self, user_id, threshold_percentage=10):
        """Retorna alertas de saldo baixo"""
        try:
            query = '''
                SELECT 
                    id, name, current_balance, initial_balance, wallet_type
                FROM wallets
                WHERE user_id = ? AND is_active = TRUE
            '''
            
            results = self.db_manager.execute_query(query, (user_id,))
            
            alerts = []
            for row in results:
                current_balance = row['current_balance']
                initial_balance = row['initial_balance']
                
                # Calcular porcentagem do saldo inicial
                if initial_balance > 0:
                    percentage = (current_balance / initial_balance) * 100
                    
                    if percentage <= threshold_percentage:
                        wallet_type_names = {
                            'checking': 'Conta Corrente',
                            'savings': 'Poupança',
                            'credit': 'Cartão de Crédito',
                            'cash': 'Dinheiro'
                        }
                        
                        alerts.append({
                            'id': row['id'],
                            'type': 'LOW_BALANCE',
                            'priority': 'MEDIUM',
                            'alert_type': 'SALDO BAIXO',
                            'title': f"Saldo baixo - {row['name']}",
                            'description': f"R$ {current_balance:,.2f} ({percentage:.1f}% do saldo inicial) - {wallet_type_names.get(row['wallet_type'], row['wallet_type'])}",
                            'current_balance': current_balance,
                            'percentage': percentage,
                            'wallet_name': row['name'],
                            'wallet_type': row['wallet_type'],
                            'color': '#ffc107'  # Amarelo para aviso
                        })
            
            return alerts
            
        except Exception as e:
            raise Exception(f"Erro ao obter alertas de saldo baixo: {str(e)}")
    
    def get_negative_balance_alerts(self, user_id):
        """Retorna alertas de saldo negativo"""
        try:
            query = '''
                SELECT 
                    id, name, current_balance, wallet_type
                FROM wallets
                WHERE user_id = ? AND is_active = TRUE AND current_balance < 0
            '''
            
            results = self.db_manager.execute_query(query, (user_id,))
            
            alerts = []
            for row in results:
                wallet_type_names = {
                    'checking': 'Conta Corrente',
                    'savings': 'Poupança',
                    'credit': 'Cartão de Crédito',
                    'cash': 'Dinheiro'
                }
                
                alerts.append({
                    'id': row['id'],
                    'type': 'NEGATIVE_BALANCE',
                    'priority': 'HIGH',
                    'alert_type': 'SALDO NEGATIVO',
                    'title': f"Saldo negativo - {row['name']}",
                    'description': f"R$ {row['current_balance']:,.2f} - {wallet_type_names.get(row['wallet_type'], row['wallet_type'])}",
                    'current_balance': row['current_balance'],
                    'wallet_name': row['name'],
                    'wallet_type': row['wallet_type'],
                    'color': '#dc3545'  # Vermelho para negativo
                })
            
            return alerts
            
        except Exception as e:
            raise Exception(f"Erro ao obter alertas de saldo negativo: {str(e)}")
    
    def get_recurring_transactions_alerts(self, user_id):
        """Retorna alertas de transações recorrentes que precisam ser processadas"""
        try:
            today = date.today()
            
            query = '''
                SELECT 
                    t.id, t.description, t.amount, t.transaction_type, t.recurring_type,
                    t.recurring_day, t.transaction_date,
                    c.name as category_name, w.name as wallet_name
                FROM transactions t
                JOIN categories c ON t.category_id = c.id
                JOIN wallets w ON t.wallet_id = w.id
                WHERE t.user_id = ? AND t.is_recurring = TRUE
                ORDER BY t.transaction_date DESC
            '''
            
            results = self.db_manager.execute_query(query, (user_id,))
            
            alerts = []
            for row in results:
                last_date = datetime.strptime(row['transaction_date'], '%Y-%m-%d').date()
                
                # Calcular próxima data baseada no tipo de recorrência
                next_date = self.calculate_next_recurring_date(
                    last_date, row['recurring_type'], row['recurring_day']
                )
                
                if next_date and next_date <= today:
                    trans_type = "Receita" if row['transaction_type'] == 'income' else "Despesa"
                    
                    alerts.append({
                        'id': row['id'],
                        'type': 'RECURRING_TRANSACTION',
                        'priority': 'MEDIUM',
                        'alert_type': 'TRANSAÇÃO RECORRENTE',
                        'title': f"{trans_type} recorrente - {row['description']}",
                        'description': f"R$ {row['amount']:,.2f} - {row['category_name']} ({row['wallet_name']})",
                        'next_date': next_date.isoformat(),
                        'amount': row['amount'],
                        'transaction_type': row['transaction_type'],
                        'category': row['category_name'],
                        'wallet': row['wallet_name'],
                        'color': '#17a2b8'  # Azul para informativo
                    })
            
            return alerts
            
        except Exception as e:
            raise Exception(f"Erro ao obter alertas de transações recorrentes: {str(e)}")
    
    def calculate_next_recurring_date(self, last_date, recurring_type, recurring_day):
        """Calcula próxima data de uma transação recorrente"""
        try:
            if recurring_type == 'daily':
                return last_date + timedelta(days=1)
            
            elif recurring_type == 'weekly':
                return last_date + timedelta(weeks=1)
            
            elif recurring_type == 'monthly':
                if recurring_day:
                    # Próximo mês no dia especificado
                    if last_date.month == 12:
                        next_year = last_date.year + 1
                        next_month = 1
                    else:
                        next_year = last_date.year
                        next_month = last_date.month + 1
                    
                    try:
                        return date(next_year, next_month, recurring_day)
                    except ValueError:
                        # Dia não existe no mês (ex: 31 em fevereiro)
                        # Usar último dia do mês
                        import calendar
                        last_day = calendar.monthrange(next_year, next_month)[1]
                        return date(next_year, next_month, last_day)
                else:
                    # Mesmo dia do próximo mês
                    return last_date.replace(month=last_date.month + 1 if last_date.month < 12 else 1,
                                           year=last_date.year if last_date.month < 12 else last_date.year + 1)
            
            elif recurring_type == 'yearly':
                if recurring_day:
                    return date(last_date.year + 1, last_date.month, recurring_day)
                else:
                    return last_date.replace(year=last_date.year + 1)
            
            return None
            
        except Exception:
            return None
    
    def get_all_alerts(self, user_id, include_low_priority=True):
        """Retorna todos os alertas do usuário"""
        try:
            all_alerts = []
            
            # Contas vencendo
            all_alerts.extend(self.get_due_bills_alerts(user_id))
            
            # Contas em atraso
            all_alerts.extend(self.get_overdue_bills_alerts(user_id))
            
            # Saldo negativo
            all_alerts.extend(self.get_negative_balance_alerts(user_id))
            
            # Saldo baixo
            all_alerts.extend(self.get_low_balance_alerts(user_id))
            
            # Transações recorrentes
            all_alerts.extend(self.get_recurring_transactions_alerts(user_id))
            
            # Filtrar por prioridade se necessário
            if not include_low_priority:
                all_alerts = [alert for alert in all_alerts if alert['priority'] != 'LOW']
            
            # Ordenar por prioridade e data
            priority_order = {'CRITICAL': 0, 'HIGH': 1, 'MEDIUM': 2, 'LOW': 3}
            all_alerts.sort(key=lambda x: (priority_order.get(x['priority'], 4), x.get('due_date', '9999-12-31')))
            
            return all_alerts
            
        except Exception as e:
            raise Exception(f"Erro ao obter todos os alertas: {str(e)}")
    
    def get_alerts_summary(self, user_id):
        """Retorna resumo dos alertas"""
        try:
            alerts = self.get_all_alerts(user_id)
            
            summary = {
                'total': len(alerts),
                'critical': len([a for a in alerts if a['priority'] == 'CRITICAL']),
                'high': len([a for a in alerts if a['priority'] == 'HIGH']),
                'medium': len([a for a in alerts if a['priority'] == 'MEDIUM']),
                'low': len([a for a in alerts if a['priority'] == 'LOW']),
                'by_type': {}
            }
            
            # Contar por tipo
            for alert in alerts:
                alert_type = alert['type']
                if alert_type not in summary['by_type']:
                    summary['by_type'][alert_type] = 0
                summary['by_type'][alert_type] += 1
            
            return summary
            
        except Exception as e:
            raise Exception(f"Erro ao obter resumo dos alertas: {str(e)}")
