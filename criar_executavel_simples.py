#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Script para criar executável do Sistema de Gestão de Contas
Versão simplificada e otimizada
"""

import os
import sys
import subprocess
import shutil
from pathlib import Path

def limpar_builds_antigos():
    """Remove builds e dist antigos"""
    print("🧹 Limpando builds antigos...")
    
    dirs_para_limpar = ['build', 'dist', '__pycache__']
    
    for dir_name in dirs_para_limpar:
        if os.path.exists(dir_name):
            try:
                shutil.rmtree(dir_name)
                print(f"   ✓ Removido: {dir_name}")
            except Exception as e:
                print(f"   ⚠️  Erro ao remover {dir_name}: {e}")

def verificar_dependencias():
    """Verifica se PyInstaller está instalado"""
    print("📦 Verificando dependências...")
    
    try:
        import PyInstaller
        print("   ✓ PyInstaller encontrado")
        return True
    except ImportError:
        print("   ❌ PyInstaller não encontrado")
        print("   💡 Instalando PyInstaller...")
        
        try:
            subprocess.check_call([sys.executable, "-m", "pip", "install", "pyinstaller"])
            print("   ✅ PyInstaller instalado com sucesso")
            return True
        except subprocess.CalledProcessError:
            print("   ❌ Erro ao instalar PyInstaller")
            return False

def criar_spec_file():
    """Cria arquivo .spec otimizado"""
    print("📝 Criando arquivo de configuração...")
    
    spec_content = '''# -*- mode: python ; coding: utf-8 -*-

block_cipher = None

a = Analysis(
    ['executar_terminal.py'],
    pathex=[],
    binaries=[],
    datas=[
        ('src', 'src'),
        ('data', 'data'),
        ('main.py', '.'),
        ('config.py', '.'),
        ('requirements.txt', '.'),
    ],
    hiddenimports=[
        'tkinter',
        'tkinter.ttk',
        'tkinter.messagebox',
        'tkinter.filedialog',
        'sqlite3',
        'bcrypt',
        'PIL',
        'PIL.Image',
        'PIL.ImageTk',
        'tkcalendar',
        'datetime',
        'decimal',
        'json',
        'os',
        'sys',
        'threading',
        'time',
        'traceback',
    ],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[
        'matplotlib',
        'numpy',
        'pandas',
        'scipy',
        'jupyter',
        'IPython',
        'pytest',
    ],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='GestaoContas',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=True,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon=None,
)
'''
    
    with open('GestaoContas.spec', 'w', encoding='utf-8') as f:
        f.write(spec_content)
    
    print("   ✅ Arquivo de configuração criado: GestaoContas.spec")

def compilar_executavel():
    """Compila o executável usando PyInstaller"""
    print("🔨 Compilando executável...")
    print("   ⏳ Isso pode levar alguns minutos...")
    
    try:
        # Comando PyInstaller
        cmd = [
            sys.executable, "-m", "PyInstaller",
            "--clean",
            "--noconfirm",
            "GestaoContas.spec"
        ]
        
        print(f"   🔄 Executando: {' '.join(cmd)}")
        
        # Executar com output em tempo real
        process = subprocess.Popen(
            cmd,
            stdout=subprocess.PIPE,
            stderr=subprocess.STDOUT,
            universal_newlines=True,
            bufsize=1
        )
        
        # Mostrar progresso
        for line in process.stdout:
            line = line.strip()
            if line:
                if "INFO:" in line:
                    print(f"   📋 {line}")
                elif "WARNING:" in line:
                    print(f"   ⚠️  {line}")
                elif "ERROR:" in line:
                    print(f"   ❌ {line}")
        
        process.wait()
        
        if process.returncode == 0:
            print("   ✅ Compilação concluída com sucesso!")
            return True
        else:
            print(f"   ❌ Erro na compilação (código: {process.returncode})")
            return False
            
    except Exception as e:
        print(f"   ❌ Erro durante compilação: {e}")
        return False

def verificar_executavel():
    """Verifica se o executável foi criado"""
    print("🔍 Verificando executável...")
    
    exe_path = Path("dist/GestaoContas.exe")
    
    if exe_path.exists():
        size_mb = exe_path.stat().st_size / (1024 * 1024)
        print(f"   ✅ Executável criado: {exe_path}")
        print(f"   📏 Tamanho: {size_mb:.1f} MB")
        
        # Criar atalho na área de trabalho
        criar_atalho_desktop(exe_path)
        
        return True
    else:
        print("   ❌ Executável não encontrado")
        return False

def criar_atalho_desktop(exe_path):
    """Cria atalho na área de trabalho"""
    try:
        desktop = Path.home() / "Desktop"
        if not desktop.exists():
            desktop = Path.home() / "Área de Trabalho"
        
        if desktop.exists():
            atalho_path = desktop / "Gestão de Contas.lnk"
            
            # Criar arquivo batch para executar
            batch_content = f'''@echo off
cd /d "{exe_path.parent.absolute()}"
start "" "{exe_path.name}"
'''
            
            batch_path = exe_path.parent / "executar.bat"
            with open(batch_path, 'w', encoding='utf-8') as f:
                f.write(batch_content)
            
            print(f"   🔗 Arquivo de execução criado: {batch_path}")
            print(f"   💡 Para criar atalho, copie {exe_path} para a área de trabalho")
        
    except Exception as e:
        print(f"   ⚠️  Erro ao criar atalho: {e}")

def criar_readme_executavel():
    """Cria README para o executável"""
    print("📖 Criando documentação...")
    
    readme_content = '''# 🚗 Sistema de Gestão de Contas - Executável

## 📋 Como usar

1. **Executar o programa:**
   - Clique duas vezes em `GestaoContas.exe`
   - Ou execute pelo terminal: `./GestaoContas.exe`

2. **Login inicial:**
   - Usuário: `admin`
   - Senha: `admin123`

3. **Funcionalidades:**
   - ✅ Controle financeiro completo
   - ✅ Gerenciamento de carteiras
   - ✅ Transações (receitas e despesas)
   - ✅ Relatórios financeiros
   - ✅ Cadastro de veículos
   - ✅ Controle de manutenção
   - ✅ Registro de combustível
   - ✅ Estatísticas de consumo

## 🔧 Requisitos do Sistema

- Windows 10 ou superior
- Não precisa instalar Python
- Executável independente (standalone)

## 📁 Estrutura de Arquivos

```
dist/
├── GestaoContas.exe          # Executável principal
├── executar.bat              # Script de execução
└── data/                     # Banco de dados (criado automaticamente)
```

## 🚨 Solução de Problemas

### Antivírus bloqueando
- Alguns antivírus podem bloquear executáveis Python
- Adicione exceção para a pasta do programa

### Erro ao iniciar
- Execute como administrador
- Verifique se a pasta tem permissões de escrita

### Banco de dados
- O banco é criado automaticamente na pasta `data/`
- Para resetar: delete a pasta `data/` e execute novamente

## 📞 Suporte

Em caso de problemas, verifique:
1. Permissões da pasta
2. Antivírus
3. Espaço em disco disponível

---
**Versão:** 2.0 com Gerenciamento de Veículos
**Data:** {datetime.now().strftime('%d/%m/%Y')}
'''
    
    import datetime
    
    with open('dist/README_EXECUTAVEL.md', 'w', encoding='utf-8') as f:
        f.write(readme_content)
    
    print("   ✅ Documentação criada: dist/README_EXECUTAVEL.md")

def main():
    """Função principal"""
    print("=" * 70)
    print("           🔨 CRIADOR DE EXECUTÁVEL")
    print("           Sistema de Gestão de Contas")
    print("=" * 70)
    print()
    
    # Verificar se estamos no diretório correto
    if not os.path.exists('executar_terminal.py'):
        print("❌ Arquivo executar_terminal.py não encontrado!")
        print("💡 Execute este script na pasta raiz do projeto")
        input("Pressione Enter para sair...")
        return
    
    try:
        # Passo 1: Limpar builds antigos
        limpar_builds_antigos()
        print()
        
        # Passo 2: Verificar dependências
        if not verificar_dependencias():
            print("❌ Não foi possível instalar as dependências")
            input("Pressione Enter para sair...")
            return
        print()
        
        # Passo 3: Criar arquivo de configuração
        criar_spec_file()
        print()
        
        # Passo 4: Compilar executável
        if not compilar_executavel():
            print("❌ Falha na compilação")
            input("Pressione Enter para sair...")
            return
        print()
        
        # Passo 5: Verificar resultado
        if not verificar_executavel():
            print("❌ Executável não foi criado")
            input("Pressione Enter para sair...")
            return
        print()
        
        # Passo 6: Criar documentação
        criar_readme_executavel()
        print()
        
        print("🎉 SUCESSO!")
        print("=" * 70)
        print("✅ Executável criado com sucesso!")
        print(f"📁 Local: {os.path.abspath('dist/GestaoContas.exe')}")
        print()
        print("🚀 Para executar:")
        print("   1. Vá para a pasta 'dist'")
        print("   2. Clique duas vezes em 'GestaoContas.exe'")
        print("   3. Use: admin / admin123 para fazer login")
        print()
        print("📖 Leia o arquivo README_EXECUTAVEL.md para mais informações")
        
    except KeyboardInterrupt:
        print("\n⚠️  Processo interrompido pelo usuário")
    except Exception as e:
        print(f"\n❌ ERRO: {e}")
        import traceback
        traceback.print_exc()
    
    input("\nPressione Enter para sair...")

if __name__ == "__main__":
    main()
