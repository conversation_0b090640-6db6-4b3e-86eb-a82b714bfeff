# 🚗 Sistema de Gestão de Contas - Versão 2.0
## Novas Funcionalidades de Veículos

Este documento descreve as **novas funcionalidades** adicionadas ao Sistema de Gestão de Contas para gerenciamento completo de veículos, manutenção e combustível.

## 🆕 O que foi Adicionado

### 📊 Banco de Dados Expandido

**3 Novas Tabelas:**

1. **`vehicles`** - Cadastro completo de veículos
   - Dados básicos (marca, modelo, ano, placa)
   - Informações técnicas (motor, combustível, quilometragem)
   - Dados de compra e valor atual
   - Informações de seguro
   - Observações gerais

2. **`vehicle_maintenance`** - Controle de manutenção
   - Tipos de manutenção (troca de óleo, pneus, freios, etc.)
   - Datas de serviço e próximas manutenções
   - Custos e prestadores de serviço
   - Controle de garantia
   - Agendamentos e status

3. **`fuel_records`** - Registros de combustível
   - Abastecimentos com data, local e valores
   - Cálculo automático de eficiência (km/l)
   - Diferentes tipos de combustível
   - Estatísticas de consumo

### 🖥 Interface Gráfica Atualizada

**Novo Menu "Veículos":**
- Gerenciar Veículos (interface completa)
- Novo Veículo (cadastro rápido)
- Nova Manutenção (registro de serviço)
- Novo Abastecimento (registro de combustível)

**Novos Formulários:**
- `vehicle_form.py` - Cadastro/edição de veículos
- `maintenance_form.py` - Registro de manutenção
- `fuel_form.py` - Registro de abastecimento
- `vehicle_manager.py` - Gerenciador principal com abas

### ⚙ Funcionalidades Implementadas

#### Gerenciamento de Veículos
- ✅ Cadastro completo com todos os dados
- ✅ Edição e exclusão (soft delete)
- ✅ Controle de seguro com alertas de vencimento
- ✅ Histórico de valor (compra vs atual)
- ✅ Validações de dados obrigatórios

#### Controle de Manutenção
- ✅ Registro detalhado de serviços realizados
- ✅ Agendamento de próximas manutenções
- ✅ Controle de garantia dos serviços
- ✅ Histórico completo por veículo
- ✅ Diferentes tipos de manutenção
- ✅ Controle de custos

#### Gestão de Combustível
- ✅ Registro de abastecimentos
- ✅ Cálculo automático de eficiência (km/l)
- ✅ Estatísticas de consumo e gastos
- ✅ Controle por tipo de combustível
- ✅ Histórico com filtros

## 🚀 Como Usar as Novas Funcionalidades

### 1. Executar a Aplicação

```bash
# Método 1: Direto
python main.py

# Método 2: Script de execução
python executar_aplicacao.py
```

### 2. Acessar o Menu Veículos

Na barra de menu principal, clique em **"Veículos"** para acessar:

- **Gerenciar Veículos**: Interface completa com 3 abas
  - Aba Veículos: Lista e gerencia veículos
  - Aba Manutenção: Histórico de manutenções
  - Aba Combustível: Registros de abastecimento

- **Novo Veículo**: Formulário para cadastro rápido
- **Nova Manutenção**: Registro de serviço realizado
- **Novo Abastecimento**: Registro de combustível

### 3. Cadastrar um Veículo

1. Menu Veículos → Novo Veículo
2. Preencha os campos obrigatórios:
   - Nome/Apelido
   - Marca
   - Modelo
   - Ano
3. Campos opcionais:
   - Placa, cor, tipo de combustível
   - Dados de compra e seguro
   - Observações

### 4. Registrar Manutenção

1. Menu Veículos → Nova Manutenção
2. Selecione o veículo
3. Escolha o tipo de manutenção
4. Preencha descrição, data e custo
5. Opcionalmente: próxima manutenção, garantia

### 5. Registrar Abastecimento

1. Menu Veículos → Novo Abastecimento
2. Selecione o veículo
3. Preencha: data, combustível, litros, preço
4. O total é calculado automaticamente
5. Opcionalmente: quilometragem (para calcular eficiência)

## 🧪 Testar as Funcionalidades

Execute o script de teste para verificar se tudo está funcionando:

```bash
python test_vehicle_tables.py
```

Este script:
- ✅ Verifica se as 3 novas tabelas foram criadas
- ✅ Testa inserção de dados de exemplo
- ✅ Verifica consultas e relatórios
- ✅ Mostra estatísticas de combustível

## 📁 Arquivos Modificados/Criados

### Arquivos Criados
- `src/gui/vehicle_form.py` - Formulário de veículos
- `src/gui/maintenance_form.py` - Formulário de manutenção
- `src/gui/fuel_form.py` - Formulário de combustível
- `src/gui/vehicle_manager.py` - Gerenciador principal
- `test_vehicle_tables.py` - Script de teste
- `executar_aplicacao.py` - Script de execução
- `README_VEICULOS.md` - Esta documentação

### Arquivos Modificados
- `src/database.py` - Adicionadas 3 tabelas e métodos
- `src/gui/main_window.py` - Adicionado menu Veículos
- `requirements.txt` - Adicionado tkcalendar

## 🔧 Dependências Adicionadas

```txt
tkcalendar==1.6.1  # Para seleção de datas nos formulários
```

Instale com:
```bash
py -m pip install tkcalendar
```

## 📊 Estrutura do Banco de Dados

### Tabela `vehicles`
```sql
- id (INTEGER PRIMARY KEY)
- user_id (INTEGER) - FK para users
- name (TEXT) - Nome/apelido do veículo
- brand (TEXT) - Marca
- model (TEXT) - Modelo
- year (INTEGER) - Ano
- license_plate (TEXT UNIQUE) - Placa
- color (TEXT) - Cor
- fuel_type (TEXT) - Tipo de combustível
- engine_size (TEXT) - Tamanho do motor
- mileage (INTEGER) - Quilometragem atual
- purchase_date (DATE) - Data de compra
- purchase_price (DECIMAL) - Preço de compra
- current_value (DECIMAL) - Valor atual
- insurance_company (TEXT) - Seguradora
- insurance_policy (TEXT) - Número da apólice
- insurance_expiry (DATE) - Vencimento do seguro
- notes (TEXT) - Observações
- is_active (BOOLEAN) - Ativo/inativo
- created_at/updated_at (TIMESTAMP)
```

### Tabela `vehicle_maintenance`
```sql
- id (INTEGER PRIMARY KEY)
- user_id (INTEGER) - FK para users
- vehicle_id (INTEGER) - FK para vehicles
- maintenance_type (TEXT) - Tipo de manutenção
- description (TEXT) - Descrição do serviço
- service_date (DATE) - Data do serviço
- mileage_at_service (INTEGER) - Km no serviço
- cost (DECIMAL) - Custo do serviço
- service_provider (TEXT) - Prestador
- next_service_date (DATE) - Próximo serviço
- next_service_mileage (INTEGER) - Próxima km
- warranty_period (INTEGER) - Garantia em meses
- warranty_expiry (DATE) - Vencimento garantia
- receipt_number (TEXT) - Número do recibo
- notes (TEXT) - Observações
- is_scheduled (BOOLEAN) - Agendado
- is_completed (BOOLEAN) - Concluído
- created_at/updated_at (TIMESTAMP)
```

### Tabela `fuel_records`
```sql
- id (INTEGER PRIMARY KEY)
- user_id (INTEGER) - FK para users
- vehicle_id (INTEGER) - FK para vehicles
- fuel_date (DATE) - Data do abastecimento
- fuel_type (TEXT) - Tipo de combustível
- liters (DECIMAL) - Quantidade em litros
- price_per_liter (DECIMAL) - Preço por litro
- total_cost (DECIMAL) - Custo total
- mileage (INTEGER) - Quilometragem
- gas_station (TEXT) - Posto de gasolina
- location (TEXT) - Localização
- is_full_tank (BOOLEAN) - Tanque cheio
- fuel_efficiency (DECIMAL) - Eficiência calculada
- notes (TEXT) - Observações
- receipt_number (TEXT) - Número do recibo
- created_at/updated_at (TIMESTAMP)
```

## 🎯 Próximos Passos (Futuras Melhorias)

- [ ] Relatórios gráficos de consumo
- [ ] Alertas de manutenção por email/notificação
- [ ] Importação/exportação de dados
- [ ] Comparação de eficiência entre veículos
- [ ] Controle de documentos (IPVA, licenciamento)
- [ ] Integração com APIs de preços de combustível

## 🐛 Problemas Conhecidos

- Algumas funcionalidades de edição/exclusão ainda estão em desenvolvimento
- Filtros nas abas de manutenção e combustível precisam ser implementados
- Relatórios de veículos ainda não foram implementados

## 📞 Suporte

Se encontrar algum problema:
1. Verifique se todas as dependências estão instaladas
2. Execute o script de teste para verificar o banco
3. Consulte os logs de erro na aplicação

---

**Versão 2.0** - Sistema de Gestão de Contas com Gerenciamento de Veículos
*Desenvolvido com Python + Tkinter + SQLite*
