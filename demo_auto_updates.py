#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Demonstração do Sistema de Atualizações Automáticas
Mostra as funcionalidades em ação com dados de exemplo
"""

import sys
import time
import threading
from datetime import datetime, date, timedelta

# Adicionar src ao path
sys.path.append('src')

from database import DatabaseManager
from auth import AuthManager
from modules.auto_update_manager import AutoUpdateManager

class AutoUpdateDemo:
    def __init__(self):
        self.db_manager = None
        self.auto_update_manager = None
        self.demo_running = False
        
    def setup_demo_environment(self):
        """Configura ambiente de demonstração"""
        print("🔧 Configurando ambiente de demonstração...")
        
        # Inicializar banco de dados
        self.db_manager = DatabaseManager("data/demo_auto_updates.db")
        self.db_manager.initialize_database()
        
        # Criar usuário de demonstração
        auth_manager = AuthManager(self.db_manager)
        if not auth_manager.user_exists('demo_user'):
            auth_manager.create_user(
                username='demo_user',
                password='demo123',
                email='<EMAIL>',
                full_name='Usuário Demonstração'
            )
        
        print("   ✓ Banco de dados configurado")
        print("   ✓ Usuário de demonstração criado")
        
        # Criar dados de exemplo
        self.create_sample_data()
        
        # Inicializar sistema de atualizações
        self.auto_update_manager = AutoUpdateManager(
            self.db_manager, 
            "config/demo_auto_update_config.json"
        )
        
        print("   ✓ Sistema de atualizações inicializado")
    
    def create_sample_data(self):
        """Cria dados de exemplo para demonstração"""
        print("📝 Criando dados de exemplo...")
        
        try:
            # Criar veículo de exemplo
            vehicle_data = {
                'name': 'Honda Civic Demo',
                'brand': 'Honda',
                'model': 'Civic',
                'year': 2022,
                'license_plate': 'DEMO-001',
                'fuel_type': 'gasoline',
                'mileage': 25000,
                'insurance_expiry': (date.today() + timedelta(days=15)).isoformat()  # Vence em 15 dias
            }
            
            vehicle_id = self.db_manager.add_vehicle(1, vehicle_data)
            print(f"   ✓ Veículo criado (ID: {vehicle_id})")
            
            # Criar registros de combustível (alguns sem eficiência calculada)
            fuel_records = [
                {
                    'vehicle_id': vehicle_id,
                    'fuel_date': (date.today() - timedelta(days=20)).isoformat(),
                    'fuel_type': 'gasoline',
                    'liters': 40.0,
                    'price_per_liter': 5.50,
                    'total_cost': 220.00,
                    'mileage': 24500,
                    'is_full_tank': True
                },
                {
                    'vehicle_id': vehicle_id,
                    'fuel_date': (date.today() - timedelta(days=10)).isoformat(),
                    'fuel_type': 'gasoline',
                    'liters': 35.0,
                    'price_per_liter': 5.60,
                    'total_cost': 196.00,
                    'mileage': 25000,
                    'is_full_tank': True
                }
            ]
            
            for fuel_data in fuel_records:
                # Inserir sem calcular eficiência para demonstrar atualização automática
                query = '''
                    INSERT INTO fuel_records (
                        user_id, vehicle_id, fuel_date, fuel_type, liters,
                        price_per_liter, total_cost, mileage, is_full_tank
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
                '''
                params = (
                    1, fuel_data['vehicle_id'], fuel_data['fuel_date'],
                    fuel_data['fuel_type'], fuel_data['liters'], fuel_data['price_per_liter'],
                    fuel_data['total_cost'], fuel_data['mileage'], fuel_data['is_full_tank']
                )
                self.db_manager.execute_query(query, params)
            
            print(f"   ✓ {len(fuel_records)} registros de combustível criados")
            
            # Criar manutenção vencida
            maintenance_data = {
                'vehicle_id': vehicle_id,
                'maintenance_type': 'oil_change',
                'description': 'Troca de óleo e filtro',
                'service_date': (date.today() - timedelta(days=180)).isoformat(),
                'cost': 150.00,
                'next_service_date': (date.today() - timedelta(days=5)).isoformat(),  # Vencida há 5 dias
                'next_service_mileage': 25000,  # Quilometragem já atingida
                'is_completed': True
            }
            
            self.db_manager.add_maintenance_record(1, maintenance_data)
            print("   ✓ Manutenção vencida criada")
            
            # Criar carteira e transações
            wallet_data = {
                'name': 'Carteira Demo',
                'initial_balance': 1000.00,
                'current_balance': 1000.00,
                'description': 'Carteira para demonstração'
            }
            
            wallet_id = self.db_manager.add_wallet(1, wallet_data)
            
            # Criar categoria
            category_data = {
                'name': 'Combustível Demo',
                'category_type': 'expense',
                'color': '#e74c3c',
                'description': 'Gastos com combustível'
            }
            
            category_id = self.db_manager.add_category(1, category_data)
            
            # Criar transação vencida
            transaction_data = {
                'wallet_id': wallet_id,
                'category_id': category_id,
                'transaction_type': 'expense',
                'amount': 200.00,
                'description': 'Conta de luz',
                'transaction_date': (date.today() - timedelta(days=10)).isoformat(),
                'due_date': (date.today() - timedelta(days=3)).isoformat(),  # Vencida há 3 dias
                'is_paid': False
            }
            
            self.db_manager.add_transaction(1, transaction_data)
            print("   ✓ Transação vencida criada")
            
        except Exception as e:
            print(f"   ✗ Erro ao criar dados: {e}")
    
    def create_demo_config(self):
        """Cria configuração otimizada para demonstração"""
        import json
        from pathlib import Path
        
        demo_config = {
            "enabled": True,
            "update_interval_seconds": 3,  # Muito rápido para demo
            "tasks": {
                "fuel_efficiency_update": {
                    "enabled": True,
                    "interval_minutes": 1,  # 1 minuto
                    "description": "Atualiza cálculos de eficiência de combustível"
                },
                "maintenance_reminders": {
                    "enabled": True,
                    "interval_minutes": 1,  # 1 minuto
                    "description": "Verifica lembretes de manutenção"
                },
                "insurance_expiry_check": {
                    "enabled": True,
                    "interval_minutes": 2,  # 2 minutos
                    "description": "Verifica vencimento de seguros"
                },
                "financial_calculations": {
                    "enabled": True,
                    "interval_minutes": 1,  # 1 minuto
                    "description": "Atualiza cálculos financeiros"
                },
                "data_cleanup": {
                    "enabled": False,  # Desabilitado para demo
                    "interval_minutes": 60,
                    "description": "Limpeza de dados antigos"
                }
            }
        }
        
        # Criar diretório config se não existir
        config_dir = Path("config")
        config_dir.mkdir(exist_ok=True)
        
        # Salvar configuração
        with open("config/demo_auto_update_config.json", 'w', encoding='utf-8') as f:
            json.dump(demo_config, f, indent=2, ensure_ascii=False)
    
    def run_demo(self):
        """Executa a demonstração"""
        print("\n" + "="*60)
        print("    🚀 DEMONSTRAÇÃO DE ATUALIZAÇÕES AUTOMÁTICAS")
        print("="*60)
        
        try:
            # Configurar ambiente
            self.create_demo_config()
            self.setup_demo_environment()
            
            print("\n📊 Status inicial dos dados:")
            self.show_data_status()
            
            print("\n🚀 Iniciando sistema de atualizações automáticas...")
            self.auto_update_manager.start()
            self.demo_running = True
            
            # Monitorar execução por 30 segundos
            print("⏱️  Monitorando execução por 30 segundos...")
            print("   (Observe as atualizações em tempo real)\n")
            
            start_time = time.time()
            last_status_time = 0
            
            while time.time() - start_time < 30 and self.demo_running:
                current_time = time.time()
                
                # Mostrar status a cada 5 segundos
                if current_time - last_status_time >= 5:
                    elapsed = int(current_time - start_time)
                    print(f"\n⏰ Tempo decorrido: {elapsed}s")
                    self.show_task_status()
                    last_status_time = current_time
                
                time.sleep(1)
            
            print("\n📊 Status final dos dados:")
            self.show_data_status()
            
            print("\n🛑 Parando sistema...")
            self.auto_update_manager.stop()
            
            print("\n✅ Demonstração concluída!")
            print("\n💡 Principais funcionalidades demonstradas:")
            print("   ✓ Cálculo automático de eficiência de combustível")
            print("   ✓ Detecção de manutenções vencidas")
            print("   ✓ Alertas de vencimento de seguro")
            print("   ✓ Verificação de contas vencidas")
            print("   ✓ Atualização de saldos financeiros")
            
        except KeyboardInterrupt:
            print("\n\n⚠️  Demonstração interrompida pelo usuário")
            self.demo_running = False
            if self.auto_update_manager:
                self.auto_update_manager.stop()
        
        except Exception as e:
            print(f"\n❌ Erro durante demonstração: {e}")
            import traceback
            traceback.print_exc()
    
    def show_data_status(self):
        """Mostra status atual dos dados"""
        try:
            # Verificar eficiência de combustível
            fuel_query = '''
                SELECT COUNT(*) as total, 
                       COUNT(fuel_efficiency) as with_efficiency
                FROM fuel_records WHERE user_id = 1
            '''
            fuel_result = self.db_manager.execute_query(fuel_query)
            if fuel_result:
                total = fuel_result[0]['total']
                with_eff = fuel_result[0]['with_efficiency']
                print(f"   🔋 Combustível: {with_eff}/{total} registros com eficiência calculada")
            
            # Verificar alertas de manutenção
            alerts = self.db_manager.get_maintenance_alerts(1, 30)
            print(f"   🔧 Manutenção: {len(alerts)} alertas encontrados")
            
            # Verificar seguros vencendo
            insurance_alerts = self.db_manager.get_insurance_expiry_alerts(1, 30)
            print(f"   🛡️  Seguros: {len(insurance_alerts)} alertas de vencimento")
            
            # Verificar contas vencidas
            overdue_query = '''
                SELECT COUNT(*) as overdue_count, COALESCE(SUM(amount), 0) as overdue_total
                FROM transactions 
                WHERE user_id = 1 AND is_paid = 0 AND due_date < date('now')
            '''
            overdue_result = self.db_manager.execute_query(overdue_query)
            if overdue_result:
                count = overdue_result[0]['overdue_count']
                total = overdue_result[0]['overdue_total']
                print(f"   💰 Financeiro: {count} contas vencidas (R$ {total:.2f})")
            
        except Exception as e:
            print(f"   ✗ Erro ao verificar status: {e}")
    
    def show_task_status(self):
        """Mostra status das tarefas"""
        try:
            status = self.auto_update_manager.get_task_status()
            
            print("   📋 Status das tarefas:")
            for task_name, task_info in status['tasks'].items():
                if not task_info['enabled']:
                    continue
                
                last_run = task_info['last_run']
                if last_run:
                    last_run_time = last_run.split('T')[1][:8]
                    print(f"      ✓ {task_name}: {last_run_time}")
                else:
                    print(f"      ⏳ {task_name}: Aguardando...")
        
        except Exception as e:
            print(f"   ✗ Erro ao verificar status das tarefas: {e}")

def main():
    """Função principal"""
    demo = AutoUpdateDemo()
    demo.run_demo()

if __name__ == "__main__":
    main()
