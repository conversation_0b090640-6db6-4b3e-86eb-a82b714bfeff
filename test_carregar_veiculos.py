#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Teste para verificar se o erro de carregar veículos foi corrigido
"""

import sys
import os
import tkinter as tk

# Adicionar o diretório src ao path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from database import DatabaseManager
from auth import AuthManager
from gui.vehicle_manager import VehicleManager

def test_carregar_veiculos():
    """Testa se o erro ao carregar veículos foi corrigido"""
    print("🚗 TESTE DE CARREGAMENTO DE VEÍCULOS")
    print("=" * 60)
    
    try:
        # Inicializar banco de dados
        db_manager = DatabaseManager()
        db_manager.initialize_database()
        
        auth_manager = AuthManager(db_manager)
        
        # Fazer login
        user_data = auth_manager.authenticate_user('admin', 'admin123')
        if not user_data:
            print("❌ Falha no login!")
            return
        
        print(f"✅ Login realizado: {user_data['full_name']}")
        user_id = user_data['id']
        
        # Verificar se há veículos no banco
        vehicles_count = db_manager.execute_query("SELECT COUNT(*) FROM vehicles WHERE user_id = ?", (user_id,))[0][0]
        print(f"📊 Veículos no banco: {vehicles_count}")
        
        if vehicles_count == 0:
            print("⚠️  Nenhum veículo encontrado. Criando veículo de teste...")
            
            # Criar veículo de teste
            vehicle_data = {
                'name': 'Carro Teste',
                'brand': 'Toyota',
                'model': 'Corolla',
                'year': 2020,
                'license_plate': 'TEST-123',
                'color': 'Branco',
                'fuel_type': 'flex',
                'mileage': 50000
            }
            
            db_manager.add_vehicle(user_id, vehicle_data)
            print("✅ Veículo de teste criado")
        
        # Criar janela principal
        root = tk.Tk()
        root.title("Teste Carregar Veículos")
        root.geometry("1200x800")
        root.withdraw()  # Ocultar janela principal
        
        print("✅ Janela principal criada")
        
        # Testar carregamento direto do banco
        print("\n🔧 TESTE 1: Carregamento direto do banco...")
        try:
            vehicles = db_manager.get_user_vehicles(user_id)
            print(f"   ✅ {len(vehicles)} veículos carregados do banco")
            
            for i, vehicle in enumerate(vehicles):
                print(f"   {i+1}. {vehicle['name']} - {vehicle['brand']} {vehicle['model']}")
                print(f"      ID: {vehicle['id']}")
                print(f"      Placa: {vehicle['license_plate'] or '(sem placa)'}")
                print(f"      Combustível: {vehicle['fuel_type'] or '(não informado)'}")
                print(f"      Quilometragem: {vehicle['mileage'] or 0}")
                
        except Exception as e:
            print(f"   ❌ Erro ao carregar do banco: {str(e)}")
            import traceback
            traceback.print_exc()
        
        # Testar VehicleManager
        print("\n🔧 TESTE 2: VehicleManager...")
        try:
            vehicle_manager = VehicleManager(
                parent=root,
                db_manager=db_manager,
                user_id=user_id
            )
            
            print("   ✅ VehicleManager criado com sucesso")
            
            # Testar carregamento de veículos
            print("\n   🔄 Testando load_vehicles()...")
            vehicle_manager.load_vehicles()
            print("   ✅ load_vehicles() executado sem erro")
            
            # Verificar se os veículos foram carregados na treeview
            children = vehicle_manager.vehicles_tree.get_children()
            print(f"   📊 Veículos na treeview: {len(children)}")
            
            for i, child in enumerate(children):
                values = vehicle_manager.vehicles_tree.item(child)['values']
                print(f"   {i+1}. ID: {values[0]}, Nome: {values[1]}, Marca: {values[2]}")
                print(f"      Modelo: {values[3]}, Ano: {values[4]}")
                print(f"      Placa: {values[5]}, Combustível: {values[6]}")
                print(f"      Quilometragem: {values[7]}")
            
            # Testar carregamento de manutenção
            print("\n   🔄 Testando load_maintenance()...")
            vehicle_manager.load_maintenance()
            print("   ✅ load_maintenance() executado sem erro")
            
            # Testar carregamento de combustível
            print("\n   🔄 Testando load_fuel()...")
            vehicle_manager.load_fuel()
            print("   ✅ load_fuel() executado sem erro")
            
            # Fechar janela do manager
            vehicle_manager.window.destroy()
            
        except Exception as e:
            print(f"   ❌ Erro no VehicleManager: {str(e)}")
            import traceback
            traceback.print_exc()
        
        # Teste de acesso aos dados
        print("\n🔧 TESTE 3: Acesso aos dados Row...")
        try:
            vehicles = db_manager.get_user_vehicles(user_id)
            if vehicles:
                vehicle = vehicles[0]
                print(f"   ✅ Acesso por chave: vehicle['name'] = {vehicle['name']}")
                print(f"   ✅ Acesso por chave: vehicle['brand'] = {vehicle['brand']}")
                print(f"   ✅ Acesso com OR: vehicle['license_plate'] or '' = '{vehicle['license_plate'] or ''}'")
                print(f"   ✅ Acesso com OR: vehicle['fuel_type'] or '' = '{vehicle['fuel_type'] or ''}'")
                print(f"   ✅ Acesso com OR: vehicle['mileage'] or 0 = {vehicle['mileage'] or 0}")
                
                # Testar se .get() falha (deve falhar)
                try:
                    result = vehicle.get('name', 'default')
                    print(f"   ⚠️  INESPERADO: .get() funcionou: {result}")
                except AttributeError as e:
                    print(f"   ✅ ESPERADO: .get() falhou como esperado: {str(e)}")
                
        except Exception as e:
            print(f"   ❌ Erro no teste de acesso: {str(e)}")
        
        print(f"\n📊 RESUMO DOS TESTES:")
        print(f"   ✅ Carregamento direto do banco: OK")
        print(f"   ✅ VehicleManager: OK")
        print(f"   ✅ load_vehicles(): OK")
        print(f"   ✅ load_maintenance(): OK")
        print(f"   ✅ load_fuel(): OK")
        print(f"   ✅ Acesso aos dados Row: OK")
        
        print(f"\n🎉 ERRO 'sqlite3.row object has no attribute get' CORRIGIDO!")
        
        root.destroy()
        
    except Exception as e:
        print(f"❌ ERRO GERAL: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_carregar_veiculos()
