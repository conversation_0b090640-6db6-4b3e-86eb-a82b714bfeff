#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Script para testar as novas tabelas de veículos
"""

import sqlite3
from src.database import DatabaseManager

def test_vehicle_tables():
    """Testa se as tabelas de veículos foram criadas corretamente"""
    
    # Inicializar banco de dados
    db_manager = DatabaseManager()
    db_manager.initialize_database()
    
    # Conectar ao banco
    conn = db_manager.get_connection()
    cursor = conn.cursor()
    
    print("=== Testando Tabelas de Veículos ===\n")
    
    # Verificar se as tabelas existem
    tables_to_check = ['vehicles', 'vehicle_maintenance', 'fuel_records']
    
    for table in tables_to_check:
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name=?", (table,))
        result = cursor.fetchone()
        
        if result:
            print(f"✓ Tabela '{table}' existe")
            
            # Mostrar estrutura da tabela
            cursor.execute(f"PRAGMA table_info({table})")
            columns = cursor.fetchall()
            print(f"  Colunas ({len(columns)}):")
            for col in columns:
                print(f"    - {col[1]} ({col[2]})")
            print()
        else:
            print(f"✗ Tabela '{table}' NÃO existe")
    
    # Testar inserção de dados de exemplo
    print("=== Testando Inserção de Dados ===\n")
    
    try:
        # Dados de exemplo para veículo
        vehicle_data = {
            'name': 'Meu Carro',
            'brand': 'Toyota',
            'model': 'Corolla',
            'year': 2020,
            'license_plate': 'ABC-1234',
            'color': 'Prata',
            'fuel_type': 'flex',
            'engine_size': '1.8',
            'mileage': 50000,
            'purchase_date': '2020-01-15',
            'purchase_price': 65000.00,
            'current_value': 55000.00,
            'insurance_company': 'Seguradora XYZ',
            'insurance_policy': 'POL123456',
            'insurance_expiry': '2024-12-31',
            'notes': 'Veículo em excelente estado'
        }
        
        # Inserir veículo (assumindo user_id = 1)
        vehicle_id = db_manager.add_vehicle(1, vehicle_data)
        print(f"✓ Veículo inserido com ID: {vehicle_id}")
        
        # Dados de exemplo para manutenção
        maintenance_data = {
            'vehicle_id': 1,  # Assumindo que existe um veículo com ID 1
            'maintenance_type': 'oil_change',
            'description': 'Troca de óleo e filtro',
            'service_date': '2024-01-15',
            'mileage_at_service': 50000,
            'cost': 150.00,
            'service_provider': 'Oficina do João',
            'next_service_date': '2024-07-15',
            'next_service_mileage': 55000,
            'warranty_period': 6,
            'warranty_expiry': '2024-07-15',
            'receipt_number': 'REC001',
            'notes': 'Óleo sintético utilizado',
            'is_scheduled': False,
            'is_completed': True
        }
        
        maintenance_id = db_manager.add_maintenance_record(1, maintenance_data)
        print(f"✓ Manutenção inserida com ID: {maintenance_id}")
        
        # Dados de exemplo para combustível
        fuel_data = {
            'vehicle_id': 1,  # Assumindo que existe um veículo com ID 1
            'fuel_date': '2024-01-20',
            'fuel_type': 'gasoline',
            'liters': 45.5,
            'price_per_liter': 5.89,
            'total_cost': 268.00,
            'mileage': 50200,
            'gas_station': 'Posto Shell',
            'location': 'Centro da cidade',
            'is_full_tank': True,
            'notes': 'Tanque completo',
            'receipt_number': 'FUEL001'
        }
        
        fuel_id = db_manager.add_fuel_record(1, fuel_data)
        print(f"✓ Registro de combustível inserido com ID: {fuel_id}")
        
    except Exception as e:
        print(f"✗ Erro ao inserir dados: {str(e)}")
    
    # Testar consultas
    print("\n=== Testando Consultas ===\n")
    
    try:
        # Buscar veículos
        vehicles = db_manager.get_user_vehicles(1)
        print(f"✓ Encontrados {len(vehicles)} veículos")
        for vehicle in vehicles:
            print(f"  - {vehicle['name']}: {vehicle['brand']} {vehicle['model']} ({vehicle['year']})")
        
        # Buscar manutenções
        if vehicles:
            maintenance_records = db_manager.get_vehicle_maintenance(vehicles[0]['id'], 1)
            print(f"✓ Encontrados {len(maintenance_records)} registros de manutenção")
            for record in maintenance_records:
                print(f"  - {record['maintenance_type']}: {record['description']} (R$ {record['cost']})")
        
        # Buscar combustível
        if vehicles:
            fuel_records = db_manager.get_vehicle_fuel_records(vehicles[0]['id'], 1, limit=5)
            print(f"✓ Encontrados {len(fuel_records)} registros de combustível")
            for record in fuel_records:
                efficiency = f" - {record['fuel_efficiency']:.2f} km/l" if record['fuel_efficiency'] else ""
                print(f"  - {record['fuel_date']}: {record['liters']}L de {record['fuel_type']} (R$ {record['total_cost']:.2f}){efficiency}")
        
        # Testar estatísticas de combustível
        if vehicles:
            stats = db_manager.get_fuel_statistics(vehicles[0]['id'], 1)
            if stats and len(stats) > 0:
                stat = stats[0]
                print(f"✓ Estatísticas de combustível:")
                print(f"  - Total de registros: {stat['total_records']}")
                print(f"  - Total de litros: {stat['total_liters']:.2f}L")
                print(f"  - Custo total: R$ {stat['total_cost']:.2f}")
                if stat['avg_price_per_liter']:
                    print(f"  - Preço médio/litro: R$ {stat['avg_price_per_liter']:.3f}")
                if stat['avg_efficiency']:
                    print(f"  - Eficiência média: {stat['avg_efficiency']:.2f} km/l")
        
    except Exception as e:
        print(f"✗ Erro ao consultar dados: {str(e)}")
    
    conn.close()
    print("\n=== Teste Concluído ===")

if __name__ == "__main__":
    test_vehicle_tables()
