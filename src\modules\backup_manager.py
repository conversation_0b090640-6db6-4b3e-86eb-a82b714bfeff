#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Módulo de gerenciamento de backup e restauração
"""

import os
import shutil
import sqlite3
import json
import zipfile
from datetime import datetime
from pathlib import Path

class BackupManager:
    def __init__(self, db_manager):
        self.db_manager = db_manager
        self.backup_dir = Path("backups")
        self.backup_dir.mkdir(exist_ok=True)
    
    def create_full_backup(self, backup_path=None, include_logs=False):
        """Cria backup completo do sistema"""
        try:
            if backup_path is None:
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                backup_path = self.backup_dir / f"backup_completo_{timestamp}.zip"
            
            backup_path = Path(backup_path)
            
            # Criar arquivo ZIP
            with zipfile.ZipFile(backup_path, 'w', zipfile.ZIP_DEFLATED) as zipf:
                # Adicionar banco de dados
                db_path = Path(self.db_manager.db_path)
                if db_path.exists():
                    zipf.write(db_path, "database.db")
                
                # Criar arquivo de metadados
                metadata = {
                    'backup_date': datetime.now().isoformat(),
                    'backup_type': 'full',
                    'database_size': db_path.stat().st_size if db_path.exists() else 0,
                    'include_logs': include_logs,
                    'version': '1.0.0'
                }
                
                # Adicionar metadados ao ZIP
                zipf.writestr("metadata.json", json.dumps(metadata, indent=2))
                
                # Adicionar dados em formato JSON para compatibilidade
                self.export_data_to_zip(zipf, include_logs)
            
            return {
                'success': True,
                'backup_path': str(backup_path),
                'size': backup_path.stat().st_size,
                'timestamp': datetime.now().isoformat()
            }
            
        except Exception as e:
            raise Exception(f"Erro ao criar backup completo: {str(e)}")
    
    def export_data_to_zip(self, zipf, include_logs=False):
        """Exporta dados do banco para JSON dentro do ZIP"""
        try:
            conn = self.db_manager.get_connection()
            
            # Exportar usuários (sem senhas)
            cursor = conn.cursor()
            cursor.execute('''
                SELECT id, username, email, full_name, is_admin, is_active, created_at, last_login
                FROM users
            ''')
            users_data = [dict(row) for row in cursor.fetchall()]
            zipf.writestr("data/users.json", json.dumps(users_data, indent=2, default=str))
            
            # Exportar carteiras
            cursor.execute('SELECT * FROM wallets')
            wallets_data = [dict(row) for row in cursor.fetchall()]
            zipf.writestr("data/wallets.json", json.dumps(wallets_data, indent=2, default=str))
            
            # Exportar categorias
            cursor.execute('SELECT * FROM categories')
            categories_data = [dict(row) for row in cursor.fetchall()]
            zipf.writestr("data/categories.json", json.dumps(categories_data, indent=2, default=str))
            
            # Exportar transações
            cursor.execute('SELECT * FROM transactions')
            transactions_data = [dict(row) for row in cursor.fetchall()]
            zipf.writestr("data/transactions.json", json.dumps(transactions_data, indent=2, default=str))
            
            # Exportar configurações
            cursor.execute('SELECT * FROM system_settings')
            settings_data = [dict(row) for row in cursor.fetchall()]
            zipf.writestr("data/settings.json", json.dumps(settings_data, indent=2, default=str))
            
            # Exportar logs se solicitado
            if include_logs:
                cursor.execute('SELECT * FROM system_logs ORDER BY created_at DESC LIMIT 1000')
                logs_data = [dict(row) for row in cursor.fetchall()]
                zipf.writestr("data/logs.json", json.dumps(logs_data, indent=2, default=str))
            
            conn.close()
            
        except Exception as e:
            raise Exception(f"Erro ao exportar dados: {str(e)}")
    
    def create_database_backup(self, backup_path=None):
        """Cria backup apenas do banco de dados"""
        try:
            if backup_path is None:
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                backup_path = self.backup_dir / f"database_backup_{timestamp}.db"
            
            backup_path = Path(backup_path)
            
            # Copiar arquivo do banco
            shutil.copy2(self.db_manager.db_path, backup_path)
            
            return {
                'success': True,
                'backup_path': str(backup_path),
                'size': backup_path.stat().st_size,
                'timestamp': datetime.now().isoformat()
            }
            
        except Exception as e:
            raise Exception(f"Erro ao criar backup do banco: {str(e)}")
    
    def restore_from_backup(self, backup_path, backup_type='auto'):
        """Restaura sistema a partir de backup"""
        try:
            backup_path = Path(backup_path)
            
            if not backup_path.exists():
                raise ValueError("Arquivo de backup não encontrado")
            
            # Criar backup de segurança antes da restauração
            safety_backup = self.create_database_backup()
            
            try:
                if backup_path.suffix.lower() == '.zip':
                    return self.restore_from_zip_backup(backup_path)
                elif backup_path.suffix.lower() == '.db':
                    return self.restore_from_database_backup(backup_path)
                else:
                    raise ValueError("Formato de backup não suportado")
                    
            except Exception as e:
                # Em caso de erro, tentar restaurar backup de segurança
                try:
                    self.restore_from_database_backup(safety_backup['backup_path'])
                except:
                    pass  # Se não conseguir restaurar, pelo menos não piorar
                raise e
            
        except Exception as e:
            raise Exception(f"Erro ao restaurar backup: {str(e)}")
    
    def restore_from_zip_backup(self, backup_path):
        """Restaura a partir de backup ZIP"""
        try:
            with zipfile.ZipFile(backup_path, 'r') as zipf:
                # Verificar se é um backup válido
                if 'metadata.json' not in zipf.namelist():
                    raise ValueError("Backup inválido: metadados não encontrados")
                
                # Ler metadados
                metadata_content = zipf.read('metadata.json').decode('utf-8')
                metadata = json.loads(metadata_content)
                
                # Extrair banco de dados
                if 'database.db' in zipf.namelist():
                    # Criar backup temporário
                    temp_db_path = Path("temp_restore.db")
                    with open(temp_db_path, 'wb') as f:
                        f.write(zipf.read('database.db'))
                    
                    # Substituir banco atual
                    shutil.move(temp_db_path, self.db_manager.db_path)
                else:
                    raise ValueError("Banco de dados não encontrado no backup")
            
            return {
                'success': True,
                'restored_from': str(backup_path),
                'backup_date': metadata.get('backup_date'),
                'timestamp': datetime.now().isoformat()
            }
            
        except Exception as e:
            raise Exception(f"Erro ao restaurar backup ZIP: {str(e)}")
    
    def restore_from_database_backup(self, backup_path):
        """Restaura a partir de backup do banco de dados"""
        try:
            backup_path = Path(backup_path)
            
            # Verificar se é um banco SQLite válido
            try:
                conn = sqlite3.connect(backup_path)
                conn.execute("SELECT name FROM sqlite_master WHERE type='table'")
                conn.close()
            except sqlite3.Error:
                raise ValueError("Arquivo não é um banco de dados SQLite válido")
            
            # Substituir banco atual
            shutil.copy2(backup_path, self.db_manager.db_path)
            
            return {
                'success': True,
                'restored_from': str(backup_path),
                'timestamp': datetime.now().isoformat()
            }
            
        except Exception as e:
            raise Exception(f"Erro ao restaurar backup do banco: {str(e)}")
    
    def list_backups(self):
        """Lista todos os backups disponíveis"""
        try:
            backups = []
            
            for backup_file in self.backup_dir.glob("*"):
                if backup_file.is_file():
                    stat = backup_file.stat()
                    
                    backup_info = {
                        'filename': backup_file.name,
                        'path': str(backup_file),
                        'size': stat.st_size,
                        'created': datetime.fromtimestamp(stat.st_ctime).isoformat(),
                        'modified': datetime.fromtimestamp(stat.st_mtime).isoformat(),
                        'type': 'full' if backup_file.suffix == '.zip' else 'database'
                    }
                    
                    # Tentar ler metadados se for ZIP
                    if backup_file.suffix.lower() == '.zip':
                        try:
                            with zipfile.ZipFile(backup_file, 'r') as zipf:
                                if 'metadata.json' in zipf.namelist():
                                    metadata_content = zipf.read('metadata.json').decode('utf-8')
                                    metadata = json.loads(metadata_content)
                                    backup_info['metadata'] = metadata
                        except:
                            pass  # Ignorar erros de leitura de metadados
                    
                    backups.append(backup_info)
            
            # Ordenar por data de criação (mais recente primeiro)
            backups.sort(key=lambda x: x['created'], reverse=True)
            
            return backups
            
        except Exception as e:
            raise Exception(f"Erro ao listar backups: {str(e)}")
    
    def delete_backup(self, backup_path):
        """Exclui arquivo de backup"""
        try:
            backup_path = Path(backup_path)
            
            if not backup_path.exists():
                raise ValueError("Arquivo de backup não encontrado")
            
            # Verificar se está no diretório de backups
            if not str(backup_path).startswith(str(self.backup_dir)):
                raise ValueError("Apenas backups no diretório padrão podem ser excluídos")
            
            backup_path.unlink()
            
            return {
                'success': True,
                'deleted_file': str(backup_path),
                'timestamp': datetime.now().isoformat()
            }
            
        except Exception as e:
            raise Exception(f"Erro ao excluir backup: {str(e)}")
    
    def cleanup_old_backups(self, keep_count=10):
        """Remove backups antigos, mantendo apenas os mais recentes"""
        try:
            backups = self.list_backups()
            
            if len(backups) <= keep_count:
                return {'deleted_count': 0, 'message': 'Nenhum backup antigo para remover'}
            
            # Remover backups mais antigos
            backups_to_delete = backups[keep_count:]
            deleted_count = 0
            
            for backup in backups_to_delete:
                try:
                    self.delete_backup(backup['path'])
                    deleted_count += 1
                except:
                    continue  # Ignorar erros individuais
            
            return {
                'deleted_count': deleted_count,
                'kept_count': len(backups) - deleted_count,
                'timestamp': datetime.now().isoformat()
            }
            
        except Exception as e:
            raise Exception(f"Erro ao limpar backups antigos: {str(e)}")
    
    def verify_backup_integrity(self, backup_path):
        """Verifica integridade de um backup"""
        try:
            backup_path = Path(backup_path)
            
            if not backup_path.exists():
                raise ValueError("Arquivo de backup não encontrado")
            
            if backup_path.suffix.lower() == '.zip':
                return self.verify_zip_backup(backup_path)
            elif backup_path.suffix.lower() == '.db':
                return self.verify_database_backup(backup_path)
            else:
                raise ValueError("Formato de backup não suportado")
                
        except Exception as e:
            raise Exception(f"Erro ao verificar integridade: {str(e)}")
    
    def verify_zip_backup(self, backup_path):
        """Verifica integridade de backup ZIP"""
        try:
            with zipfile.ZipFile(backup_path, 'r') as zipf:
                # Testar integridade do ZIP
                bad_files = zipf.testzip()
                if bad_files:
                    return {'valid': False, 'error': f'Arquivos corrompidos: {bad_files}'}
                
                # Verificar arquivos essenciais
                required_files = ['metadata.json', 'database.db']
                missing_files = [f for f in required_files if f not in zipf.namelist()]
                
                if missing_files:
                    return {'valid': False, 'error': f'Arquivos ausentes: {missing_files}'}
                
                # Verificar metadados
                metadata_content = zipf.read('metadata.json').decode('utf-8')
                metadata = json.loads(metadata_content)
                
                return {
                    'valid': True,
                    'metadata': metadata,
                    'files': zipf.namelist(),
                    'size': backup_path.stat().st_size
                }
                
        except Exception as e:
            return {'valid': False, 'error': str(e)}
    
    def verify_database_backup(self, backup_path):
        """Verifica integridade de backup do banco"""
        try:
            conn = sqlite3.connect(backup_path)
            
            # Verificar integridade do banco
            cursor = conn.cursor()
            cursor.execute("PRAGMA integrity_check")
            integrity_result = cursor.fetchone()[0]
            
            if integrity_result != 'ok':
                conn.close()
                return {'valid': False, 'error': f'Integridade comprometida: {integrity_result}'}
            
            # Verificar tabelas essenciais
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
            tables = [row[0] for row in cursor.fetchall()]
            
            required_tables = ['users', 'wallets', 'categories', 'transactions']
            missing_tables = [t for t in required_tables if t not in tables]
            
            conn.close()
            
            if missing_tables:
                return {'valid': False, 'error': f'Tabelas ausentes: {missing_tables}'}
            
            return {
                'valid': True,
                'tables': tables,
                'size': backup_path.stat().st_size
            }
            
        except Exception as e:
            return {'valid': False, 'error': str(e)}
