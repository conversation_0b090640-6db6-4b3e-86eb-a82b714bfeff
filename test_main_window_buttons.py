#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Teste dos botões da janela principal
"""

import sys
import os
import tkinter as tk
from tkinter import messagebox

# Adicionar o diretório src ao path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from database import DatabaseManager
from auth import AuthManager
from gui.main_window import MainWindow

def test_main_window_buttons():
    """Testa os botões da janela principal"""
    print("🧪 TESTE DOS BOTÕES DA JANELA PRINCIPAL")
    print("=" * 60)
    
    try:
        # Inicializar banco de dados
        db_manager = DatabaseManager()
        db_manager.initialize_database()
        
        auth_manager = AuthManager(db_manager)
        
        # Criar usuário admin se não existir
        if not auth_manager.user_exists('admin'):
            auth_manager.create_user(
                username='admin',
                password='admin123',
                email='<EMAIL>',
                is_admin=True,
                full_name='Administrador do Sistema'
            )
        
        # Fazer login
        user_data = auth_manager.authenticate_user('admin', 'admin123')
        if not user_data:
            print("❌ Falha no login!")
            return
        
        print(f"✅ Login realizado: {user_data['full_name']}")
        
        # Criar janela principal
        root = tk.Tk()
        root.title("Teste Janela Principal")
        root.geometry("800x600")
        
        # Função de logout mock
        def mock_logout():
            print("Logout chamado")
        
        # Criar MainWindow
        main_window = MainWindow(root, db_manager, auth_manager, user_data, mock_logout)
        
        print("✅ Janela principal criada com sucesso!")
        
        # Testar botão Nova Carteira
        print("\n🔧 Testando botão 'Nova Carteira'...")
        try:
            main_window.new_wallet()
            print("✅ Botão 'Nova Carteira' funcionou!")
        except Exception as e:
            print(f"❌ Erro no botão 'Nova Carteira': {str(e)}")
        
        # Testar botão Nova Receita
        print("\n🔧 Testando botão 'Nova Receita'...")
        try:
            main_window.new_income()
            print("✅ Botão 'Nova Receita' funcionou!")
        except Exception as e:
            print(f"❌ Erro no botão 'Nova Receita': {str(e)}")
        
        # Testar botão Nova Despesa
        print("\n🔧 Testando botão 'Nova Despesa'...")
        try:
            main_window.new_expense()
            print("✅ Botão 'Nova Despesa' funcionou!")
        except Exception as e:
            print(f"❌ Erro no botão 'Nova Despesa': {str(e)}")
        
        print("\n✅ TODOS OS TESTES CONCLUÍDOS!")
        print("💡 Se não houve erros, os formulários devem estar funcionando.")
        
        # Fechar janela após um tempo
        root.after(2000, root.destroy)  # Fechar após 2 segundos
        root.mainloop()
        
    except Exception as e:
        print(f"❌ ERRO GERAL: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_main_window_buttons()
