#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Teste simples do formulário de transação
"""

import sys
import os
import tkinter as tk

# Adicionar o diretório src ao path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from database import DatabaseManager

def test_transaction_form_simple():
    """Teste simples do formulário de transação"""
    print("🧪 TESTE SIMPLES DO FORMULÁRIO DE TRANSAÇÃO")
    print("=" * 60)
    
    try:
        # Inicializar banco de dados
        db_manager = DatabaseManager()
        
        # Verificar usuário admin
        users = db_manager.execute_query("SELECT id FROM users WHERE username = 'admin'")
        if not users:
            print("❌ Usuário admin não encontrado!")
            return
        
        user_id = users[0]['id']
        print(f"✅ Usuário admin encontrado (ID: {user_id})")
        
        # Criar janela principal
        root = tk.Tk()
        root.title("Teste Formulário Transação")
        root.geometry("300x200")
        
        print("🔧 Importando TransactionFormNew...")
        
        # Tentar importar
        from gui.transaction_form_new import TransactionFormNew
        print("✅ Import bem-sucedido!")
        
        print("🔧 Criando formulário de receita...")
        
        # Criar formulário
        form = TransactionFormNew(root, db_manager, user_id, 'income')
        print("✅ Formulário criado!")
        
        # Verificar se a janela foi criada
        if hasattr(form, 'window') and form.window:
            print("✅ Janela do formulário criada!")
            
            # Fechar automaticamente após 3 segundos
            def close_form():
                if form.window and form.window.winfo_exists():
                    form.window.destroy()
                root.quit()
            
            root.after(3000, close_form)
            
            print("⏳ Aguardando 3 segundos...")
            root.mainloop()
            
            print("✅ Teste concluído!")
        else:
            print("❌ Janela do formulário não foi criada!")
        
    except Exception as e:
        print(f"❌ ERRO: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_transaction_form_simple()
