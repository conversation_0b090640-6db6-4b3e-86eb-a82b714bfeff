#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Teste das funcionalidades de editar e excluir transações
"""

import sys
import os
import tkinter as tk

# Adicionar o diretório src ao path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from database import DatabaseManager
from auth import AuthManager
from gui.main_window import MainWindow

def test_transaction_operations():
    """Testa as operações de transações"""
    print("🧪 TESTE DAS OPERAÇÕES DE TRANSAÇÕES")
    print("=" * 70)
    
    try:
        # Inicializar banco de dados
        db_manager = DatabaseManager()
        db_manager.initialize_database()
        
        auth_manager = AuthManager(db_manager)
        
        # Fazer login
        user_data = auth_manager.authenticate_user('admin', 'admin123')
        if not user_data:
            print("❌ Falha no login!")
            return
        
        print(f"✅ Login realizado: {user_data['full_name']}")
        
        # Criar janela principal
        root = tk.Tk()
        root.title("Teste Transações")
        root.geometry("800x600")
        
        # Função de logout mock
        def mock_logout():
            print("Logout chamado")
        
        # Criar MainWindow
        main_window = MainWindow(root, db_manager, auth_manager, user_data, mock_logout)
        
        print("✅ Janela principal criada com sucesso!")
        
        # Verificar se há transações
        print("\n📊 Verificando transações existentes...")
        try:
            query = "SELECT COUNT(*) FROM transactions WHERE user_id = ?"
            count = db_manager.execute_query(query, (user_data['id'],))[0][0]
            print(f"✅ Encontradas {count} transações")
            
            if count == 0:
                print("⚠️  Nenhuma transação encontrada. Criando uma transação de teste...")
                
                # Criar uma transação de teste
                from src.modules.transaction_manager import TransactionManager
                transaction_manager = TransactionManager(db_manager)
                
                # Buscar primeira carteira e categoria
                wallets = db_manager.execute_query("SELECT id FROM wallets WHERE user_id = ? LIMIT 1", (user_data['id'],))
                categories = db_manager.execute_query("SELECT id FROM categories WHERE category_type = 'expense' LIMIT 1")
                
                if wallets and categories:
                    transaction_manager.create_transaction(
                        user_id=user_data['id'],
                        wallet_id=wallets[0]['id'],
                        category_id=categories[0]['id'],
                        transaction_type='expense',
                        amount=100.00,
                        description='Transação de teste para edição',
                        transaction_date='2024-01-15',
                        is_paid=False
                    )
                    print("✅ Transação de teste criada!")
                
        except Exception as e:
            print(f"❌ Erro ao verificar transações: {str(e)}")
        
        # Testar carregamento de transações
        print("\n🔧 Testando carregamento de transações...")
        try:
            main_window.load_transactions()
            print("✅ Transações carregadas com sucesso!")
        except Exception as e:
            print(f"❌ Erro ao carregar transações: {str(e)}")
        
        # Testar funcionalidade de edição (sem seleção)
        print("\n🔧 Testando edição sem seleção...")
        try:
            main_window.edit_transaction()
            print("✅ Validação de seleção funcionou!")
        except Exception as e:
            print(f"❌ Erro na validação de edição: {str(e)}")
        
        # Testar funcionalidade de exclusão (sem seleção)
        print("\n🔧 Testando exclusão sem seleção...")
        try:
            main_window.delete_transaction()
            print("✅ Validação de seleção funcionou!")
        except Exception as e:
            print(f"❌ Erro na validação de exclusão: {str(e)}")
        
        # Simular seleção de transação
        print("\n🔧 Testando com transação selecionada...")
        try:
            # Verificar se há itens na árvore
            children = main_window.transactions_tree.get_children()
            if children:
                # Selecionar primeiro item
                main_window.transactions_tree.selection_set(children[0])
                print("✅ Transação selecionada para teste")
                
                # Testar edição com seleção
                print("🔧 Testando edição com seleção...")
                main_window.edit_transaction()
                print("✅ Edição de transação funcionou!")
                
            else:
                print("⚠️  Nenhuma transação na árvore para testar")
                
        except Exception as e:
            print(f"❌ Erro ao testar com seleção: {str(e)}")
        
        print("\n✅ TODOS OS TESTES CONCLUÍDOS!")
        print("💡 Se não houve erros, as funcionalidades de transação estão funcionando.")
        
        # Fechar janela após um tempo
        root.after(3000, root.destroy)  # Fechar após 3 segundos
        root.mainloop()
        
    except Exception as e:
        print(f"❌ ERRO GERAL: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_transaction_operations()
