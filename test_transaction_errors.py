#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Teste detalhado para identificar erros nas operações de transações
"""

import sys
import os
import tkinter as tk
import traceback

# Adicionar o diretório src ao path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from database import DatabaseManager
from auth import AuthManager
from gui.main_window import MainWindow

def test_detailed_transaction_operations():
    """Testa operações de transações com detalhes de erro"""
    print("🔍 TESTE DETALHADO DAS OPERAÇÕES DE TRANSAÇÕES")
    print("=" * 80)
    
    try:
        # Inicializar banco de dados
        db_manager = DatabaseManager()
        db_manager.initialize_database()
        
        auth_manager = AuthManager(db_manager)
        
        # Fazer login
        user_data = auth_manager.authenticate_user('admin', 'admin123')
        if not user_data:
            print("❌ Falha no login!")
            return
        
        print(f"✅ Login realizado: {user_data['full_name']}")
        
        # Criar janela principal
        root = tk.Tk()
        root.title("Teste Detalhado Transações")
        root.geometry("1200x800")
        
        # Função de logout mock
        def mock_logout():
            print("Logout chamado")
        
        # Criar MainWindow
        main_window = MainWindow(root, db_manager, auth_manager, user_data, mock_logout)
        
        print("✅ Janela principal criada com sucesso!")
        
        # Carregar transações
        print("\n📊 Carregando transações...")
        main_window.load_transactions()
        
        # Verificar se há transações na árvore
        children = main_window.transactions_tree.get_children()
        print(f"✅ Encontradas {len(children)} transações na árvore")
        
        if not children:
            print("⚠️  Criando transação de teste...")
            # Criar transação de teste
            from src.modules.transaction_manager import TransactionManager
            transaction_manager = TransactionManager(db_manager)
            
            # Buscar carteira e categoria
            wallets = db_manager.execute_query("SELECT id FROM wallets WHERE user_id = ? LIMIT 1", (user_data['id'],))
            categories = db_manager.execute_query("SELECT id FROM categories WHERE category_type = 'expense' LIMIT 1")
            
            if wallets and categories:
                transaction_manager.create_transaction(
                    user_id=user_data['id'],
                    wallet_id=wallets[0]['id'],
                    category_id=categories[0]['id'],
                    transaction_type='expense',
                    amount=150.00,
                    description='Transação de teste para debug',
                    transaction_date='2024-01-20',
                    is_paid=False
                )
                print("✅ Transação de teste criada!")
                main_window.load_transactions()
                children = main_window.transactions_tree.get_children()
        
        if children:
            # Selecionar primeira transação
            first_item = children[0]
            main_window.transactions_tree.selection_set(first_item)
            
            # Verificar tags do item
            tags = main_window.transactions_tree.item(first_item)['tags']
            values = main_window.transactions_tree.item(first_item)['values']
            
            print(f"📋 Item selecionado:")
            print(f"   Tags: {tags}")
            print(f"   Values: {values}")
            
            # Encontrar ID da transação
            transaction_id = None
            for tag in tags:
                if isinstance(tag, int):
                    transaction_id = tag
                    break
                elif isinstance(tag, str) and tag.isdigit():
                    transaction_id = int(tag)
                    break
            
            print(f"🔍 ID da transação identificado: {transaction_id}")
            
            # Teste 1: Editar transação
            print("\n🔧 TESTE 1: Editando transação...")
            try:
                main_window.edit_transaction()
                print("✅ Edição iniciada sem erros!")
            except Exception as e:
                print(f"❌ ERRO na edição: {str(e)}")
                print("📋 Traceback completo:")
                traceback.print_exc()
            
            # Aguardar um pouco
            root.update()
            
            # Teste 2: Excluir transação (com mock para evitar confirmação)
            print("\n🔧 TESTE 2: Testando exclusão...")
            try:
                # Mock da confirmação para evitar diálogo
                import tkinter.messagebox
                original_askyesno = tkinter.messagebox.askyesno
                tkinter.messagebox.askyesno = lambda title, message: False  # Simular "Não"
                
                main_window.delete_transaction()
                print("✅ Exclusão testada sem erros (cancelada)!")
                
                # Restaurar função original
                tkinter.messagebox.askyesno = original_askyesno
                
            except Exception as e:
                print(f"❌ ERRO na exclusão: {str(e)}")
                print("📋 Traceback completo:")
                traceback.print_exc()
                
                # Restaurar função original mesmo em caso de erro
                tkinter.messagebox.askyesno = original_askyesno
            
            # Teste 3: Verificar módulos necessários
            print("\n🔧 TESTE 3: Verificando módulos...")
            try:
                from src.modules.transaction_manager import TransactionManager
                from src.gui.transaction_form_new import TransactionFormNew
                print("✅ Módulos importados com sucesso!")
                
                # Testar TransactionManager
                tm = TransactionManager(db_manager)
                transaction_data = tm.get_transaction_by_id(transaction_id, user_data['id'])
                print(f"✅ Dados da transação obtidos: {transaction_data is not None}")
                
            except Exception as e:
                print(f"❌ ERRO nos módulos: {str(e)}")
                traceback.print_exc()
        
        else:
            print("❌ Nenhuma transação disponível para teste")
        
        print("\n✅ TESTE DETALHADO CONCLUÍDO!")
        
        # Fechar janela
        root.after(2000, root.destroy)
        root.mainloop()
        
    except Exception as e:
        print(f"❌ ERRO GERAL: {str(e)}")
        print("📋 Traceback completo:")
        traceback.print_exc()

if __name__ == "__main__":
    test_detailed_transaction_operations()
