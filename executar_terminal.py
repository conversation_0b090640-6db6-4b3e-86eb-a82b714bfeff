#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Execução via terminal - sem interface gráfica inicial
"""

import sys
import os
import time

def main():
    """Executa a aplicação diretamente"""
    
    print("=" * 70)
    print("           🚗 SISTEMA DE GESTÃO DE CONTAS")
    print("           Versão 2.0 - Com Gerenciamento de Veículos")
    print("=" * 70)
    print()
    
    print("📋 Funcionalidades disponíveis:")
    print("   ✓ Controle financeiro completo")
    print("   ✓ Gerenciamento de carteiras")
    print("   ✓ Transações (receitas e despesas)")
    print("   ✓ Relatórios financeiros")
    print("   ✓ NOVO: Cadastro de veículos")
    print("   ✓ NOVO: Controle de manutenção")
    print("   ✓ NOVO: Registro de combustível")
    print("   ✓ NOVO: Estatísticas de consumo")
    print()
    
    print("🔑 Credenciais padrão:")
    print("   Usuário: admin")
    print("   Senha: admin123")
    print()
    
    print("🚀 Iniciando aplicação...")
    print("-" * 70)
    
    try:
        # Verificar se os módulos existem
        print("📦 Verificando módulos...")
        
        try:
            import tkinter as tk
            print("   ✓ tkinter - OK")
        except ImportError:
            print("   ✗ tkinter - ERRO: Não encontrado")
            return
        
        try:
            import sqlite3
            print("   ✓ sqlite3 - OK")
        except ImportError:
            print("   ✗ sqlite3 - ERRO: Não encontrado")
            return
        
        # Verificar arquivos do projeto
        print("📁 Verificando arquivos...")
        
        arquivos = ['main.py', 'src/database.py', 'src/auth.py', 'src/gui/main_window.py', 'src/gui/login_window.py']
        
        for arquivo in arquivos:
            if os.path.exists(arquivo):
                print(f"   ✓ {arquivo} - OK")
            else:
                print(f"   ✗ {arquivo} - ERRO: Não encontrado")
                return
        
        print()
        print("🔄 Inicializando banco de dados...")
        
        # Importar e inicializar
        from src.database import DatabaseManager
        from src.auth import AuthManager
        from src.modules.auto_update_manager import AutoUpdateManager

        db_manager = DatabaseManager()
        db_manager.initialize_database()

        auth_manager = AuthManager(db_manager)

        # Inicializar sistema de atualizações automáticas
        auto_update_manager = None
        try:
            auto_update_manager = AutoUpdateManager(db_manager)
            print("   ✓ Sistema de atualizações automáticas inicializado")
        except Exception as e:
            print(f"   ⚠️ Erro ao inicializar atualizações automáticas: {e}")
        
        # Criar usuário admin se não existir
        if not auth_manager.user_exists('admin'):
            auth_manager.create_user(
                username='admin',
                password='admin123',
                email='<EMAIL>',
                is_admin=True,
                full_name='Administrador do Sistema'
            )
            print("   ✓ Usuário admin criado")
        else:
            print("   ✓ Usuário admin já existe")
        
        print()
        print("🖥️  Abrindo interface gráfica...")
        print("   Uma janela deve aparecer na sua tela!")
        print()
        
        # Criar interface gráfica
        root = tk.Tk()
        root.title("Sistema de Gestão de Contas")
        root.geometry("800x800")
        root.state('zoomed')  # Maximizar
        
        # Forçar janela aparecer
        root.lift()
        root.attributes('-topmost', True)
        root.focus_force()
        
        # Importar janelas
        from src.gui.login_window import LoginWindow
        from src.gui.main_window import MainWindow
        
        # Esconder janela principal inicialmente
        root.withdraw()
        
        def on_login_success(user_data):
            """Callback após login bem-sucedido"""
            print(f"✓ Login realizado: {user_data['full_name']}")

            # Iniciar sistema de atualizações automáticas
            if auto_update_manager:
                try:
                    auto_update_manager.start()
                    print("   ✓ Sistema de atualizações automáticas iniciado")
                except Exception as e:
                    print(f"   ⚠️ Erro ao iniciar atualizações automáticas: {e}")

            # Mostrar janela principal
            root.deiconify()
            root.attributes('-topmost', False)

            # Criar interface principal
            MainWindow(root, db_manager, auth_manager, user_data, on_logout, auto_update_manager)
        
        def on_logout():
            """Callback no logout"""
            print("✓ Logout realizado")

            # Parar sistema de atualizações automáticas
            if auto_update_manager:
                try:
                    auto_update_manager.stop()
                    print("   ✓ Sistema de atualizações automáticas parado")
                except Exception as e:
                    print(f"   ⚠️ Erro ao parar atualizações automáticas: {e}")

            root.withdraw()
            show_login()
        
        def show_login():
            """Mostra tela de login"""
            LoginWindow(root, auth_manager, on_login_success)
        
        # Mostrar login
        show_login()
        
        print("✅ Interface criada com sucesso!")
        print("✅ Procure a janela de login na sua tela")
        print()
        print("💡 Dicas:")
        print("   - Verifique a barra de tarefas")
        print("   - Use Alt+Tab para alternar janelas")
        print("   - A janela pode estar atrás de outras")
        print()
        print("🔄 Aguardando interação do usuário...")
        print("   (Para encerrar, pressione Ctrl+C)")
        
        # Iniciar loop principal
        root.mainloop()
        
        print("\n✅ Aplicação encerrada normalmente")
        
    except KeyboardInterrupt:
        print("\n\n⚠️  Aplicação interrompida pelo usuário")
        
    except Exception as e:
        print(f"\n❌ ERRO: {str(e)}")
        print("\n🔍 Detalhes do erro:")
        import traceback
        traceback.print_exc()
        
        print("\n💡 Possíveis soluções:")
        print("   1. Verifique se o Python está instalado corretamente")
        print("   2. Instale as dependências: py -m pip install -r requirements.txt")
        print("   3. Verifique se todos os arquivos estão presentes")
        
        input("\nPressione Enter para sair...")

if __name__ == "__main__":
    main()
