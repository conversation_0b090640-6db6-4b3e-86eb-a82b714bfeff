#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Script para criar executável da versão simplificada
"""

import subprocess
import sys
from pathlib import Path
import shutil
import zipfile

def build_simple_executable():
    """Constrói executável da versão simplificada"""
    print("Criando executável da versão simplificada...")
    
    try:
        # Comando PyInstaller mais simples
        cmd = [
            sys.executable, "-m", "PyInstaller",
            "--onefile",
            "--windowed", 
            "--name=GestaoContasSimples",
            "--add-data=data;data",
            "main_simple.py"
        ]
        
        print("Executando PyInstaller...")
        result = subprocess.run(cmd, capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✓ Executável criado com sucesso!")
            
            exe_path = Path("dist/GestaoContasSimples.exe")
            if exe_path.exists():
                size_mb = exe_path.stat().st_size / (1024 * 1024)
                print(f"✓ Arquivo: {exe_path}")
                print(f"✓ Tamanho: {size_mb:.1f} MB")
                
                # Criar pacote de distribuição
                create_simple_package()
                return True
            else:
                print("✗ Arquivo executável não encontrado")
                return False
        else:
            print("✗ Erro ao criar executável:")
            print(result.stderr)
            return False
            
    except Exception as e:
        print(f"✗ Erro: {str(e)}")
        return False

def create_simple_package():
    """Cria pacote simplificado"""
    print("\nCriando pacote de distribuição...")
    
    try:
        # Criar diretório
        dist_dir = Path("GestaoContasSimples_v1.0.0")
        if dist_dir.exists():
            shutil.rmtree(dist_dir)
        
        dist_dir.mkdir()
        
        # Copiar executável
        exe_source = Path("dist/GestaoContasSimples.exe")
        if exe_source.exists():
            shutil.copy2(exe_source, dist_dir / "GestaoContasSimples.exe")
            print("✓ Executável copiado")
        
        # Criar diretórios
        (dist_dir / "data").mkdir()
        (dist_dir / "backups").mkdir()
        print("✓ Diretórios criados")
        
        # Criar instruções
        instructions = """SISTEMA DE GESTÃO DE CONTAS - VERSÃO SIMPLIFICADA v1.0.0

COMO USAR:
1. Execute GestaoContasSimples.exe
2. Faça login com:
   - Usuário: admin
   - Senha: admin123

FUNCIONALIDADES:
✓ Login de usuários
✓ Gestão de carteiras
✓ Receitas e despesas
✓ Dashboard com saldo total
✓ Interface simples e intuitiva

ESTRUTURA:
- GestaoContasSimples.exe: Aplicação principal
- data/: Banco de dados (criado automaticamente)
- backups/: Para backups manuais

PROBLEMAS?
- Se der erro, delete a pasta 'data' e execute novamente
- O sistema criará um novo banco de dados limpo

DESENVOLVIDO EM PYTHON COM TKINTER
© 2024 - Sistema de Gestão Financeira
"""
        
        with open(dist_dir / "LEIA-ME.txt", 'w', encoding='utf-8') as f:
            f.write(instructions)
        
        print("✓ Instruções criadas")
        
        # Criar ZIP
        zip_path = "GestaoContasSimples_v1.0.0.zip"
        with zipfile.ZipFile(zip_path, 'w', zipfile.ZIP_DEFLATED) as zipf:
            for file_path in dist_dir.rglob('*'):
                if file_path.is_file():
                    arcname = file_path.relative_to(dist_dir.parent)
                    zipf.write(file_path, arcname)
        
        zip_size_mb = Path(zip_path).stat().st_size / (1024 * 1024)
        print(f"✓ Pacote ZIP criado: {zip_path} ({zip_size_mb:.1f} MB)")
        
        return True
        
    except Exception as e:
        print(f"✗ Erro ao criar pacote: {str(e)}")
        return False

def cleanup():
    """Limpa arquivos temporários"""
    print("\nLimpando arquivos temporários...")
    
    temp_items = ["build", "GestaoContasSimples.spec", "__pycache__"]
    
    for item in temp_items:
        path = Path(item)
        try:
            if path.is_file():
                path.unlink()
                print(f"✓ Removido: {item}")
            elif path.is_dir():
                shutil.rmtree(path)
                print(f"✓ Removido: {item}/")
        except:
            pass

def main():
    """Função principal"""
    print("Sistema de Gestão de Contas - Versão Simplificada")
    print("=" * 50)
    
    if build_simple_executable():
        cleanup()
        print("\n" + "=" * 50)
        print("✓ EXECUTÁVEL SIMPLIFICADO CRIADO COM SUCESSO!")
        print("\nArquivos gerados:")
        print("- dist/GestaoContasSimples.exe")
        print("- GestaoContasSimples_v1.0.0/")
        print("- GestaoContasSimples_v1.0.0.zip")
        print("\n🎯 Esta versão é mais estável e confiável!")
    else:
        print("\n✗ Falha na criação do executável")

if __name__ == "__main__":
    main()
