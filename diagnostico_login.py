#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Diagnóstico e correção de problemas de login
"""

import sys
import os
from src.database import DatabaseManager
from src.auth import AuthManager

def diagnosticar_login():
    """Diagnostica problemas de login"""
    
    print("=" * 60)
    print("    DIAGNÓSTICO DE LOGIN")
    print("=" * 60)
    print()
    
    try:
        # Inicializar componentes
        print("🔄 Inicializando componentes...")
        db_manager = DatabaseManager()
        db_manager.initialize_database()
        auth_manager = AuthManager(db_manager)
        print("   ✓ Componentes inicializados")
        
        # Verificar se usuário admin existe
        print("\n🔍 Verificando usuário admin...")
        
        query = "SELECT id, username, password_hash, email, full_name, is_admin, is_active FROM users WHERE username = 'admin'"
        result = db_manager.execute_query(query)
        
        if not result:
            print("   ❌ Usuário admin NÃO EXISTE!")
            print("   🔧 Criando usuário admin...")
            
            # Criar usuário admin
            success = auth_manager.create_user(
                username='admin',
                password='admin123',
                email='<EMAIL>',
                is_admin=True,
                full_name='Administrador do Sistema'
            )
            
            if success:
                print("   ✅ Usuário admin criado com sucesso!")
            else:
                print("   ❌ Erro ao criar usuário admin!")
                return
        else:
            user_data = dict(result[0])
            print(f"   ✅ Usuário admin existe:")
            print(f"      ID: {user_data['id']}")
            print(f"      Username: {user_data['username']}")
            print(f"      Email: {user_data['email']}")
            print(f"      Nome: {user_data['full_name']}")
            print(f"      Admin: {user_data['is_admin']}")
            print(f"      Ativo: {user_data['is_active']}")
        
        # Testar autenticação
        print("\n🔐 Testando autenticação...")
        
        # Teste 1: Credenciais corretas
        print("   Teste 1: admin / admin123")
        auth_result = auth_manager.authenticate_user('admin', 'admin123')
        
        if auth_result:
            print("   ✅ SUCESSO! Login funcionando corretamente")
            print(f"      Usuário logado: {auth_result['full_name']}")
        else:
            print("   ❌ FALHA! Login não funcionou")
            print("   🔧 Tentando corrigir...")
            
            # Recriar usuário admin com nova senha
            try:
                # Deletar usuário existente
                db_manager.execute_query("DELETE FROM users WHERE username = 'admin'")
                print("      ✓ Usuário admin removido")
                
                # Criar novo usuário
                success = auth_manager.create_user(
                    username='admin',
                    password='admin123',
                    email='<EMAIL>',
                    is_admin=True,
                    full_name='Administrador do Sistema'
                )
                
                if success:
                    print("      ✓ Novo usuário admin criado")
                    
                    # Testar novamente
                    auth_result = auth_manager.authenticate_user('admin', 'admin123')
                    if auth_result:
                        print("      ✅ Login corrigido com sucesso!")
                    else:
                        print("      ❌ Ainda há problemas com o login")
                else:
                    print("      ❌ Erro ao recriar usuário")
                    
            except Exception as e:
                print(f"      ❌ Erro na correção: {e}")
        
        # Teste 2: Credenciais incorretas
        print("\n   Teste 2: admin / senha_errada")
        auth_result = auth_manager.authenticate_user('admin', 'senha_errada')
        
        if auth_result:
            print("   ❌ PROBLEMA! Login aceitou senha incorreta")
        else:
            print("   ✅ OK! Login rejeitou senha incorreta")
        
        # Mostrar informações de hash
        print("\n🔍 Informações técnicas:")
        
        try:
            import bcrypt
            print("   ✓ bcrypt disponível - usando criptografia segura")
        except ImportError:
            print("   ⚠ bcrypt não disponível - usando hashlib (menos seguro)")
        
        # Verificar hash da senha
        query = "SELECT password_hash FROM users WHERE username = 'admin'"
        result = db_manager.execute_query(query)
        
        if result:
            hash_senha = result[0]['password_hash']
            print(f"   Hash da senha: {hash_senha[:20]}...")
            
            # Testar verificação de senha manualmente
            senha_correta = auth_manager.verify_password('admin123', hash_senha)
            print(f"   Verificação manual: {'✅ OK' if senha_correta else '❌ FALHA'}")
        
        print("\n" + "=" * 60)
        print("DIAGNÓSTICO CONCLUÍDO")
        print("=" * 60)
        
    except Exception as e:
        print(f"\n❌ ERRO no diagnóstico: {e}")
        import traceback
        traceback.print_exc()

def testar_login_interface():
    """Testa o login através da interface"""
    
    print("\n🖥️  TESTE DE LOGIN VIA INTERFACE")
    print("-" * 40)
    
    try:
        import tkinter as tk
        from tkinter import messagebox
        from src.gui.login_window import LoginWindow
        
        # Inicializar componentes
        db_manager = DatabaseManager()
        db_manager.initialize_database()
        auth_manager = AuthManager(db_manager)
        
        # Garantir que usuário admin existe
        if not auth_manager.user_exists('admin'):
            auth_manager.create_user(
                username='admin',
                password='admin123',
                email='<EMAIL>',
                is_admin=True,
                full_name='Administrador do Sistema'
            )
        
        # Criar janela de teste
        root = tk.Tk()
        root.title("TESTE DE LOGIN")
        root.geometry("400x300")
        
        # Centralizar
        root.update_idletasks()
        x = (root.winfo_screenwidth() // 2) - (400 // 2)
        y = (root.winfo_screenheight() // 2) - (300 // 2)
        root.geometry(f"400x300+{x}+{y}")
        
        # Forçar aparecer
        root.lift()
        root.attributes('-topmost', True)
        root.focus_force()
        
        frame = tk.Frame(root, padx=30, pady=30)
        frame.pack(fill=tk.BOTH, expand=True)
        
        tk.Label(frame, text="🔐 TESTE DE LOGIN", 
                font=('Arial', 16, 'bold')).pack(pady=10)
        
        tk.Label(frame, text="Credenciais para teste:", 
                font=('Arial', 12)).pack(pady=5)
        
        tk.Label(frame, text="Usuário: admin", 
                font=('Arial', 11, 'bold')).pack()
        tk.Label(frame, text="Senha: admin123", 
                font=('Arial', 11, 'bold')).pack()
        
        def abrir_login():
            try:
                root.destroy()
                
                # Criar nova janela para login
                login_root = tk.Tk()
                login_root.withdraw()
                
                def on_success(user_data):
                    messagebox.showinfo("Sucesso!", 
                                      f"Login realizado com sucesso!\n"
                                      f"Usuário: {user_data['full_name']}")
                    login_root.quit()
                
                # Mostrar janela de login
                LoginWindow(login_root, auth_manager, on_success)
                login_root.mainloop()
                
            except Exception as e:
                messagebox.showerror("Erro", f"Erro no login: {e}")
        
        tk.Button(frame, text="🚀 ABRIR TELA DE LOGIN", 
                 command=abrir_login,
                 font=('Arial', 12, 'bold'),
                 bg='blue', fg='white',
                 padx=20, pady=10).pack(pady=20)
        
        tk.Button(frame, text="❌ Fechar", 
                 command=root.destroy,
                 font=('Arial', 10),
                 padx=15, pady=5).pack()
        
        # Remover topmost após 2 segundos
        root.after(2000, lambda: root.attributes('-topmost', False))
        
        print("✅ Janela de teste criada!")
        print("✅ Clique em 'ABRIR TELA DE LOGIN' para testar")
        
        root.mainloop()
        
    except Exception as e:
        print(f"❌ Erro no teste de interface: {e}")
        import traceback
        traceback.print_exc()

def main():
    """Função principal"""
    
    # Executar diagnóstico
    diagnosticar_login()
    
    # Perguntar se quer testar interface
    print("\n🤔 Deseja testar a interface de login? (s/n): ", end="")
    
    try:
        resposta = input().lower().strip()
        if resposta in ['s', 'sim', 'y', 'yes']:
            testar_login_interface()
        else:
            print("\n✅ Diagnóstico concluído!")
            print("💡 Agora tente executar: py executar_terminal.py")
    except:
        print("\n✅ Diagnóstico concluído!")

if __name__ == "__main__":
    main()
