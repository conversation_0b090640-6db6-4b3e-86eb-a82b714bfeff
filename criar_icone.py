#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Cria ícone para a aplicação de Gestão de Contas
"""

from PIL import Image, ImageDraw, ImageFont
import os

def create_icon():
    """Cria um ícone para a aplicação"""
    try:
        # Criar imagem 256x256 (tamanho padrão para ícones)
        size = 256
        img = Image.new('RGBA', (size, size), (0, 0, 0, 0))
        draw = ImageDraw.Draw(img)
        
        # Cores
        bg_color = '#2c3e50'  # Azul escuro
        accent_color = '#3498db'  # Azul claro
        text_color = '#ffffff'  # Branco
        money_color = '#27ae60'  # Verde
        
        # Desenhar fundo circular
        margin = 20
        draw.ellipse([margin, margin, size-margin, size-margin], 
                    fill=bg_color, outline=accent_color, width=8)
        
        # Desenhar símbolo de dinheiro ($)
        center_x = size // 2
        center_y = size // 2 - 10
        
        # Símbolo $ estilizado
        try:
            # Tentar usar fonte do sistema
            font_large = ImageFont.truetype("arial.ttf", 120)
            font_small = ImageFont.truetype("arial.ttf", 40)
        except:
            # Fallback para fonte padrão
            font_large = ImageFont.load_default()
            font_small = ImageFont.load_default()
        
        # Desenhar símbolo $
        draw.text((center_x, center_y), "$", font=font_large, 
                 fill=money_color, anchor="mm")
        
        # Desenhar texto "GC" (Gestão de Contas)
        draw.text((center_x, center_y + 60), "GC", font=font_small, 
                 fill=text_color, anchor="mm")
        
        # Salvar como PNG primeiro
        img.save('app_icon.png', 'PNG')
        print("✓ Ícone PNG criado: app_icon.png")
        
        # Converter para ICO
        # Criar múltiplos tamanhos para o ICO
        sizes = [(16, 16), (32, 32), (48, 48), (64, 64), (128, 128), (256, 256)]
        icons = []
        
        for icon_size in sizes:
            icon_img = img.resize(icon_size, Image.Resampling.LANCZOS)
            icons.append(icon_img)
        
        # Salvar como ICO
        icons[0].save('app_icon.ico', format='ICO', sizes=[(icon.width, icon.height) for icon in icons])
        print("✓ Ícone ICO criado: app_icon.ico")
        
        return True
        
    except ImportError:
        print("⚠️ Pillow não está instalado. Criando ícone simples...")
        return create_simple_icon()
    except Exception as e:
        print(f"⚠️ Erro ao criar ícone: {e}")
        return create_simple_icon()

def create_simple_icon():
    """Cria um ícone simples usando apenas texto"""
    try:
        # Criar um arquivo de texto que será convertido em ícone
        icon_content = """
        Este é um placeholder para o ícone da aplicação.
        O PyInstaller usará o ícone padrão do Windows.
        """
        
        with open('app_icon_placeholder.txt', 'w', encoding='utf-8') as f:
            f.write(icon_content)
        
        print("✓ Placeholder de ícone criado")
        return False  # Indica que não foi possível criar ícone real
        
    except Exception as e:
        print(f"✗ Erro ao criar placeholder: {e}")
        return False

def install_pillow():
    """Instala Pillow se necessário"""
    try:
        import subprocess
        import sys
        
        print("Instalando Pillow para criação de ícones...")
        subprocess.check_call([sys.executable, "-m", "pip", "install", "Pillow"])
        print("✓ Pillow instalado com sucesso")
        return True
    except Exception as e:
        print(f"✗ Erro ao instalar Pillow: {e}")
        return False

def main():
    """Função principal"""
    print("CRIADOR DE ÍCONE - GESTÃO DE CONTAS")
    print("=" * 50)
    
    # Tentar criar ícone
    if not create_icon():
        print("\nTentando instalar Pillow...")
        if install_pillow():
            print("Tentando criar ícone novamente...")
            create_icon()
    
    print("\n" + "=" * 50)
    print("🎨 ÍCONE CRIADO!")
    print("\n📁 ARQUIVOS GERADOS:")
    
    if os.path.exists('app_icon.ico'):
        print("  • app_icon.ico (ícone principal)")
    if os.path.exists('app_icon.png'):
        print("  • app_icon.png (versão PNG)")
    
    print("\n🚀 PRÓXIMO PASSO:")
    print("  Execute: python criar_executavel_com_icone.py")
    
    input("\nPressione Enter para sair...")

if __name__ == "__main__":
    main()
