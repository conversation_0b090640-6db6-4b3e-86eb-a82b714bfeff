#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Teste de importação dos módulos
"""

import sys
sys.path.append('src')

print("Testando importações...")

try:
    from database import DatabaseManager
    print("✓ DatabaseManager importado")
except Exception as e:
    print(f"✗ Erro ao importar DatabaseManager: {e}")

try:
    from modules.auto_update_manager import AutoUpdateManager
    print("✓ AutoUpdateManager importado")
except Exception as e:
    print(f"✗ Erro ao importar AutoUpdateManager: {e}")

try:
    from gui.auto_update_config import AutoUpdateConfigWindow
    print("✓ AutoUpdateConfigWindow importado")
except Exception as e:
    print(f"✗ Erro ao importar AutoUpdateConfigWindow: {e}")

# Teste de criação
try:
    db_manager = DatabaseManager("data/test_import.db")
    db_manager.initialize_database()
    print("✓ DatabaseManager criado")
    
    auto_update_manager = AutoUpdateManager(db_manager)
    print("✓ AutoUpdateManager criado")
    print(f"✓ auto_update_manager é None? {auto_update_manager is None}")
    
except Exception as e:
    print(f"✗ Erro ao criar objetos: {e}")
    import traceback
    traceback.print_exc()

print("Teste concluído!")
