#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Teste do banco de dados
"""

from src.database import DatabaseManager
from src.auth import AuthManager

def test_database():
    print("Testando banco de dados...")
    
    try:
        # Inicializar banco
        db_manager = DatabaseManager()
        print("DatabaseManager criado")
        
        db_manager.initialize_database()
        print("Banco de dados inicializado")
        
        # Testar autenticação
        auth_manager = AuthManager(db_manager)
        print("AuthManager criado")
        
        # Criar usuário admin se não existir
        if not auth_manager.user_exists('admin'):
            auth_manager.create_user(
                username='admin',
                password='admin123',
                email='<EMAIL>',
                is_admin=True,
                full_name='Administrador do Sistema'
            )
            print("Usuário admin criado!")
        else:
            print("Usuário admin já existe")
        
        # Testar login
        user_data = auth_manager.authenticate_user('admin', 'admin123')
        if user_data:
            print(f"Login bem-sucedido: {user_data['full_name']}")
        else:
            print("Erro no login")
        
        print("Teste concluído com sucesso!")
        
    except Exception as e:
        print(f"Erro no teste: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_database()
