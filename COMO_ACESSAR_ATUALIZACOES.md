# 🔍 Como Acessar as Configurações de Atualizações Automáticas

## 📍 Localização do Menu

O botão **⚙️ Atualizações Automáticas** está localizado em:

**Configurações** → **Sistema** → **⚙️ Atualizações Automáticas**

## 🚀 Passo a Passo

### 1. Iniciar a Aplicação
```bash
py executar_terminal.py
```
ou
```bash
py main.py
```

### 2. Fazer Login
- Use suas credenciais normais
- O sistema de atualizações inicia automaticamente após o login

### 3. Acessar Configurações
1. Na janela principal, clique na aba **"Configurações"**
2. Na seção **"Sistema"**, você verá três botões:
   - 🧹 **Limpar Dados de Teste**
   - 🔄 **Reset Completo** 
   - ⚙️ **Atualizações Automáticas** ← **ESTE É O BOTÃO!**

### 4. Abrir Interface de Configuração
- Clique no botão **⚙️ Atualizações Automáticas**
- A janela de configuração será aberta com 3 abas:
  - **Geral**: Configurações do sistema
  - **Tarefas**: Configuração individual das tarefas
  - **Status**: Monitoramento em tempo real

## ❓ Se o Botão Não Aparecer

### Possíveis Causas:
1. **Sistema não inicializado**: O auto_update_manager não foi criado
2. **Erro na importação**: Problema com os módulos
3. **Versão antiga**: Usando versão sem as atualizações

### Soluções:

#### 1. Verificar se o Sistema Está Funcionando
```bash
py test_login_auto.py
```
Este script testa diretamente se o botão aparece.

#### 2. Testar Importações
```bash
py test_import.py
```
Verifica se todos os módulos estão sendo importados corretamente.

#### 3. Verificar Logs
Após fazer login, verifique se aparece no console:
```
✓ Sistema de atualizações automáticas inicializado
✓ Sistema de atualizações automáticas iniciado
```

#### 4. Teste Manual
Se o botão não aparecer, você pode testar manualmente:
```bash
py demo_auto_updates.py
```

## 🔧 Solução de Problemas

### Erro: "Módulo não encontrado"
```bash
# Verificar se os arquivos existem
ls src/modules/auto_update_manager.py
ls src/gui/auto_update_config.py
```

### Erro: "auto_update_manager é None"
- Reinicie a aplicação
- Verifique se não há erros no console durante a inicialização
- Execute `py test_import.py` para verificar importações

### Botão Aparece mas Não Funciona
- Verifique se o arquivo `config/auto_update_config.json` existe
- Execute `py test_auto_updates.py` para testar o sistema completo

## 📱 Interface da Configuração

Quando o botão funcionar, você verá:

### Aba Geral
- ☑️ **Habilitar atualizações automáticas**
- 🕐 **Intervalo de verificação** (em segundos)

### Aba Tarefas
- ⛽ **Eficiência de Combustível** (60 min)
- 🔧 **Lembretes de Manutenção** (24h)
- 🛡️ **Vencimento de Seguros** (24h)
- 💰 **Cálculos Financeiros** (30 min)
- 🧹 **Limpeza de Dados** (7 dias)

### Aba Status
- 📊 Status em tempo real
- ▶️ Executar tarefas manualmente
- ⏹️ Parar/Iniciar sistema

## 🎯 Confirmação de Funcionamento

Quando tudo estiver funcionando, você verá:

1. **No Console** (após login):
   ```
   ✓ Sistema de atualizações automáticas inicializado
   ✓ Sistema de atualizações automáticas iniciado
   ```

2. **Na Interface**: 
   - Botão **⚙️ Atualizações Automáticas** visível
   - Interface de configuração abre sem erros

3. **Nos Logs** (`logs/auto_updates.log`):
   ```
   2024-01-16 10:30:00 - AutoUpdateManager - INFO - Sistema iniciado
   2024-01-16 10:30:15 - AutoUpdateManager - INFO - Executando: fuel_efficiency_update
   ```

## 📞 Suporte

Se ainda não conseguir acessar:

1. **Execute os testes**:
   ```bash
   py test_import.py
   py test_login_auto.py
   py demo_auto_updates.py
   ```

2. **Verifique os arquivos**:
   - `src/modules/auto_update_manager.py`
   - `src/gui/auto_update_config.py`
   - `config/auto_update_config.json`

3. **Consulte os logs**:
   - Console da aplicação
   - `logs/auto_updates.log`

---

**✅ O sistema está funcionando corretamente quando testado!**  
Se o botão não aparecer na sua execução normal, use os scripts de teste para diagnosticar o problema.
