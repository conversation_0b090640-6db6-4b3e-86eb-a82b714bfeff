#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Janela principal do sistema de gestão de contas
"""

import tkinter as tk
from tkinter import ttk, messagebox, simpledialog
from tkinter import font as tkFont
from datetime import datetime, date, timedelta
import calendar

class MainWindow:
    def __init__(self, root, db_manager, auth_manager, current_user, logout_callback, auto_update_manager=None):
        self.root = root
        self.db_manager = db_manager
        self.auth_manager = auth_manager
        self.current_user = current_user
        self.logout_callback = logout_callback
        self.auto_update_manager = auto_update_manager
        
        # Configurar janela principal
        self.setup_main_window()
        
        # Criar interface
        self.create_widgets()
        
        # Carregar dados iniciais
        self.load_initial_data()
    
    def setup_main_window(self):
        """Configura a janela principal"""
        self.root.title(f"Sistema de Gestão de Contas - {self.current_user['full_name']}")
        self.root.geometry("1200x800")
        self.root.state('zoomed')  # Maximizar no Windows
        
        # Configurar estilo
        style = ttk.Style()
        style.theme_use('clam')
        
        # Configurar cores personalizadas
        style.configure('Title.TLabel', font=('Arial', 14, 'bold'))
        style.configure('Subtitle.TLabel', font=('Arial', 10))
        style.configure('Header.TFrame', background='#f0f0f0')
    
    def create_widgets(self):
        """Cria os widgets da interface principal"""
        # Frame principal
        self.main_frame = ttk.Frame(self.root)
        self.main_frame.pack(fill=tk.BOTH, expand=True)
        
        # Criar barra de menu
        self.create_menu_bar()
        
        # Criar header
        self.create_header()
        
        # Criar área de conteúdo
        self.create_content_area()
        
        # Criar barra de status
        self.create_status_bar()
    
    def create_menu_bar(self):
        """Cria a barra de menu"""
        menubar = tk.Menu(self.root)
        self.root.config(menu=menubar)
        
        # Menu Arquivo
        file_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="Arquivo", menu=file_menu)
        file_menu.add_command(label="Backup", command=self.backup_database)
        file_menu.add_command(label="Restaurar", command=self.restore_database)
        file_menu.add_separator()
        file_menu.add_command(label="Sair", command=self.logout)
        
        # Menu Carteiras
        wallet_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="Carteiras", menu=wallet_menu)
        wallet_menu.add_command(label="Gerenciar Carteiras", command=self.show_wallets)
        wallet_menu.add_command(label="Nova Carteira", command=self.new_wallet)
        
        # Menu Transações
        transaction_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="Transações", menu=transaction_menu)
        transaction_menu.add_command(label="Nova Receita", command=self.new_income)
        transaction_menu.add_command(label="Nova Despesa", command=self.new_expense)
        transaction_menu.add_separator()
        transaction_menu.add_command(label="Histórico", command=self.show_transactions)
        
        # Menu Veículos
        vehicles_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="Veículos", menu=vehicles_menu)
        vehicles_menu.add_command(label="Gerenciar Veículos", command=self.show_vehicle_manager)
        vehicles_menu.add_separator()
        vehicles_menu.add_command(label="Novo Veículo", command=self.new_vehicle)
        vehicles_menu.add_command(label="Nova Manutenção", command=self.new_maintenance)
        vehicles_menu.add_command(label="Novo Abastecimento", command=self.new_fuel_record)

        # Menu Relatórios
        reports_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="Relatórios", menu=reports_menu)
        reports_menu.add_command(label="Por Categoria", command=self.show_category_report)
        reports_menu.add_command(label="Fluxo de Caixa", command=self.show_cashflow_report)
        reports_menu.add_command(label="Contas a Vencer", command=self.show_due_bills)
        reports_menu.add_separator()
        reports_menu.add_command(label="Relatório de Veículos", command=self.show_vehicle_report)
        
        # Menu Configurações
        config_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="Configurações", menu=config_menu)
        config_menu.add_command(label="Categorias", command=self.manage_categories)
        config_menu.add_command(label="Preferências", command=self.show_preferences)
        config_menu.add_command(label="Alterar Senha", command=self.change_password)
        
        # Menu Administração (apenas para admin)
        if self.current_user['is_admin']:
            admin_menu = tk.Menu(menubar, tearoff=0)
            menubar.add_cascade(label="Administração", menu=admin_menu)
            admin_menu.add_command(label="Gerenciar Usuários", command=self.manage_users)
            admin_menu.add_command(label="Logs do Sistema", command=self.show_system_logs)
        
        # Menu Ajuda
        help_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="Ajuda", menu=help_menu)
        help_menu.add_command(label="Sobre", command=self.show_about)
    
    def create_header(self):
        """Cria o cabeçalho da aplicação"""
        header_frame = ttk.Frame(self.main_frame, style='Header.TFrame')
        header_frame.pack(fill=tk.X, padx=10, pady=(10, 0))
        
        # Título e informações do usuário
        title_frame = ttk.Frame(header_frame)
        title_frame.pack(fill=tk.X, pady=10)
        
        # Título
        title_label = ttk.Label(title_frame, text="Dashboard Financeiro", 
                              style='Title.TLabel')
        title_label.pack(side=tk.LEFT)
        
        # Informações do usuário
        user_info = f"Usuário: {self.current_user['full_name']}"
        if self.current_user['is_admin']:
            user_info += " (Administrador)"
        
        user_label = ttk.Label(title_frame, text=user_info, style='Subtitle.TLabel')
        user_label.pack(side=tk.RIGHT)
        
        # Data atual
        today = datetime.now().strftime("%d/%m/%Y")
        date_label = ttk.Label(title_frame, text=f"Data: {today}", style='Subtitle.TLabel')
        date_label.pack(side=tk.RIGHT, padx=(0, 20))
    
    def create_content_area(self):
        """Cria a área de conteúdo principal"""
        # Notebook para abas
        self.notebook = ttk.Notebook(self.main_frame)
        self.notebook.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # Aba Dashboard
        self.create_dashboard_tab()
        
        # Aba Carteiras
        self.create_wallets_tab()
        
        # Aba Transações
        self.create_transactions_tab()
        
        # Aba Relatórios
        self.create_reports_tab()
    
    def create_status_bar(self):
        """Cria a barra de status"""
        self.status_bar = ttk.Frame(self.main_frame)
        self.status_bar.pack(fill=tk.X, side=tk.BOTTOM)
        
        self.status_label = ttk.Label(self.status_bar, text="Pronto")
        self.status_label.pack(side=tk.LEFT, padx=10, pady=5)
        
        # Indicador de alertas
        self.alerts_label = ttk.Label(self.status_bar, text="", foreground="red")
        self.alerts_label.pack(side=tk.RIGHT, padx=10, pady=5)
    
    def create_dashboard_tab(self):
        """Cria a aba do dashboard"""
        dashboard_frame = ttk.Frame(self.notebook)
        self.notebook.add(dashboard_frame, text="Dashboard")
        
        # Frame para cards de resumo
        cards_frame = ttk.Frame(dashboard_frame)
        cards_frame.pack(fill=tk.X, padx=20, pady=20)
        
        # Cards de resumo financeiro
        self.create_summary_cards(cards_frame)
        
        # Frame para gráficos e listas
        content_frame = ttk.Frame(dashboard_frame)
        content_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=(0, 20))
        
        # Frame esquerdo - Transações recentes
        left_frame = ttk.LabelFrame(content_frame, text="Transações Recentes", padding=10)
        left_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=(0, 10))
        
        # Lista de transações recentes
        self.create_recent_transactions_list(left_frame)
        
        # Frame direito - Alertas e contas a vencer
        right_frame = ttk.LabelFrame(content_frame, text="Alertas e Lembretes", padding=10)
        right_frame.pack(side=tk.RIGHT, fill=tk.BOTH, expand=True, padx=(10, 0))
        
        # Lista de alertas
        self.create_alerts_list(right_frame)
    
    def create_summary_cards(self, parent):
        """Cria os cards de resumo financeiro"""
        # Card Saldo Total
        total_card = ttk.LabelFrame(parent, text="Saldo Total", padding=10)
        total_card.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=(0, 10))
        
        self.total_balance_label = ttk.Label(total_card, text="R$ 0,00", 
                                           font=('Arial', 16, 'bold'), foreground='blue')
        self.total_balance_label.pack()
        
        # Card Receitas do Mês
        income_card = ttk.LabelFrame(parent, text="Receitas do Mês", padding=10)
        income_card.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=(0, 10))
        
        self.month_income_label = ttk.Label(income_card, text="R$ 0,00", 
                                          font=('Arial', 16, 'bold'), foreground='green')
        self.month_income_label.pack()
        
        # Card Despesas do Mês
        expense_card = ttk.LabelFrame(parent, text="Despesas do Mês", padding=10)
        expense_card.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=(0, 10))
        
        self.month_expense_label = ttk.Label(expense_card, text="R$ 0,00", 
                                           font=('Arial', 16, 'bold'), foreground='red')
        self.month_expense_label.pack()
        
        # Card Saldo do Mês
        balance_card = ttk.LabelFrame(parent, text="Saldo do Mês", padding=10)
        balance_card.pack(side=tk.LEFT, fill=tk.X, expand=True)
        
        self.month_balance_label = ttk.Label(balance_card, text="R$ 0,00", 
                                           font=('Arial', 16, 'bold'))
        self.month_balance_label.pack()
    
    def create_recent_transactions_list(self, parent):
        """Cria a lista de transações recentes"""
        # Treeview para transações
        columns = ('Data', 'Descrição', 'Categoria', 'Valor', 'Tipo')
        self.recent_tree = ttk.Treeview(parent, columns=columns, show='headings', height=10)
        
        # Configurar colunas
        self.recent_tree.heading('Data', text='Data')
        self.recent_tree.heading('Descrição', text='Descrição')
        self.recent_tree.heading('Categoria', text='Categoria')
        self.recent_tree.heading('Valor', text='Valor')
        self.recent_tree.heading('Tipo', text='Tipo')
        
        self.recent_tree.column('Data', width=80)
        self.recent_tree.column('Descrição', width=200)
        self.recent_tree.column('Categoria', width=120)
        self.recent_tree.column('Valor', width=100)
        self.recent_tree.column('Tipo', width=80)
        
        # Scrollbar
        recent_scroll = ttk.Scrollbar(parent, orient=tk.VERTICAL, command=self.recent_tree.yview)
        self.recent_tree.configure(yscrollcommand=recent_scroll.set)
        
        # Pack
        self.recent_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        recent_scroll.pack(side=tk.RIGHT, fill=tk.Y)
    
    def create_alerts_list(self, parent):
        """Cria a lista de alertas"""
        # Treeview para alertas
        columns = ('Tipo', 'Descrição', 'Data')
        self.alerts_tree = ttk.Treeview(parent, columns=columns, show='headings', height=10)
        
        # Configurar colunas
        self.alerts_tree.heading('Tipo', text='Tipo')
        self.alerts_tree.heading('Descrição', text='Descrição')
        self.alerts_tree.heading('Data', text='Data')
        
        self.alerts_tree.column('Tipo', width=80)
        self.alerts_tree.column('Descrição', width=250)
        self.alerts_tree.column('Data', width=80)
        
        # Scrollbar
        alerts_scroll = ttk.Scrollbar(parent, orient=tk.VERTICAL, command=self.alerts_tree.yview)
        self.alerts_tree.configure(yscrollcommand=alerts_scroll.set)
        
        # Pack
        self.alerts_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        alerts_scroll.pack(side=tk.RIGHT, fill=tk.Y)

    def create_wallets_tab(self):
        """Cria a aba de carteiras"""
        wallets_frame = ttk.Frame(self.notebook)
        self.notebook.add(wallets_frame, text="Carteiras")

        # Toolbar
        toolbar_frame = ttk.Frame(wallets_frame)
        toolbar_frame.pack(fill=tk.X, padx=10, pady=10)

        ttk.Button(toolbar_frame, text="Nova Carteira", command=self.new_wallet, width=15).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(toolbar_frame, text="Editar", command=self.edit_wallet, width=10).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(toolbar_frame, text="Excluir", command=self.delete_wallet, width=10).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(toolbar_frame, text="🔄 Recalcular Saldos", command=self.recalculate_wallet_balances, width=18).pack(side=tk.LEFT)

        # Lista de carteiras
        columns = ('Nome', 'Tipo', 'Saldo Inicial', 'Saldo Atual', 'Status')
        self.wallets_tree = ttk.Treeview(wallets_frame, columns=columns, show='headings')

        for col in columns:
            self.wallets_tree.heading(col, text=col)
            self.wallets_tree.column(col, width=150)

        # Scrollbars
        wallet_v_scroll = ttk.Scrollbar(wallets_frame, orient=tk.VERTICAL, command=self.wallets_tree.yview)
        wallet_h_scroll = ttk.Scrollbar(wallets_frame, orient=tk.HORIZONTAL, command=self.wallets_tree.xview)
        self.wallets_tree.configure(yscrollcommand=wallet_v_scroll.set, xscrollcommand=wallet_h_scroll.set)

        # Pack
        self.wallets_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=(10, 0), pady=(0, 10))
        wallet_v_scroll.pack(side=tk.RIGHT, fill=tk.Y, pady=(0, 10))
        wallet_h_scroll.pack(side=tk.BOTTOM, fill=tk.X, padx=(10, 0))

    def create_transactions_tab(self):
        """Cria a aba de transações"""
        transactions_frame = ttk.Frame(self.notebook)
        self.notebook.add(transactions_frame, text="Transações")

        # Toolbar
        toolbar_frame = ttk.Frame(transactions_frame)
        toolbar_frame.pack(fill=tk.X, padx=10, pady=10)

        ttk.Button(toolbar_frame, text="Nova Receita", command=self.new_income, width=15).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(toolbar_frame, text="Nova Despesa", command=self.new_expense, width=15).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(toolbar_frame, text="Editar", command=self.edit_transaction, width=10).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(toolbar_frame, text="Excluir", command=self.delete_transaction, width=10).pack(side=tk.LEFT)

        # Filtros
        filter_frame = ttk.LabelFrame(transactions_frame, text="Filtros", padding=10)
        filter_frame.pack(fill=tk.X, padx=10, pady=(0, 10))

        # Filtro por período
        ttk.Label(filter_frame, text="Período:").pack(side=tk.LEFT, padx=(0, 5))
        self.period_combo = ttk.Combobox(filter_frame, values=["Todos", "Este Mês", "Últimos 30 dias", "Este Ano", "Incluir Futuras"],
                                       state="readonly", width=15)
        self.period_combo.set("Todos")  # Mudado de "Este Mês" para "Todos"
        self.period_combo.pack(side=tk.LEFT, padx=(0, 20))

        # Filtro por tipo
        ttk.Label(filter_frame, text="Tipo:").pack(side=tk.LEFT, padx=(0, 5))
        self.type_combo = ttk.Combobox(filter_frame, values=["Todos", "Receitas", "Despesas"],
                                     state="readonly", width=15)
        self.type_combo.set("Todos")
        self.type_combo.pack(side=tk.LEFT, padx=(0, 20))

        # Botão filtrar
        ttk.Button(filter_frame, text="Filtrar", command=self.filter_transactions, width=10).pack(side=tk.LEFT)

        # Lista de transações
        columns = ('Data', 'Descrição', 'Categoria', 'Carteira', 'Valor', 'Vencimento', 'Parcelas', 'Tipo', 'Status', 'Observações')
        self.transactions_tree = ttk.Treeview(transactions_frame, columns=columns, show='headings')

        for col in columns:
            self.transactions_tree.heading(col, text=col)
            if col == 'Descrição':
                self.transactions_tree.column(col, width=200)
            elif col == 'Observações':
                self.transactions_tree.column(col, width=150)
            elif col == 'Parcelas':
                self.transactions_tree.column(col, width=80)
            elif col == 'Vencimento':
                self.transactions_tree.column(col, width=100)
            else:
                self.transactions_tree.column(col, width=100)

        # Scrollbars
        trans_v_scroll = ttk.Scrollbar(transactions_frame, orient=tk.VERTICAL, command=self.transactions_tree.yview)
        trans_h_scroll = ttk.Scrollbar(transactions_frame, orient=tk.HORIZONTAL, command=self.transactions_tree.xview)
        self.transactions_tree.configure(yscrollcommand=trans_v_scroll.set, xscrollcommand=trans_h_scroll.set)

        # Context menu para transações
        self.transaction_context_menu = tk.Menu(self.root, tearoff=0)
        self.transaction_context_menu.add_command(label="✅ Marcar como Pago", command=self.mark_transaction_as_paid)
        self.transaction_context_menu.add_command(label="⏳ Marcar como Pendente", command=self.mark_transaction_as_pending)
        self.transaction_context_menu.add_separator()
        self.transaction_context_menu.add_command(label="✏️ Editar", command=self.edit_transaction)
        self.transaction_context_menu.add_command(label="🗑️ Excluir", command=self.delete_transaction)

        # Bind do menu de contexto
        self.transactions_tree.bind("<Button-3>", self.show_transaction_context_menu)

        # Pack
        self.transactions_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=(10, 0), pady=(0, 10))
        trans_v_scroll.pack(side=tk.RIGHT, fill=tk.Y, pady=(0, 10))
        trans_h_scroll.pack(side=tk.BOTTOM, fill=tk.X, padx=(10, 0))

    def create_reports_tab(self):
        """Cria a aba de relatórios"""
        reports_frame = ttk.Frame(self.notebook)
        self.notebook.add(reports_frame, text="Relatórios")

        # Notebook para sub-relatórios
        self.reports_notebook = ttk.Notebook(reports_frame)
        self.reports_notebook.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        # Relatório por categoria
        self.create_category_report_tab(self.reports_notebook)

        # Relatório de fluxo de caixa
        self.create_cashflow_report_tab(self.reports_notebook)

        # Contas a vencer
        self.create_due_bills_tab(self.reports_notebook)

        # Aba de Configurações
        self.create_settings_tab()

        # Aba de Administração (se for admin)
        if self.current_user['is_admin']:
            self.create_admin_tab()

    def create_category_report_tab(self, parent_notebook):
        """Cria aba de relatório por categoria"""
        category_frame = ttk.Frame(parent_notebook)
        parent_notebook.add(category_frame, text="Por Categoria")

        # Toolbar
        toolbar = ttk.Frame(category_frame)
        toolbar.pack(fill=tk.X, padx=10, pady=10)

        ttk.Label(toolbar, text="Período:").pack(side=tk.LEFT, padx=(0, 5))
        period_combo = ttk.Combobox(toolbar, values=["Este Mês", "Últimos 3 Meses", "Este Ano"],
                                   state="readonly", width=15)
        period_combo.set("Este Mês")
        period_combo.pack(side=tk.LEFT, padx=(0, 10))

        ttk.Button(toolbar, text="Atualizar",
                  command=lambda: self.update_category_report(category_tree, period_combo.get())).pack(side=tk.LEFT)

        # Treeview para relatório
        columns = ('Categoria', 'Tipo', 'Transações', 'Total', 'Média')
        category_tree = ttk.Treeview(category_frame, columns=columns, show='headings')

        for col in columns:
            category_tree.heading(col, text=col)
            category_tree.column(col, width=120)

        # Scrollbar
        scrollbar = ttk.Scrollbar(category_frame, orient=tk.VERTICAL, command=category_tree.yview)
        category_tree.configure(yscrollcommand=scrollbar.set)

        # Pack
        category_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=(10, 0), pady=(0, 10))
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y, pady=(0, 10))

        # Carregar dados iniciais
        self.update_category_report(category_tree, "Este Mês")

    def create_cashflow_report_tab(self, parent_notebook):
        """Cria aba de fluxo de caixa"""
        cashflow_frame = ttk.Frame(parent_notebook)
        parent_notebook.add(cashflow_frame, text="Fluxo de Caixa")

        # Cards de resumo
        cards_frame = ttk.Frame(cashflow_frame)
        cards_frame.pack(fill=tk.X, padx=20, pady=20)

        # Card Receitas
        income_card = ttk.LabelFrame(cards_frame, text="Total de Receitas", padding=10)
        income_card.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=(0, 10))

        self.income_total_label = ttk.Label(income_card, text="R$ 0,00",
                                          font=('Arial', 14, 'bold'), foreground='green')
        self.income_total_label.pack()

        # Card Despesas
        expense_card = ttk.LabelFrame(cards_frame, text="Total de Despesas", padding=10)
        expense_card.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=(0, 10))

        self.expense_total_label = ttk.Label(expense_card, text="R$ 0,00",
                                           font=('Arial', 14, 'bold'), foreground='red')
        self.expense_total_label.pack()

        # Card Saldo
        balance_card = ttk.LabelFrame(cards_frame, text="Saldo Líquido", padding=10)
        balance_card.pack(side=tk.LEFT, fill=tk.X, expand=True)

        self.balance_total_label = ttk.Label(balance_card, text="R$ 0,00",
                                           font=('Arial', 14, 'bold'))
        self.balance_total_label.pack()

        # Lista mensal de fluxo de caixa
        monthly_frame = ttk.LabelFrame(cashflow_frame, text="📅 Fluxo de Caixa Mensal", padding=10)
        monthly_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=(0, 20))

        # Treeview para dados mensais
        columns = ('Mês', 'Entradas', 'Saídas', 'Saldo', 'Status')
        self.monthly_cashflow_tree = ttk.Treeview(monthly_frame, columns=columns, show='headings', height=12)

        # Configurar colunas
        self.monthly_cashflow_tree.heading('Mês', text='📅 Mês')
        self.monthly_cashflow_tree.heading('Entradas', text='📈 Entradas')
        self.monthly_cashflow_tree.heading('Saídas', text='📉 Saídas')
        self.monthly_cashflow_tree.heading('Saldo', text='💰 Saldo')
        self.monthly_cashflow_tree.heading('Status', text='🔄 Status')

        # Configurar largura das colunas
        self.monthly_cashflow_tree.column('Mês', width=140, anchor='center')
        self.monthly_cashflow_tree.column('Entradas', width=130, anchor='center')
        self.monthly_cashflow_tree.column('Saídas', width=130, anchor='center')
        self.monthly_cashflow_tree.column('Saldo', width=130, anchor='center')
        self.monthly_cashflow_tree.column('Status', width=140, anchor='center')

        # Scrollbar para a lista mensal
        monthly_scrollbar = ttk.Scrollbar(monthly_frame, orient=tk.VERTICAL, command=self.monthly_cashflow_tree.yview)
        self.monthly_cashflow_tree.configure(yscrollcommand=monthly_scrollbar.set)

        # Pack da lista mensal
        self.monthly_cashflow_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        monthly_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

        # Atualizar dados
        self.update_cashflow_report()

    def create_due_bills_tab(self, parent_notebook):
        """Cria aba de contas a vencer"""
        due_bills_frame = ttk.Frame(parent_notebook)
        parent_notebook.add(due_bills_frame, text="Contas a Vencer")

        # Toolbar
        toolbar = ttk.Frame(due_bills_frame)
        toolbar.pack(fill=tk.X, padx=10, pady=10)

        ttk.Label(toolbar, text="Próximos:").pack(side=tk.LEFT, padx=(0, 5))
        days_combo = ttk.Combobox(toolbar, values=["7 dias", "15 dias", "30 dias"],
                                 state="readonly", width=10)
        days_combo.set("7 dias")
        days_combo.pack(side=tk.LEFT, padx=(0, 10))

        ttk.Button(toolbar, text="Atualizar",
                  command=lambda: self.update_due_bills(due_tree, days_combo.get())).pack(side=tk.LEFT)

        # Treeview para contas a vencer
        columns = ('Vencimento', 'Descrição', 'Categoria', 'Valor', 'Status')
        due_tree = ttk.Treeview(due_bills_frame, columns=columns, show='headings')

        for col in columns:
            due_tree.heading(col, text=col)
            due_tree.column(col, width=120)

        # Scrollbar
        due_scrollbar = ttk.Scrollbar(due_bills_frame, orient=tk.VERTICAL, command=due_tree.yview)
        due_tree.configure(yscrollcommand=due_scrollbar.set)

        # Pack
        due_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=(10, 0), pady=(0, 10))
        due_scrollbar.pack(side=tk.RIGHT, fill=tk.Y, pady=(0, 10))

        # Carregar dados iniciais
        self.update_due_bills(due_tree, "7 dias")

    def create_settings_tab(self):
        """Cria aba de configurações"""
        settings_frame = ttk.Frame(self.notebook)
        self.notebook.add(settings_frame, text="Configurações")

        # Frame principal
        main_settings_frame = ttk.Frame(settings_frame, padding="20")
        main_settings_frame.pack(fill=tk.BOTH, expand=True)

        # Título
        title_label = ttk.Label(main_settings_frame, text="Configurações do Sistema",
                               font=("Arial", 16, "bold"))
        title_label.pack(pady=(0, 20))

        # Seção Categorias
        categories_section = ttk.LabelFrame(main_settings_frame, text="Categorias", padding=10)
        categories_section.pack(fill=tk.X, pady=(0, 15))

        ttk.Button(categories_section, text="Gerenciar Categorias",
                  command=self.show_categories_manager).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(categories_section, text="Importar Categorias Padrão",
                  command=self.import_default_categories).pack(side=tk.LEFT)

        # Seção Usuário
        user_section = ttk.LabelFrame(main_settings_frame, text="Conta do Usuário", padding=10)
        user_section.pack(fill=tk.X, pady=(0, 15))

        ttk.Button(user_section, text="Alterar Senha",
                  command=self.show_change_password).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(user_section, text="Editar Perfil",
                  command=self.show_edit_profile).pack(side=tk.LEFT)

        # Seção Backup
        backup_section = ttk.LabelFrame(main_settings_frame, text="Backup e Restauração", padding=10)
        backup_section.pack(fill=tk.X, pady=(0, 15))

        ttk.Button(backup_section, text="Criar Backup",
                  command=self.backup_database).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(backup_section, text="Restaurar Backup",
                  command=self.restore_database).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(backup_section, text="Gerenciar Backups",
                  command=self.show_backup_manager).pack(side=tk.LEFT)

        # Seção Sistema
        system_section = ttk.LabelFrame(main_settings_frame, text="Sistema", padding=10)
        system_section.pack(fill=tk.X, pady=(0, 15))

        ttk.Button(system_section, text="🧹 Limpar Dados de Teste",
                  command=self.show_reset_options, width=20).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(system_section, text="🔄 Reset Completo",
                  command=self.show_full_reset, width=15).pack(side=tk.LEFT, padx=(0, 10))

        # Botão de configuração de atualizações automáticas
        if self.auto_update_manager:
            ttk.Button(system_section, text="⚙️ Atualizações Automáticas",
                      command=self.show_auto_update_config, width=20).pack(side=tk.LEFT)

    def create_admin_tab(self):
        """Cria aba de administração (apenas para admin)"""
        admin_frame = ttk.Frame(self.notebook)
        self.notebook.add(admin_frame, text="Administração")

        # Frame principal
        main_admin_frame = ttk.Frame(admin_frame, padding="20")
        main_admin_frame.pack(fill=tk.BOTH, expand=True)

        # Título
        title_label = ttk.Label(main_admin_frame, text="Administração do Sistema",
                               font=("Arialblack", 14, "bold"))
        title_label.pack(pady=(0, 20))

        # Seção Usuários
        users_section = ttk.LabelFrame(main_admin_frame, text="Gerenciamento de Usuários", padding=10)
        users_section.pack(fill=tk.X, pady=(0, 15))

        ttk.Button(users_section, text="Gerenciar Usuários",
                  command=self.show_users_manager).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(users_section, text="Criar Novo Usuário",
                  command=self.show_create_user).pack(side=tk.LEFT)

        # Seção Logs
        logs_section = ttk.LabelFrame(main_admin_frame, text="Logs do Sistema", padding=10)
        logs_section.pack(fill=tk.X, pady=(0, 15))

        ttk.Button(logs_section, text="Ver Logs",
                  command=self.show_system_logs).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(logs_section, text="Limpar Logs",
                  command=self.clear_system_logs).pack(side=tk.LEFT)

        # Seção Sistema
        system_section = ttk.LabelFrame(main_admin_frame, text="Sistema", padding=10)
        system_section.pack(fill=tk.X, pady=(0, 15))

        ttk.Button(system_section, text="Estatísticas do Sistema",
                  command=self.show_system_stats).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(system_section, text="Manutenção do Banco",
                  command=self.show_database_maintenance).pack(side=tk.LEFT)

    def load_initial_data(self):
        """Carrega dados iniciais da aplicação"""
        try:
            self.update_summary_cards()
            self.load_recent_transactions()
            self.load_alerts()
            self.load_wallets()
            self.load_transactions()
            self.check_due_bills()
        except Exception as e:
            messagebox.showerror("Erro", f"Erro ao carregar dados: {str(e)}")

    def update_summary_cards(self):
        """Atualiza os cards de resumo financeiro"""
        try:
            user_id = self.current_user['id']

            # Saldo total das carteiras
            query = "SELECT SUM(current_balance) FROM wallets WHERE user_id = ? AND is_active = TRUE"
            result = self.db_manager.execute_query(query, (user_id,))
            total_balance = result[0][0] if result[0][0] else 0
            self.total_balance_label.config(text=f"R$ {total_balance:,.2f}")

            # Receitas do mês atual
            current_month = datetime.now().strftime('%Y-%m')
            query = '''
                SELECT SUM(amount) FROM transactions
                WHERE user_id = ? AND transaction_type = 'income'
                AND strftime('%Y-%m', transaction_date) = ?
            '''
            result = self.db_manager.execute_query(query, (user_id, current_month))
            month_income = result[0][0] if result[0][0] else 0
            self.month_income_label.config(text=f"R$ {month_income:,.2f}")

            # Despesas do mês atual
            query = '''
                SELECT SUM(amount) FROM transactions
                WHERE user_id = ? AND transaction_type = 'expense'
                AND strftime('%Y-%m', transaction_date) = ?
            '''
            result = self.db_manager.execute_query(query, (user_id, current_month))
            month_expense = result[0][0] if result[0][0] else 0
            self.month_expense_label.config(text=f"R$ {month_expense:,.2f}")

            # Saldo do mês
            month_balance = month_income - month_expense
            color = 'green' if month_balance >= 0 else 'red'
            self.month_balance_label.config(text=f"R$ {month_balance:,.2f}", foreground=color)

        except Exception as e:
            print(f"Erro ao atualizar cards de resumo: {str(e)}")

    def load_recent_transactions(self):
        """Carrega transações recentes"""
        try:
            # Limpar lista atual
            for item in self.recent_tree.get_children():
                self.recent_tree.delete(item)

            user_id = self.current_user['id']
            query = '''
                SELECT t.transaction_date, t.description, c.name, t.amount, t.transaction_type
                FROM transactions t
                JOIN categories c ON t.category_id = c.id
                WHERE t.user_id = ?
                ORDER BY t.created_at DESC
                LIMIT 10
            '''

            transactions = self.db_manager.execute_query(query, (user_id,))

            for trans in transactions:
                date_str = trans['transaction_date']
                description = trans['description']
                category = trans['name']
                amount = f"R$ {trans['amount']:,.2f}"
                trans_type = "Receita" if trans['transaction_type'] == 'income' else "Despesa"

                # Inserir na árvore
                item = self.recent_tree.insert('', 'end', values=(date_str, description, category, amount, trans_type))

                # Colorir baseado no tipo
                if trans['transaction_type'] == 'income':
                    self.recent_tree.set(item, 'Valor', amount)
                    self.recent_tree.item(item, tags=('income',))
                else:
                    self.recent_tree.item(item, tags=('expense',))

            # Configurar tags de cor
            self.recent_tree.tag_configure('income', foreground='green')
            self.recent_tree.tag_configure('expense', foreground='red')

        except Exception as e:
            print(f"Erro ao carregar transações recentes: {str(e)}")

    def load_alerts(self):
        """Carrega alertas e lembretes"""
        try:
            # Limpar lista atual
            for item in self.alerts_tree.get_children():
                self.alerts_tree.delete(item)

            user_id = self.current_user['id']
            today = date.today()

            # Contas vencendo nos próximos 7 dias
            query = '''
                SELECT t.description, t.due_date, t.amount, c.name
                FROM transactions t
                JOIN categories c ON t.category_id = c.id
                WHERE t.user_id = ? AND t.is_paid = FALSE
                AND t.due_date IS NOT NULL
                AND t.due_date BETWEEN ? AND ?
                ORDER BY t.due_date
            '''

            end_date = today + timedelta(days=7)
            due_bills = self.db_manager.execute_query(query, (user_id, today.isoformat(), end_date.isoformat()))

            alert_count = 0
            for bill in due_bills:
                due_date = datetime.strptime(bill['due_date'], '%Y-%m-%d').date()
                days_left = (due_date - today).days

                if days_left == 0:
                    alert_type = "VENCE HOJE"
                elif days_left == 1:
                    alert_type = "VENCE AMANHÃ"
                else:
                    alert_type = f"VENCE EM {days_left} DIAS"

                description = f"{bill['description']} - R$ {bill['amount']:,.2f}"
                date_str = due_date.strftime('%d/%m')

                self.alerts_tree.insert('', 'end', values=(alert_type, description, date_str))
                alert_count += 1

            # Atualizar indicador de alertas na barra de status
            if alert_count > 0:
                self.alerts_label.config(text=f"{alert_count} alerta(s)")
            else:
                self.alerts_label.config(text="")

        except Exception as e:
            print(f"Erro ao carregar alertas: {str(e)}")

    def load_wallets(self):
        """Carrega lista de carteiras"""
        try:
            # Limpar lista atual
            for item in self.wallets_tree.get_children():
                self.wallets_tree.delete(item)

            user_id = self.current_user['id']

            # Primeiro, recalcular saldos para garantir consistência
            try:
                from src.modules.transaction_manager import TransactionManager
                transaction_manager = TransactionManager(self.db_manager)
                transaction_manager.recalculate_all_wallet_balances(user_id)
            except Exception as e:
                print(f"Aviso: Erro ao recalcular saldos: {str(e)}")

            query = '''
                SELECT id, name, wallet_type, initial_balance, current_balance, is_active
                FROM wallets
                WHERE user_id = ?
                ORDER BY name
            '''

            wallets = self.db_manager.execute_query(query, (user_id,))

            for wallet in wallets:
                wallet_type = {
                    'checking': 'Conta Corrente',
                    'savings': 'Poupança',
                    'credit': 'Cartão de Crédito',
                    'cash': 'Dinheiro'
                }.get(wallet['wallet_type'], wallet['wallet_type'])

                status = "Ativa" if wallet['is_active'] else "Inativa"

                values = (
                    wallet['name'],
                    wallet_type,
                    f"R$ {wallet['initial_balance']:,.2f}",
                    f"R$ {wallet['current_balance']:,.2f}",
                    status
                )

                self.wallets_tree.insert('', 'end', values=values)

        except Exception as e:
            print(f"Erro ao carregar carteiras: {str(e)}")

    def load_transactions(self):
        """Carrega lista de transações"""
        try:
            # Limpar lista atual
            for item in self.transactions_tree.get_children():
                self.transactions_tree.delete(item)

            user_id = self.current_user['id']

            # Construir query baseada nos filtros
            base_query = '''
                SELECT t.id, t.transaction_date, t.description, c.name as category_name,
                       w.name as wallet_name, t.amount, t.due_date, t.installments, t.transaction_type, t.is_paid, t.notes
                FROM transactions t
                JOIN categories c ON t.category_id = c.id
                JOIN wallets w ON t.wallet_id = w.id
                WHERE t.user_id = ?
            '''

            params = [user_id]

            # Aplicar filtros
            period_filter = self.period_combo.get()
            if period_filter == "Este Mês":
                current_month = datetime.now().strftime('%Y-%m')
                base_query += " AND strftime('%Y-%m', t.transaction_date) = ?"
                params.append(current_month)
            elif period_filter == "Últimos 30 dias":
                thirty_days_ago = (datetime.now() - timedelta(days=30)).date()
                base_query += " AND t.transaction_date >= ?"
                params.append(thirty_days_ago.isoformat())
            elif period_filter == "Este Ano":
                current_year = datetime.now().strftime('%Y')
                base_query += " AND strftime('%Y', t.transaction_date) = ?"
                params.append(current_year)
            elif period_filter == "Incluir Futuras":
                # Mostrar transações dos últimos 30 dias até 2 anos no futuro
                thirty_days_ago = (datetime.now() - timedelta(days=30)).date()
                two_years_future = (datetime.now() + timedelta(days=730)).date()
                base_query += " AND t.transaction_date BETWEEN ? AND ?"
                params.append(thirty_days_ago.isoformat())
                params.append(two_years_future.isoformat())

            type_filter = self.type_combo.get()
            if type_filter == "Receitas":
                base_query += " AND t.transaction_type = 'income'"
            elif type_filter == "Despesas":
                base_query += " AND t.transaction_type = 'expense'"

            base_query += " ORDER BY t.transaction_date DESC, t.created_at DESC"

            transactions = self.db_manager.execute_query(base_query, params)

            for trans in transactions:
                date_str = datetime.strptime(trans['transaction_date'], '%Y-%m-%d').strftime('%d/%m/%Y')
                trans_type = "Receita" if trans['transaction_type'] == 'income' else "Despesa"
                status = "Pago" if trans['is_paid'] else "Pendente"

                # Formatação da data de vencimento
                due_date_str = ""
                if trans['due_date']:
                    due_date_str = datetime.strptime(trans['due_date'], '%Y-%m-%d').strftime('%d/%m/%Y')

                # Formatação das parcelas
                installments = trans['installments'] if trans['installments'] else 1
                parcelas_str = f"{installments}x" if installments > 1 else "1x"

                # Formatação das observações
                notes = trans['notes'] if trans['notes'] else ""

                values = (
                    date_str,
                    trans['description'],
                    trans['category_name'],
                    trans['wallet_name'],
                    f"R$ {trans['amount']:,.2f}",
                    due_date_str,
                    parcelas_str,
                    trans_type,
                    status,
                    notes
                )

                item = self.transactions_tree.insert('', 'end', values=values)

                # Armazenar ID da transação nas tags para uso posterior
                if trans['transaction_type'] == 'income':
                    self.transactions_tree.item(item, tags=('income', str(trans['id'])))
                else:
                    self.transactions_tree.item(item, tags=('expense', str(trans['id'])))

            # Configurar tags de cor
            self.transactions_tree.tag_configure('income', foreground='green')
            self.transactions_tree.tag_configure('expense', foreground='red')

        except Exception as e:
            print(f"Erro ao carregar transações: {str(e)}")

    def check_due_bills(self):
        """Verifica contas vencendo e atualiza status"""
        try:
            user_id = self.current_user['id']
            today = date.today()

            # Contar contas vencendo hoje
            query = '''
                SELECT COUNT(*) FROM transactions
                WHERE user_id = ? AND is_paid = FALSE
                AND due_date = ?
            '''
            result = self.db_manager.execute_query(query, (user_id, today.isoformat()))
            due_today = result[0][0]

            if due_today > 0:
                self.status_label.config(text=f"Atenção: {due_today} conta(s) vencem hoje!")

        except Exception as e:
            print(f"Erro ao verificar contas vencendo: {str(e)}")

    # Métodos de callback para os menus e botões
    def logout(self):
        """Faz logout do usuário"""
        if messagebox.askyesno("Confirmar", "Deseja realmente sair do sistema?"):
            self.logout_callback()

    def backup_database(self):
        """Cria backup do banco de dados"""
        from tkinter import filedialog

        try:
            backup_path = filedialog.asksaveasfilename(
                title="Salvar Backup",
                defaultextension=".db",
                filetypes=[("Banco de Dados", "*.db"), ("Todos os arquivos", "*.*")]
            )

            if backup_path:
                self.db_manager.backup_database(backup_path)
                messagebox.showinfo("Sucesso", "Backup criado com sucesso!")

        except Exception as e:
            messagebox.showerror("Erro", f"Erro ao criar backup: {str(e)}")

    def restore_database(self):
        """Restaura banco de dados a partir de backup"""
        from tkinter import filedialog

        if not messagebox.askyesno("Confirmar",
                                 "Esta operação substituirá todos os dados atuais. Continuar?"):
            return

        try:
            backup_path = filedialog.askopenfilename(
                title="Selecionar Backup",
                filetypes=[("Banco de Dados", "*.db"), ("Todos os arquivos", "*.*")]
            )

            if backup_path:
                self.db_manager.restore_database(backup_path)
                messagebox.showinfo("Sucesso", "Banco de dados restaurado com sucesso!")
                self.load_initial_data()  # Recarregar dados

        except Exception as e:
            messagebox.showerror("Erro", f"Erro ao restaurar backup: {str(e)}")

    # Métodos implementados para funcionalidades completas
    def show_wallets(self):
        """Mostra gerenciamento de carteiras"""
        self.notebook.select(1)  # Selecionar aba de carteiras

    def new_wallet(self):
        """Cria nova carteira"""
        try:
            from src.gui.wallet_dialog import WalletDialog
            dialog = WalletDialog(self.root, self.db_manager, self.current_user['id'])
            if dialog.result:
                self.load_wallets()
                self.update_summary_cards()
        except Exception as e:
            messagebox.showerror("Erro", f"Erro ao abrir formulário de carteira: {str(e)}")

    def edit_wallet(self):
        """Edita carteira selecionada"""
        selection = self.wallets_tree.selection()
        if not selection:
            messagebox.showwarning("Aviso", "Selecione uma carteira para editar")
            return

        # Obter ID da carteira selecionada
        item = selection[0]
        wallet_name = self.wallets_tree.item(item)['values'][0]

        # Buscar carteira no banco
        query = "SELECT id FROM wallets WHERE user_id = ? AND name = ?"
        result = self.db_manager.execute_query(query, (self.current_user['id'], wallet_name))

        if result:
            wallet_id = result[0]['id']
            from src.gui.wallet_dialog import WalletDialog
            dialog = WalletDialog(self.root, self.db_manager, self.current_user['id'], wallet_id)
            if form.result:
                self.load_wallets()
                self.update_summary_cards()

    def delete_wallet(self):
        """Exclui carteira selecionada"""
        selection = self.wallets_tree.selection()
        if not selection:
            messagebox.showwarning("Aviso", "Selecione uma carteira para excluir")
            return

        if messagebox.askyesno("Confirmar", "Deseja realmente excluir esta carteira?"):
            item = selection[0]
            wallet_name = self.wallets_tree.item(item)['values'][0]

            try:
                from src.modules.wallet_manager import WalletManager
                wallet_manager = WalletManager(self.db_manager)

                # Buscar ID da carteira
                query = "SELECT id FROM wallets WHERE user_id = ? AND name = ?"
                result = self.db_manager.execute_query(query, (self.current_user['id'], wallet_name))

                if result:
                    wallet_id = result[0]['id']
                    wallet_manager.delete_wallet(wallet_id, self.current_user['id'])
                    messagebox.showinfo("Sucesso", "Carteira excluída com sucesso!")
                    self.load_wallets()
                    self.update_summary_cards()

            except Exception as e:
                messagebox.showerror("Erro", str(e))

    def recalculate_wallet_balances(self):
        """Recalcula saldos de todas as carteiras"""
        try:
            if messagebox.askyesno("Confirmar",
                "Deseja recalcular os saldos de todas as carteiras?\n\n"
                "Esta operação irá:\n"
                "• Recalcular o saldo atual baseado nas transações pagas\n"
                "• Corrigir possíveis inconsistências\n"
                "• Atualizar a exibição das carteiras"):

                from src.modules.transaction_manager import TransactionManager
                transaction_manager = TransactionManager(self.db_manager)

                # Recalcular saldos de todas as carteiras
                transaction_manager.recalculate_all_wallet_balances(self.current_user['id'])

                # Recarregar dados
                self.load_wallets()
                self.update_summary_cards()

                messagebox.showinfo("Sucesso",
                    "Saldos das carteiras recalculados com sucesso!\n\n"
                    "Os saldos agora refletem corretamente as transações pagas.")

        except Exception as e:
            messagebox.showerror("Erro", f"Erro ao recalcular saldos: {str(e)}")

    def new_income(self):
        """Cria nova receita"""
        try:
            # Importar o formulário correto de transação
            from src.gui.transaction_form_new import TransactionFormNew
            form = TransactionFormNew(self.root, self.db_manager, self.current_user['id'], 'income')
            self.root.wait_window(form.window)
            if form.result:
                self.load_transactions()
                self.load_recent_transactions()
                self.update_summary_cards()
        except Exception as e:
            messagebox.showerror("Erro", f"Erro ao abrir formulário de receita: {str(e)}")

    def new_expense(self):
        """Cria nova despesa"""
        try:
            # Importar o formulário correto de transação
            from src.gui.transaction_form_new import TransactionFormNew
            form = TransactionFormNew(self.root, self.db_manager, self.current_user['id'], 'expense')
            self.root.wait_window(form.window)
            if form.result:
                self.load_transactions()
                self.load_recent_transactions()
                self.update_summary_cards()
        except Exception as e:
            messagebox.showerror("Erro", f"Erro ao abrir formulário de despesa: {str(e)}")

    def edit_transaction(self):
        """Edita transação selecionada"""
        selection = self.transactions_tree.selection()
        if not selection:
            messagebox.showwarning("Aviso", "Selecione uma transação para editar")
            return

        try:
            # Obter ID da transação das tags
            item = selection[0]
            tags = self.transactions_tree.item(item)['tags']

            # Encontrar o ID da transação nas tags (pode ser string ou int)
            transaction_id = None
            for tag in tags:
                if isinstance(tag, int):
                    transaction_id = tag
                    break
                elif isinstance(tag, str) and tag.isdigit():
                    transaction_id = int(tag)
                    break

            if not transaction_id:
                messagebox.showerror("Erro", "Não foi possível identificar a transação selecionada")
                return

            # Buscar dados completos da transação
            from src.modules.transaction_manager import TransactionManager
            transaction_manager = TransactionManager(self.db_manager)

            transaction_data = transaction_manager.get_transaction_by_id(transaction_id, self.current_user['id'])

            if not transaction_data:
                messagebox.showerror("Erro", "Transação não encontrada")
                return

            # Abrir formulário de edição
            from src.gui.transaction_form_new import TransactionFormNew

            form = TransactionFormNew(
                self.root,
                self.db_manager,
                self.current_user['id'],
                transaction_data['transaction_type'],
                transaction_id  # Passar ID para modo de edição
            )

            # Aguardar fechamento da janela
            self.root.wait_window(form.window)

            # Se a transação foi editada com sucesso, recarregar a lista
            if form.result:
                self.load_transactions()
                self.update_summary_cards()
                self.load_wallets()

        except Exception as e:
            messagebox.showerror("Erro", f"Erro ao editar transação: {str(e)}")

    def delete_transaction(self):
        """Exclui transação selecionada"""
        selection = self.transactions_tree.selection()
        if not selection:
            messagebox.showwarning("Aviso", "Selecione uma transação para excluir")
            return

        try:
            # Obter ID da transação das tags
            item = selection[0]
            tags = self.transactions_tree.item(item)['tags']

            # Encontrar o ID da transação nas tags (pode ser string ou int)
            transaction_id = None
            for tag in tags:
                if isinstance(tag, int):
                    transaction_id = tag
                    break
                elif isinstance(tag, str) and tag.isdigit():
                    transaction_id = int(tag)
                    break

            if not transaction_id:
                messagebox.showerror("Erro", "Não foi possível identificar a transação selecionada")
                return

            # Obter descrição da transação para confirmação
            values = self.transactions_tree.item(item)['values']
            transaction_description = values[1]  # Descrição está na segunda coluna
            transaction_amount = values[4]  # Valor está na quinta coluna

            # Confirmar exclusão
            if messagebox.askyesno("Confirmar Exclusão",
                f"Tem certeza que deseja excluir a transação:\n\n"
                f"Descrição: {transaction_description}\n"
                f"Valor: {transaction_amount}\n\n"
                f"Esta ação não pode ser desfeita."):

                # Excluir transação
                from src.modules.transaction_manager import TransactionManager
                transaction_manager = TransactionManager(self.db_manager)

                if transaction_manager.delete_transaction(transaction_id, self.current_user['id']):
                    messagebox.showinfo("Sucesso", "Transação excluída com sucesso!")

                    # Recarregar dados
                    self.load_transactions()
                    self.update_summary_cards()
                    self.load_wallets()
                else:
                    messagebox.showerror("Erro", "Erro ao excluir transação")

        except Exception as e:
            messagebox.showerror("Erro", f"Erro ao excluir transação: {str(e)}")

    def show_transaction_context_menu(self, event):
        """Mostra menu de contexto para transações"""
        # Selecionar item clicado
        item = self.transactions_tree.identify_row(event.y)
        if item:
            self.transactions_tree.selection_set(item)
            self.transaction_context_menu.post(event.x_root, event.y_root)

    def mark_transaction_as_paid(self):
        """Marca transação selecionada como paga"""
        selection = self.transactions_tree.selection()
        if not selection:
            messagebox.showwarning("Aviso", "Selecione uma transação")
            return

        try:
            # Obter ID da transação das tags
            item = selection[0]
            tags = self.transactions_tree.item(item, 'tags')

            # Encontrar o ID da transação nas tags
            transaction_id = None
            for tag in tags:
                if tag.isdigit():
                    transaction_id = int(tag)
                    break

            if not transaction_id:
                messagebox.showerror("Erro", "ID da transação não encontrado")
                return

            # Marcar como paga
            from src.modules.transaction_manager import TransactionManager
            transaction_manager = TransactionManager(self.db_manager)

            if transaction_manager.mark_as_paid(transaction_id, self.current_user['id']):
                messagebox.showinfo("Sucesso", "Transação marcada como paga!")

                # Recarregar dados
                self.load_transactions()
                self.update_summary_cards()
                self.update_cashflow_report()
                self.load_wallets()
            else:
                messagebox.showerror("Erro", "Erro ao marcar transação como paga")

        except Exception as e:
            messagebox.showerror("Erro", f"Erro ao marcar como paga: {str(e)}")

    def mark_transaction_as_pending(self):
        """Marca transação selecionada como pendente"""
        selection = self.transactions_tree.selection()
        if not selection:
            messagebox.showwarning("Aviso", "Selecione uma transação")
            return

        try:
            # Obter ID da transação das tags
            item = selection[0]
            tags = self.transactions_tree.item(item, 'tags')

            # Encontrar o ID da transação nas tags
            transaction_id = None
            for tag in tags:
                if tag.isdigit():
                    transaction_id = int(tag)
                    break

            if not transaction_id:
                messagebox.showerror("Erro", "ID da transação não encontrado")
                return

            # Marcar como pendente
            from src.modules.transaction_manager import TransactionManager
            transaction_manager = TransactionManager(self.db_manager)

            if transaction_manager.mark_as_unpaid(transaction_id, self.current_user['id']):
                messagebox.showinfo("Sucesso", "Transação marcada como pendente!")

                # Recarregar dados
                self.load_transactions()
                self.update_summary_cards()
                self.update_cashflow_report()
                self.load_wallets()
            else:
                messagebox.showerror("Erro", "Erro ao marcar transação como pendente")

        except Exception as e:
            messagebox.showerror("Erro", f"Erro ao marcar como pendente: {str(e)}")

    def filter_transactions(self):
        """Aplica filtros às transações"""
        self.load_transactions()

    def show_transactions(self):
        """Mostra aba de transações"""
        self.notebook.select(2)  # Selecionar aba de transações

    def show_category_report(self):
        """Mostra relatório por categoria"""
        self.notebook.select(3)  # Selecionar aba de relatórios
        # Selecionar sub-aba de categoria se existir
        if hasattr(self, 'reports_notebook'):
            self.reports_notebook.select(0)  # Primeira aba (Por Categoria)

    def show_cashflow_report(self):
        """Mostra relatório de fluxo de caixa"""
        self.notebook.select(3)  # Selecionar aba de relatórios
        # Selecionar sub-aba de fluxo de caixa se existir
        if hasattr(self, 'reports_notebook'):
            self.reports_notebook.select(1)  # Segunda aba (Fluxo de Caixa)

    def show_due_bills(self):
        """Mostra contas a vencer"""
        self.notebook.select(3)  # Selecionar aba de relatórios
        # Selecionar sub-aba de contas a vencer se existir
        if hasattr(self, 'reports_notebook'):
            self.reports_notebook.select(2)  # Terceira aba (Contas a Vencer)

    def manage_categories(self):
        """Gerencia categorias"""
        try:
            # Criar janela de gerenciamento de categorias
            category_window = tk.Toplevel(self.root)
            category_window.title("Gerenciar Categorias")
            category_window.geometry("600x400")
            category_window.transient(self.root)
            category_window.grab_set()

            # Frame principal
            main_frame = ttk.Frame(category_window)
            main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

            # Título
            ttk.Label(main_frame, text="Gerenciar Categorias",
                     style='Title.TLabel').pack(pady=(0, 10))

            # Frame para botões
            button_frame = ttk.Frame(main_frame)
            button_frame.pack(fill=tk.X, pady=(0, 10))

            ttk.Button(button_frame, text="Nova Categoria",
                      command=lambda: self.add_category(category_tree)).pack(side=tk.LEFT, padx=(0, 5))
            ttk.Button(button_frame, text="Editar",
                      command=lambda: self.edit_category(category_tree)).pack(side=tk.LEFT, padx=(0, 5))
            ttk.Button(button_frame, text="Excluir",
                      command=lambda: self.delete_category(category_tree)).pack(side=tk.LEFT, padx=(0, 5))
            ttk.Button(button_frame, text="Atualizar",
                      command=lambda: self.load_categories_list(category_tree)).pack(side=tk.LEFT, padx=(0, 5))

            # Treeview para categorias
            columns = ('Nome', 'Tipo', 'Descrição', 'Cor', 'Status')
            category_tree = ttk.Treeview(main_frame, columns=columns, show='headings')

            for col in columns:
                category_tree.heading(col, text=col)
                category_tree.column(col, width=100)

            # Scrollbar
            scrollbar = ttk.Scrollbar(main_frame, orient=tk.VERTICAL, command=category_tree.yview)
            category_tree.configure(yscrollcommand=scrollbar.set)

            # Pack
            category_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
            scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

            # Carregar categorias
            self.load_categories_list(category_tree)

            # Botão fechar
            ttk.Button(main_frame, text="Fechar",
                      command=category_window.destroy).pack(pady=(10, 0))

        except Exception as e:
            messagebox.showerror("Erro", f"Erro ao abrir gerenciamento de categorias: {str(e)}")

    def load_categories_list(self, tree):
        """Carrega lista de categorias"""
        try:
            # Limpar árvore
            for item in tree.get_children():
                tree.delete(item)

            # Buscar categorias
            query = """
                SELECT id, name, category_type, description, color, is_active
                FROM categories
                WHERE user_id = ? OR user_id IS NULL
                ORDER BY category_type, name
            """
            categories = self.db_manager.execute_query(query, (self.current_user['id'],))

            for category in categories:
                status = "Ativa" if category['is_active'] else "Inativa"
                tipo = "Receita" if category['category_type'] == 'income' else "Despesa"

                tree.insert('', 'end', values=(
                    category['name'],
                    tipo,
                    category['description'] or '',
                    category['color'] or '#007ACC',
                    status
                ), tags=(str(category['id']),))

        except Exception as e:
            messagebox.showerror("Erro", f"Erro ao carregar categorias: {str(e)}")

    def add_category(self, tree):
        """Adiciona nova categoria"""
        try:
            from src.gui.category_dialog import CategoryDialog

            dialog = CategoryDialog(self.root, self.db_manager, self.current_user['id'])

            # Se categoria foi criada com sucesso, atualizar lista
            if dialog.result:
                self.load_categories_list(tree)

        except Exception as e:
            messagebox.showerror("Erro", f"Erro ao abrir diálogo de categoria: {str(e)}")

    def edit_category(self, tree):
        """Edita categoria selecionada"""
        selection = tree.selection()
        if not selection:
            messagebox.showwarning("Aviso", "Selecione uma categoria para editar")
            return

        try:
            # Obter ID da categoria selecionada
            item = selection[0]
            category_id = tree.item(item)['tags'][0]

            # Verificar se é categoria do usuário (não padrão do sistema)
            check_query = "SELECT user_id FROM categories WHERE id = ?"
            result = self.db_manager.execute_query(check_query, (category_id,))

            if not result or result[0]['user_id'] is None:
                messagebox.showwarning("Aviso", "Não é possível editar categorias padrão do sistema")
                return

            from src.gui.category_dialog import CategoryDialog

            dialog = CategoryDialog(self.root, self.db_manager, self.current_user['id'], category_id)

            # Se categoria foi editada com sucesso, atualizar lista
            if dialog.result:
                self.load_categories_list(tree)

        except Exception as e:
            messagebox.showerror("Erro", f"Erro ao editar categoria: {str(e)}")

    def delete_category(self, tree):
        """Exclui categoria selecionada"""
        selection = tree.selection()
        if not selection:
            messagebox.showwarning("Aviso", "Selecione uma categoria para excluir")
            return

        if messagebox.askyesno("Confirmar", "Tem certeza que deseja excluir esta categoria?"):
            try:
                item = selection[0]
                category_id = tree.item(item)['tags'][0]

                # Verificar se categoria está em uso
                usage_query = "SELECT COUNT(*) FROM transactions WHERE category_id = ?"
                usage_count = self.db_manager.execute_query(usage_query, (category_id,))[0][0]

                if usage_count > 0:
                    messagebox.showwarning("Aviso",
                        f"Esta categoria não pode ser excluída pois está sendo usada em {usage_count} transação(ões).\n"
                        "Você pode desativá-la em vez de excluir.")
                    return

                # Excluir categoria
                delete_query = "DELETE FROM categories WHERE id = ? AND user_id = ?"
                self.db_manager.execute_query(delete_query, (category_id, self.current_user['id']))

                # Atualizar lista
                self.load_categories_list(tree)
                messagebox.showinfo("Sucesso", "Categoria excluída com sucesso!")

            except Exception as e:
                messagebox.showerror("Erro", f"Erro ao excluir categoria: {str(e)}")

    def show_preferences(self):
        """Mostra preferências do sistema"""
        # Navegar para a aba de configurações
        self.notebook.select(4)  # Aba de Configurações (índice pode variar)

    def show_categories_manager(self):
        """Mostra gerenciador de categorias"""
        self.manage_categories()

    def import_default_categories(self):
        """Importa categorias padrão"""
        try:
            if messagebox.askyesno("Confirmar",
                "Isso irá criar categorias padrão para receitas e despesas.\n"
                "Categorias existentes não serão afetadas.\n\n"
                "Deseja continuar?"):

                # Criar categorias padrão para o usuário atual
                default_categories = [
                    # Receitas
                    ('Salário', 'income', 'Salário mensal', '#28a745'),
                    ('Freelance', 'income', 'Trabalhos extras', '#17a2b8'),
                    ('Investimentos', 'income', 'Rendimentos de investimentos', '#ffc107'),
                    ('Vendas', 'income', 'Vendas de produtos/serviços', '#20c997'),

                    # Despesas
                    ('Alimentação', 'expense', 'Gastos com comida', '#dc3545'),
                    ('Transporte', 'expense', 'Gastos com transporte', '#fd7e14'),
                    ('Moradia', 'expense', 'Aluguel, condomínio, etc.', '#6f42c1'),
                    ('Saúde', 'expense', 'Gastos médicos', '#e83e8c'),
                    ('Educação', 'expense', 'Cursos, livros, etc.', '#20c997'),
                    ('Lazer', 'expense', 'Entretenimento', '#0dcaf0'),
                    ('Utilidades', 'expense', 'Luz, água, internet, etc.', '#6c757d'),
                ]

                created_count = 0
                for name, category_type, description, color in default_categories:
                    # Verificar se categoria já existe
                    check_query = """
                        SELECT COUNT(*) FROM categories
                        WHERE user_id = ? AND name = ? AND category_type = ?
                    """
                    exists = self.db_manager.execute_query(check_query,
                        (self.current_user['id'], name, category_type))[0][0]

                    if not exists:
                        # Criar categoria
                        insert_query = """
                            INSERT INTO categories (user_id, name, category_type, description, color, is_active)
                            VALUES (?, ?, ?, ?, ?, TRUE)
                        """
                        self.db_manager.execute_query(insert_query,
                            (self.current_user['id'], name, category_type, description, color))
                        created_count += 1

                messagebox.showinfo("Sucesso",
                    f"{created_count} categorias padrão foram criadas com sucesso!")

        except Exception as e:
            messagebox.showerror("Erro", f"Erro ao importar categorias padrão: {str(e)}")

    def show_change_password(self):
        """Mostra diálogo para alterar senha"""
        self.change_password()

    def change_password(self):
        """Altera senha do usuário"""
        try:
            # Criar janela de alteração de senha
            password_window = tk.Toplevel(self.root)
            password_window.title("Alterar Senha")
            password_window.geometry("400x300")
            password_window.transient(self.root)
            password_window.grab_set()

            # Frame principal
            main_frame = ttk.Frame(password_window, padding="20")
            main_frame.pack(fill=tk.BOTH, expand=True)

            # Título
            ttk.Label(main_frame, text="Alterar Senha",
                     font=("Arial", 14, "bold")).pack(pady=(0, 20))

            # Campo senha atual
            ttk.Label(main_frame, text="Senha Atual:").pack(anchor=tk.W)
            current_password_entry = ttk.Entry(main_frame, show="*", width=30)
            current_password_entry.pack(pady=(5, 15))

            # Campo nova senha
            ttk.Label(main_frame, text="Nova Senha:").pack(anchor=tk.W)
            new_password_entry = ttk.Entry(main_frame, show="*", width=30)
            new_password_entry.pack(pady=(5, 15))

            # Campo confirmar senha
            ttk.Label(main_frame, text="Confirmar Nova Senha:").pack(anchor=tk.W)
            confirm_password_entry = ttk.Entry(main_frame, show="*", width=30)
            confirm_password_entry.pack(pady=(5, 20))

            # Função para salvar
            def save_password():
                try:
                    current_pwd = current_password_entry.get()
                    new_pwd = new_password_entry.get()
                    confirm_pwd = confirm_password_entry.get()

                    # Validações
                    if not current_pwd:
                        messagebox.showerror("Erro", "Digite a senha atual")
                        return

                    if not new_pwd:
                        messagebox.showerror("Erro", "Digite a nova senha")
                        return

                    if len(new_pwd) < 6:
                        messagebox.showerror("Erro", "A nova senha deve ter pelo menos 6 caracteres")
                        return

                    if new_pwd != confirm_pwd:
                        messagebox.showerror("Erro", "As senhas não coincidem")
                        return

                    # Alterar senha usando o método correto
                    self.auth_manager.change_password(self.current_user['id'], current_pwd, new_pwd)
                    messagebox.showinfo("Sucesso", "Senha alterada com sucesso!")
                    password_window.destroy()

                except Exception as e:
                    messagebox.showerror("Erro", f"Erro ao alterar senha: {str(e)}")

            # Botões
            button_frame = ttk.Frame(main_frame)
            button_frame.pack(pady=(10, 0))

            ttk.Button(button_frame, text="Salvar", command=save_password).pack(side=tk.LEFT, padx=(0, 10))
            ttk.Button(button_frame, text="Cancelar", command=password_window.destroy).pack(side=tk.LEFT)

            # Focar no primeiro campo
            current_password_entry.focus()

        except Exception as e:
            messagebox.showerror("Erro", f"Erro ao abrir alteração de senha: {str(e)}")

    def show_edit_profile(self):
        """Mostra diálogo para editar perfil"""
        try:
            # Criar janela de edição de perfil
            profile_window = tk.Toplevel(self.root)
            profile_window.title("Editar Perfil")
            profile_window.geometry("400x350")
            profile_window.transient(self.root)
            profile_window.grab_set()

            # Frame principal
            main_frame = ttk.Frame(profile_window, padding="20")
            main_frame.pack(fill=tk.BOTH, expand=True)

            # Título
            ttk.Label(main_frame, text="Editar Perfil",
                     font=("Arial", 14, "bold")).pack(pady=(0, 20))

            # Campo nome completo
            ttk.Label(main_frame, text="Nome Completo:").pack(anchor=tk.W)
            full_name_entry = ttk.Entry(main_frame, width=40)
            full_name_entry.insert(0, self.current_user['full_name'])
            full_name_entry.pack(pady=(5, 15))

            # Campo email
            ttk.Label(main_frame, text="Email:").pack(anchor=tk.W)
            email_entry = ttk.Entry(main_frame, width=40)
            email_entry.insert(0, self.current_user['email'])
            email_entry.pack(pady=(5, 15))

            # Campo usuário (somente leitura)
            ttk.Label(main_frame, text="Usuário:").pack(anchor=tk.W)
            username_entry = ttk.Entry(main_frame, width=40, state="readonly")
            username_entry.insert(0, self.current_user['username'])
            username_entry.pack(pady=(5, 20))

            # Função para salvar
            def save_profile():
                try:
                    full_name = full_name_entry.get().strip()
                    email = email_entry.get().strip()

                    # Validações
                    if not full_name:
                        messagebox.showerror("Erro", "Nome completo é obrigatório")
                        return

                    if not email:
                        messagebox.showerror("Erro", "Email é obrigatório")
                        return

                    # Validar formato do email
                    import re
                    if not re.match(r'^[^@]+@[^@]+\.[^@]+$', email):
                        messagebox.showerror("Erro", "Formato de email inválido")
                        return

                    # Atualizar perfil
                    update_query = """
                        UPDATE users
                        SET full_name = ?, email = ?, updated_at = CURRENT_TIMESTAMP
                        WHERE id = ?
                    """
                    self.db_manager.execute_query(update_query, (full_name, email, self.current_user['id']))

                    # Atualizar dados do usuário atual
                    self.current_user['full_name'] = full_name
                    self.current_user['email'] = email

                    # Atualizar título da janela
                    self.root.title(f"Sistema de Gestão de Contas - {full_name}")

                    messagebox.showinfo("Sucesso", "Perfil atualizado com sucesso!")
                    profile_window.destroy()

                except Exception as e:
                    messagebox.showerror("Erro", f"Erro ao salvar perfil: {str(e)}")

            # Botões
            button_frame = ttk.Frame(main_frame)
            button_frame.pack(pady=(10, 0))

            ttk.Button(button_frame, text="Salvar", command=save_profile).pack(side=tk.LEFT, padx=(0, 10))
            ttk.Button(button_frame, text="Cancelar", command=profile_window.destroy).pack(side=tk.LEFT)

            # Focar no primeiro campo
            full_name_entry.focus()

        except Exception as e:
            messagebox.showerror("Erro", f"Erro ao abrir edição de perfil: {str(e)}")

    def show_backup_manager(self):
        """Mostra gerenciador de backups"""
        messagebox.showinfo("Em Desenvolvimento", "Gerenciador de backups em desenvolvimento")

    def manage_users(self):
        """Gerencia usuários (apenas admin)"""
        if not self.current_user['is_admin']:
            messagebox.showerror("Erro", "Acesso negado. Apenas administradores podem acessar esta função.")
            return
        self.show_users_manager()

    def show_system_logs(self):
        """Mostra logs do sistema (apenas admin)"""
        if not self.current_user['is_admin']:
            messagebox.showerror("Erro", "Acesso negado. Apenas administradores podem acessar esta função.")
            return

        try:
            # Criar janela de logs
            logs_window = tk.Toplevel(self.root)
            logs_window.title("Logs do Sistema")
            logs_window.geometry("900x600")
            logs_window.transient(self.root)
            logs_window.grab_set()

            # Frame principal
            main_frame = ttk.Frame(logs_window)
            main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

            # Título
            ttk.Label(main_frame, text="Logs do Sistema",
                     font=("Arial", 14, "bold")).pack(pady=(0, 10))

            # Frame para filtros
            filter_frame = ttk.Frame(main_frame)
            filter_frame.pack(fill=tk.X, pady=(0, 10))

            ttk.Label(filter_frame, text="Filtrar por:").pack(side=tk.LEFT, padx=(0, 5))
            filter_combo = ttk.Combobox(filter_frame, values=["Todos", "Login", "Logout", "Transação", "Erro"],
                                       state="readonly", width=15)
            filter_combo.set("Todos")
            filter_combo.pack(side=tk.LEFT, padx=(0, 10))

            ttk.Button(filter_frame, text="Atualizar",
                      command=lambda: self.load_system_logs(logs_text, filter_combo.get()), width=10).pack(side=tk.LEFT, padx=(0, 10))
            ttk.Button(filter_frame, text="Limpar Logs",
                      command=lambda: self.clear_system_logs_dialog(logs_text), width=12).pack(side=tk.LEFT)

            # Área de texto para logs
            logs_frame = ttk.Frame(main_frame)
            logs_frame.pack(fill=tk.BOTH, expand=True)

            logs_text = tk.Text(logs_frame, wrap=tk.WORD, font=("Consolas", 9))
            logs_scrollbar = ttk.Scrollbar(logs_frame, orient=tk.VERTICAL, command=logs_text.yview)
            logs_text.configure(yscrollcommand=logs_scrollbar.set)

            logs_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
            logs_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

            # Carregar logs iniciais
            self.load_system_logs(logs_text, "Todos")

            # Botão fechar
            ttk.Button(main_frame, text="Fechar",
                      command=logs_window.destroy).pack(pady=(10, 0))

        except Exception as e:
            messagebox.showerror("Erro", f"Erro ao abrir logs do sistema: {str(e)}")

    def load_system_logs(self, text_widget, filter_type):
        """Carrega logs do sistema"""
        try:
            # Limpar área de texto
            text_widget.delete(1.0, tk.END)

            # Simular logs (em um sistema real, você leria de um arquivo de log)
            import datetime

            sample_logs = [
                f"[{datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}] INFO: Sistema iniciado",
                f"[{datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}] LOGIN: Usuário '{self.current_user['username']}' fez login",
                f"[{datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}] TRANSACAO: Nova transação criada",
                f"[{datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}] INFO: Backup criado com sucesso",
                f"[{datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}] WARNING: Tentativa de acesso negado",
                f"[{datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}] INFO: Estatísticas do sistema acessadas",
                f"[{datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}] INFO: Manutenção do banco executada",
            ]

            # Filtrar logs se necessário
            filtered_logs = sample_logs
            if filter_type != "Todos":
                filtered_logs = [log for log in sample_logs if filter_type.upper() in log]

            # Exibir logs
            for log in filtered_logs:
                text_widget.insert(tk.END, log + "\n")

            # Rolar para o final
            text_widget.see(tk.END)

        except Exception as e:
            text_widget.insert(tk.END, f"Erro ao carregar logs: {str(e)}\n")

    def clear_system_logs_dialog(self, text_widget):
        """Diálogo para limpar logs do sistema"""
        if messagebox.askyesno("Confirmar", "Tem certeza que deseja limpar todos os logs?"):
            try:
                text_widget.delete(1.0, tk.END)
                text_widget.insert(tk.END, "Logs limpos com sucesso.\n")
                messagebox.showinfo("Sucesso", "Logs limpos com sucesso!")

            except Exception as e:
                messagebox.showerror("Erro", f"Erro ao limpar logs: {str(e)}")

    def show_about(self):
        """Mostra informações sobre o sistema"""
        about_text = """Sistema de Gestão de Contas

Versão: 1.0.0
Desenvolvido em Python com Tkinter

Funcionalidades:
• Gestão de carteiras e contas
• Controle de receitas e despesas
• Relatórios e análises
• Sistema de alertas
• Backup e restauração
• Controle de usuários

© 2024 - Sistema de Gestão Financeira"""

        messagebox.showinfo("Sobre", about_text)

    # Métodos para relatórios
    def update_category_report(self, tree, period):
        """Atualiza relatório por categoria"""
        try:
            # Limpar árvore
            for item in tree.get_children():
                tree.delete(item)

            from src.modules.category_manager import CategoryManager
            category_manager = CategoryManager(self.db_manager)

            # Definir período
            from datetime import datetime, timedelta
            today = datetime.now().date()

            if period == "Este Mês":
                start_date = today.replace(day=1)
                end_date = today
            elif period == "Últimos 3 Meses":
                start_date = today - timedelta(days=90)
                end_date = today
            elif period == "Este Ano":
                start_date = today.replace(month=1, day=1)
                end_date = today
            else:
                start_date = None
                end_date = None

            # Obter estatísticas
            stats = category_manager.get_category_usage_stats(
                self.current_user['id'],
                period_start=start_date,
                period_end=end_date
            )

            for stat in stats:
                trans_type = "Receita" if stat['category_type'] == 'income' else "Despesa"
                tree.insert('', 'end', values=(
                    stat['name'],
                    trans_type,
                    stat['transaction_count'],
                    f"R$ {stat['total_amount']:,.2f}",
                    f"R$ {stat['avg_amount']:,.2f}"
                ))

        except Exception as e:
            print(f"Erro ao atualizar relatório por categoria: {str(e)}")

    def update_cashflow_report(self):
        """Atualiza relatório de fluxo de caixa"""
        try:
            from src.modules.transaction_manager import TransactionManager
            transaction_manager = TransactionManager(self.db_manager)

            # Obter resumo do mês atual
            from datetime import datetime
            today = datetime.now().date()
            start_month = today.replace(day=1)

            summary = transaction_manager.get_transactions_summary(
                self.current_user['id'],
                period_start=start_month,
                period_end=today
            )

            # Atualizar labels - mostrar totais incluindo pendentes
            income_paid = summary['income']['paid_total']
            income_total = summary['income']['total']
            expense_paid = summary['expense']['paid_total']
            expense_total = summary['expense']['total']

            # Calcular saldo baseado apenas em transações pagas
            balance_paid = income_paid - expense_paid
            # Calcular saldo total (incluindo pendentes)
            balance_total = income_total - expense_total

            # Mostrar valores pagos e totais
            self.income_total_label.config(
                text=f"Pago: R$ {income_paid:,.2f}\nTotal: R$ {income_total:,.2f}"
            )
            self.expense_total_label.config(
                text=f"Pago: R$ {expense_paid:,.2f}\nTotal: R$ {expense_total:,.2f}"
            )

            # Mostrar saldo baseado em transações pagas
            color = 'green' if balance_paid >= 0 else 'red'
            self.balance_total_label.config(
                text=f"Saldo Pago: R$ {balance_paid:,.2f}\nSaldo Total: R$ {balance_total:,.2f}",
                foreground=color
            )

            # Atualizar lista mensal
            self.update_monthly_cashflow()

        except Exception as e:
            print(f"Erro ao atualizar fluxo de caixa: {str(e)}")

    def update_monthly_cashflow(self):
        """Atualiza lista mensal de fluxo de caixa"""
        try:
            # Limpar árvore
            for item in self.monthly_cashflow_tree.get_children():
                self.monthly_cashflow_tree.delete(item)

            # Obter dados mensais dos últimos 12 meses
            from datetime import datetime, timedelta
            from dateutil.relativedelta import relativedelta

            today = datetime.now().date()

            # Calcular 12 meses atrás
            start_date = today - relativedelta(months=11)
            start_date = start_date.replace(day=1)  # Primeiro dia do mês

            monthly_data = self.get_monthly_cashflow_data(start_date, today)

            # Configurar tags de cores para os ícones
            self.monthly_cashflow_tree.tag_configure('positive', foreground='#27ae60', font=('Arial', 9, 'bold'))
            self.monthly_cashflow_tree.tag_configure('negative', foreground='#e74c3c', font=('Arial', 9, 'bold'))
            self.monthly_cashflow_tree.tag_configure('neutral', foreground='#f39c12', font=('Arial', 9, 'bold'))

            # Adicionar dados à árvore
            for month_data in monthly_data:
                month_str = month_data['month_str']
                income = month_data['income']
                expense = month_data['expense']
                balance = income - expense

                # Determinar status e ícone
                if balance > 1000:
                    status_icon = "🟢 Excelente"  # Verde para muito positivo
                    tag = 'positive'
                elif balance > 0:
                    status_icon = "🟢 Positivo"   # Verde para positivo
                    tag = 'positive'
                elif balance > -500:
                    status_icon = "🟡 Atenção"    # Amarelo para levemente negativo
                    tag = 'neutral'
                elif balance > -1000:
                    status_icon = "🟠 Cuidado"    # Laranja para negativo
                    tag = 'negative'
                else:
                    status_icon = "🔴 Crítico"    # Vermelho para muito negativo
                    tag = 'negative'

                # Inserir item na árvore
                item = self.monthly_cashflow_tree.insert('', 'end', values=(
                    month_str,
                    f"R$ {income:,.2f}",
                    f"R$ {expense:,.2f}",
                    f"R$ {balance:,.2f}",
                    status_icon
                ), tags=(tag,))

        except Exception as e:
            print(f"Erro ao atualizar fluxo mensal: {str(e)}")

    def get_monthly_cashflow_data(self, start_date, end_date):
        """Obtém dados de fluxo de caixa agrupados por mês"""
        try:
            from src.modules.transaction_manager import TransactionManager
            from datetime import datetime
            from dateutil.relativedelta import relativedelta
            import locale

            # Configurar locale para português
            try:
                locale.setlocale(locale.LC_TIME, 'pt_BR.UTF-8')
            except:
                try:
                    locale.setlocale(locale.LC_TIME, 'Portuguese_Brazil.1252')
                except:
                    pass  # Usar padrão se não conseguir configurar

            transaction_manager = TransactionManager(self.db_manager)
            monthly_data = []

            # Iterar pelos meses
            current_date = start_date
            while current_date <= end_date:
                # Calcular primeiro e último dia do mês
                first_day = current_date.replace(day=1)
                if current_date.month == 12:
                    last_day = current_date.replace(year=current_date.year + 1, month=1, day=1) - timedelta(days=1)
                else:
                    last_day = current_date.replace(month=current_date.month + 1, day=1) - timedelta(days=1)

                # Obter resumo do mês
                summary = transaction_manager.get_transactions_summary(
                    self.current_user['id'],
                    period_start=first_day,
                    period_end=last_day
                )

                # Formatar nome do mês
                try:
                    month_str = current_date.strftime("%B %Y").title()
                except:
                    month_names = [
                        "Janeiro", "Fevereiro", "Março", "Abril", "Maio", "Junho",
                        "Julho", "Agosto", "Setembro", "Outubro", "Novembro", "Dezembro"
                    ]
                    month_str = f"{month_names[current_date.month - 1]} {current_date.year}"

                monthly_data.append({
                    'month_str': month_str,
                    'income': summary['income']['total'],
                    'expense': summary['expense']['total'],
                    'date': current_date
                })

                # Próximo mês
                current_date = current_date + relativedelta(months=1)

            # Ordenar por data (mais recente primeiro)
            monthly_data.sort(key=lambda x: x['date'], reverse=True)

            return monthly_data

        except Exception as e:
            print(f"Erro ao obter dados mensais: {str(e)}")
            return []

    def update_due_bills(self, tree, period):
        """Atualiza contas a vencer"""
        try:
            # Limpar árvore
            for item in tree.get_children():
                tree.delete(item)

            from src.modules.alert_manager import AlertManager
            alert_manager = AlertManager(self.db_manager)

            # Extrair número de dias
            days = int(period.split()[0])

            # Obter alertas de vencimento
            alerts = alert_manager.get_due_bills_alerts(self.current_user['id'], days)

            for alert in alerts:
                status = alert['alert_type']

                # Formatação da data de vencimento
                due_date_str = ""
                if alert['due_date']:
                    due_date_str = datetime.strptime(alert['due_date'], '%Y-%m-%d').strftime('%d/%m/%Y')

                tree.insert('', 'end', values=(
                    due_date_str,
                    alert['transaction_description'],  # Usar descrição original da transação
                    alert['category'],
                    f"R$ {alert['amount']:,.2f}",
                    status
                ))

        except Exception as e:
            print(f"Erro ao atualizar contas a vencer: {str(e)}")

    # Métodos para configurações


    def import_default_categories(self):
        """Importa categorias padrão"""
        try:
            from src.modules.category_manager import CategoryManager
            category_manager = CategoryManager(self.db_manager)

            count = category_manager.copy_default_categories_to_user(self.current_user['id'])
            messagebox.showinfo("Sucesso", f"{count} categorias importadas com sucesso!")

        except Exception as e:
            messagebox.showerror("Erro", str(e))





    def show_backup_manager(self):
        """Mostra gerenciador de backups"""
        messagebox.showinfo("Em Desenvolvimento", "Gerenciador de backups em desenvolvimento")

    # Métodos para administração
    def show_users_manager(self):
        """Mostra gerenciador de usuários"""
        try:
            # Criar janela de gerenciamento de usuários
            users_window = tk.Toplevel(self.root)
            users_window.title("Gerenciar Usuários")
            users_window.geometry("800x600")
            users_window.transient(self.root)
            users_window.grab_set()

            # Frame principal
            main_frame = ttk.Frame(users_window)
            main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

            # Título
            ttk.Label(main_frame, text="Gerenciar Usuários",
                     style='Title.TLabel').pack(pady=(0, 10))

            # Frame para botões
            button_frame = ttk.Frame(main_frame)
            button_frame.pack(fill=tk.X, pady=(0, 10))

            ttk.Button(button_frame, text="Novo Usuário",
                      command=lambda: self.create_user_dialog(users_tree)).pack(side=tk.LEFT, padx=(0, 5))
            ttk.Button(button_frame, text="Editar",
                      command=lambda: self.edit_user_dialog(users_tree)).pack(side=tk.LEFT, padx=(0, 5))
            ttk.Button(button_frame, text="Ativar/Desativar",
                      command=lambda: self.toggle_user_status(users_tree)).pack(side=tk.LEFT, padx=(0, 5))
            ttk.Button(button_frame, text="Resetar Senha",
                      command=lambda: self.reset_user_password(users_tree)).pack(side=tk.LEFT, padx=(0, 5))
            ttk.Button(button_frame, text="Atualizar",
                      command=lambda: self.load_users_list(users_tree)).pack(side=tk.LEFT, padx=(0, 5))

            # Treeview para usuários
            columns = ('ID', 'Usuário', 'Nome Completo', 'Email', 'Admin', 'Status', 'Criado em')
            users_tree = ttk.Treeview(main_frame, columns=columns, show='headings')

            # Configurar colunas
            users_tree.column('ID', width=50)
            users_tree.column('Usuário', width=100)
            users_tree.column('Nome Completo', width=150)
            users_tree.column('Email', width=150)
            users_tree.column('Admin', width=60)
            users_tree.column('Status', width=80)
            users_tree.column('Criado em', width=120)

            for col in columns:
                users_tree.heading(col, text=col)

            # Scrollbar
            scrollbar = ttk.Scrollbar(main_frame, orient=tk.VERTICAL, command=users_tree.yview)
            users_tree.configure(yscrollcommand=scrollbar.set)

            # Pack
            users_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
            scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

            # Carregar usuários
            self.load_users_list(users_tree)

            # Botão fechar
            ttk.Button(main_frame, text="Fechar",
                      command=users_window.destroy).pack(pady=(10, 0))

        except Exception as e:
            messagebox.showerror("Erro", f"Erro ao abrir gerenciamento de usuários: {str(e)}")

    def load_users_list(self, tree):
        """Carrega lista de usuários"""
        try:
            # Limpar árvore
            for item in tree.get_children():
                tree.delete(item)

            # Buscar usuários
            query = """
                SELECT id, username, full_name, email, is_admin, is_active, created_at
                FROM users
                ORDER BY created_at DESC
            """
            users = self.db_manager.execute_query(query)

            for user in users:
                admin_status = "Sim" if user['is_admin'] else "Não"
                status = "Ativo" if user['is_active'] else "Inativo"
                created_date = user['created_at'][:10] if user['created_at'] else ""

                tree.insert('', 'end', values=(
                    user['id'],
                    user['username'],
                    user['full_name'],
                    user['email'],
                    admin_status,
                    status,
                    created_date
                ), tags=(str(user['id']),))

        except Exception as e:
            messagebox.showerror("Erro", f"Erro ao carregar usuários: {str(e)}")

    def create_user_dialog(self, tree):
        """Cria diálogo para novo usuário"""
        try:
            # Criar janela de novo usuário
            user_window = tk.Toplevel(self.root)
            user_window.title("Novo Usuário")
            user_window.geometry("400x450")
            user_window.transient(self.root)
            user_window.grab_set()

            # Frame principal
            main_frame = ttk.Frame(user_window, padding="20")
            main_frame.pack(fill=tk.BOTH, expand=True)

            # Título
            ttk.Label(main_frame, text="Criar Novo Usuário",
                     font=("Arial", 14, "bold")).pack(pady=(0, 20))

            # Campos
            ttk.Label(main_frame, text="Nome de Usuário:").pack(anchor=tk.W)
            username_entry = ttk.Entry(main_frame, width=40)
            username_entry.pack(pady=(5, 15))

            ttk.Label(main_frame, text="Nome Completo:").pack(anchor=tk.W)
            full_name_entry = ttk.Entry(main_frame, width=40)
            full_name_entry.pack(pady=(5, 15))

            ttk.Label(main_frame, text="Email:").pack(anchor=tk.W)
            email_entry = ttk.Entry(main_frame, width=40)
            email_entry.pack(pady=(5, 15))

            ttk.Label(main_frame, text="Senha:").pack(anchor=tk.W)
            password_entry = ttk.Entry(main_frame, show="*", width=40)
            password_entry.pack(pady=(5, 15))

            ttk.Label(main_frame, text="Confirmar Senha:").pack(anchor=tk.W)
            confirm_password_entry = ttk.Entry(main_frame, show="*", width=40)
            confirm_password_entry.pack(pady=(5, 15))

            # Checkbox admin
            is_admin_var = tk.BooleanVar()
            ttk.Checkbutton(main_frame, text="Usuário Administrador",
                           variable=is_admin_var).pack(pady=(5, 20))

            # Função para salvar
            def save_user():
                try:
                    username = username_entry.get().strip()
                    full_name = full_name_entry.get().strip()
                    email = email_entry.get().strip()
                    password = password_entry.get()
                    confirm_password = confirm_password_entry.get()
                    is_admin = is_admin_var.get()

                    # Validações
                    if not username:
                        messagebox.showerror("Erro", "Nome de usuário é obrigatório")
                        return

                    if not full_name:
                        messagebox.showerror("Erro", "Nome completo é obrigatório")
                        return

                    if not email:
                        messagebox.showerror("Erro", "Email é obrigatório")
                        return

                    if not password:
                        messagebox.showerror("Erro", "Senha é obrigatória")
                        return

                    if password != confirm_password:
                        messagebox.showerror("Erro", "As senhas não coincidem")
                        return

                    # Criar usuário
                    if self.auth_manager.create_user(username, password, email, is_admin, full_name):
                        messagebox.showinfo("Sucesso", "Usuário criado com sucesso!")
                        self.load_users_list(tree)
                        user_window.destroy()
                    else:
                        messagebox.showerror("Erro", "Erro ao criar usuário")

                except Exception as e:
                    messagebox.showerror("Erro", f"Erro ao criar usuário: {str(e)}")

            # Botões
            button_frame = ttk.Frame(main_frame)
            button_frame.pack(pady=(10, 0))

            ttk.Button(button_frame, text="Salvar", command=save_user).pack(side=tk.LEFT, padx=(0, 10))
            ttk.Button(button_frame, text="Cancelar", command=user_window.destroy).pack(side=tk.LEFT)

            # Focar no primeiro campo
            username_entry.focus()

        except Exception as e:
            messagebox.showerror("Erro", f"Erro ao abrir criação de usuário: {str(e)}")

    def show_create_user(self):
        """Mostra diálogo para criar usuário"""
        # Criar uma árvore temporária para compatibilidade
        temp_tree = None
        self.create_user_dialog(temp_tree)

    def edit_user_dialog(self, tree):
        """Edita usuário selecionado"""
        selection = tree.selection()
        if not selection:
            messagebox.showwarning("Aviso", "Selecione um usuário para editar")
            return

        try:
            item = selection[0]
            user_id = tree.item(item)['tags'][0]

            # Buscar dados do usuário
            query = "SELECT * FROM users WHERE id = ?"
            user_data = self.db_manager.execute_query(query, (user_id,))[0]

            # Criar janela de edição
            edit_window = tk.Toplevel(self.root)
            edit_window.title("Editar Usuário")
            edit_window.geometry("400x400")
            edit_window.transient(self.root)
            edit_window.grab_set()

            # Frame principal
            main_frame = ttk.Frame(edit_window, padding="20")
            main_frame.pack(fill=tk.BOTH, expand=True)

            # Título
            ttk.Label(main_frame, text="Editar Usuário",
                     font=("Arial", 14, "bold")).pack(pady=(0, 20))

            # Campos
            ttk.Label(main_frame, text="Nome de Usuário:").pack(anchor=tk.W)
            username_entry = ttk.Entry(main_frame, width=40, state="readonly")
            username_entry.insert(0, user_data['username'])
            username_entry.pack(pady=(5, 15))

            ttk.Label(main_frame, text="Nome Completo:").pack(anchor=tk.W)
            full_name_entry = ttk.Entry(main_frame, width=40)
            full_name_entry.insert(0, user_data['full_name'])
            full_name_entry.pack(pady=(5, 15))

            ttk.Label(main_frame, text="Email:").pack(anchor=tk.W)
            email_entry = ttk.Entry(main_frame, width=40)
            email_entry.insert(0, user_data['email'])
            email_entry.pack(pady=(5, 15))

            # Checkbox admin
            is_admin_var = tk.BooleanVar(value=user_data['is_admin'])
            ttk.Checkbutton(main_frame, text="Usuário Administrador",
                           variable=is_admin_var).pack(pady=(5, 20))

            # Função para salvar
            def save_changes():
                try:
                    full_name = full_name_entry.get().strip()
                    email = email_entry.get().strip()
                    is_admin = is_admin_var.get()

                    # Validações
                    if not full_name:
                        messagebox.showerror("Erro", "Nome completo é obrigatório")
                        return

                    if not email:
                        messagebox.showerror("Erro", "Email é obrigatório")
                        return

                    # Atualizar usuário
                    update_query = """
                        UPDATE users
                        SET full_name = ?, email = ?, is_admin = ?, updated_at = CURRENT_TIMESTAMP
                        WHERE id = ?
                    """
                    self.db_manager.execute_query(update_query, (full_name, email, is_admin, user_id))

                    messagebox.showinfo("Sucesso", "Usuário atualizado com sucesso!")
                    self.load_users_list(tree)
                    edit_window.destroy()

                except Exception as e:
                    messagebox.showerror("Erro", f"Erro ao atualizar usuário: {str(e)}")

            # Botões
            button_frame = ttk.Frame(main_frame)
            button_frame.pack(pady=(10, 0))

            ttk.Button(button_frame, text="Salvar", command=save_changes).pack(side=tk.LEFT, padx=(0, 10))
            ttk.Button(button_frame, text="Cancelar", command=edit_window.destroy).pack(side=tk.LEFT)

        except Exception as e:
            messagebox.showerror("Erro", f"Erro ao editar usuário: {str(e)}")

    def toggle_user_status(self, tree):
        """Ativa/desativa usuário selecionado"""
        selection = tree.selection()
        if not selection:
            messagebox.showwarning("Aviso", "Selecione um usuário para ativar/desativar")
            return

        try:
            item = selection[0]
            user_id = tree.item(item)['tags'][0]

            # Não permitir desativar o próprio usuário
            if int(user_id) == self.current_user['id']:
                messagebox.showerror("Erro", "Você não pode desativar sua própria conta")
                return

            # Buscar status atual
            query = "SELECT is_active, username FROM users WHERE id = ?"
            user_data = self.db_manager.execute_query(query, (user_id,))[0]

            new_status = not user_data['is_active']
            action = "ativar" if new_status else "desativar"

            if messagebox.askyesno("Confirmar", f"Tem certeza que deseja {action} o usuário '{user_data['username']}'?"):
                # Atualizar status
                update_query = "UPDATE users SET is_active = ? WHERE id = ?"
                self.db_manager.execute_query(update_query, (new_status, user_id))

                messagebox.showinfo("Sucesso", f"Usuário {action}do com sucesso!")
                self.load_users_list(tree)

        except Exception as e:
            messagebox.showerror("Erro", f"Erro ao alterar status do usuário: {str(e)}")

    def reset_user_password(self, tree):
        """Reseta senha do usuário selecionado"""
        selection = tree.selection()
        if not selection:
            messagebox.showwarning("Aviso", "Selecione um usuário para resetar a senha")
            return

        try:
            item = selection[0]
            user_id = tree.item(item)['tags'][0]

            # Buscar dados do usuário
            query = "SELECT username FROM users WHERE id = ?"
            user_data = self.db_manager.execute_query(query, (user_id,))[0]

            # Solicitar nova senha
            new_password = tk.simpledialog.askstring("Nova Senha",
                f"Digite a nova senha para o usuário '{user_data['username']}':", show='*')

            if new_password:
                if len(new_password) < 6:
                    messagebox.showerror("Erro", "A senha deve ter pelo menos 6 caracteres")
                    return

                # Atualizar senha
                new_hash = self.auth_manager.hash_password(new_password)
                update_query = "UPDATE users SET password_hash = ? WHERE id = ?"
                self.db_manager.execute_query(update_query, (new_hash, user_id))

                messagebox.showinfo("Sucesso", "Senha resetada com sucesso!")

        except Exception as e:
            messagebox.showerror("Erro", f"Erro ao resetar senha: {str(e)}")

    def show_system_logs(self):
        """Mostra logs do sistema"""
        if not self.current_user['is_admin']:
            messagebox.showerror("Erro", "Acesso negado. Apenas administradores podem acessar esta função.")
            return

        try:
            # Criar janela de logs
            logs_window = tk.Toplevel(self.root)
            logs_window.title("Logs do Sistema")
            logs_window.geometry("900x600")
            logs_window.transient(self.root)
            logs_window.grab_set()

            # Frame principal
            main_frame = ttk.Frame(logs_window)
            main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

            # Título
            ttk.Label(main_frame, text="Logs do Sistema",
                     style='Title.TLabel').pack(pady=(0, 10))

            # Frame para filtros
            filter_frame = ttk.Frame(main_frame)
            filter_frame.pack(fill=tk.X, pady=(0, 10))

            ttk.Label(filter_frame, text="Filtrar por:").pack(side=tk.LEFT, padx=(0, 5))
            filter_combo = ttk.Combobox(filter_frame, values=["Todos", "Login", "Logout", "Transação", "Erro"],
                                       state="readonly", width=15)
            filter_combo.set("Todos")
            filter_combo.pack(side=tk.LEFT, padx=(0, 10))

            ttk.Button(filter_frame, text="Atualizar",
                      command=lambda: self.load_system_logs(logs_text, filter_combo.get())).pack(side=tk.LEFT, padx=(0, 10))
            ttk.Button(filter_frame, text="Limpar Logs",
                      command=lambda: self.clear_system_logs(logs_text)).pack(side=tk.LEFT)

            # Área de texto para logs
            logs_frame = ttk.Frame(main_frame)
            logs_frame.pack(fill=tk.BOTH, expand=True)

            logs_text = tk.Text(logs_frame, wrap=tk.WORD, font=("Consolas", 9))
            logs_scrollbar = ttk.Scrollbar(logs_frame, orient=tk.VERTICAL, command=logs_text.yview)
            logs_text.configure(yscrollcommand=logs_scrollbar.set)

            logs_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
            logs_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

            # Carregar logs iniciais
            self.load_system_logs(logs_text, "Todos")

            # Botão fechar
            ttk.Button(main_frame, text="Fechar",
                      command=logs_window.destroy).pack(pady=(10, 0))

        except Exception as e:
            messagebox.showerror("Erro", f"Erro ao abrir logs do sistema: {str(e)}")

    def load_system_logs(self, text_widget, filter_type):
        """Carrega logs do sistema"""
        try:
            # Limpar área de texto
            text_widget.delete(1.0, tk.END)

            # Simular logs (em um sistema real, você leria de um arquivo de log)
            import datetime

            sample_logs = [
                f"[{datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}] INFO: Sistema iniciado",
                f"[{datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}] LOGIN: Usuário 'admin' fez login",
                f"[{datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}] TRANSACAO: Nova transação criada - ID: 123",
                f"[{datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}] INFO: Backup criado com sucesso",
                f"[{datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}] WARNING: Tentativa de acesso negado",
            ]

            # Filtrar logs se necessário
            filtered_logs = sample_logs
            if filter_type != "Todos":
                filtered_logs = [log for log in sample_logs if filter_type.upper() in log]

            # Exibir logs
            for log in filtered_logs:
                text_widget.insert(tk.END, log + "\n")

            # Rolar para o final
            text_widget.see(tk.END)

        except Exception as e:
            text_widget.insert(tk.END, f"Erro ao carregar logs: {str(e)}\n")

    def clear_system_logs(self, text_widget=None):
        """Limpa logs do sistema"""
        if messagebox.askyesno("Confirmar", "Tem certeza que deseja limpar todos os logs?"):
            try:
                # Em um sistema real, você limparia o arquivo de log
                if text_widget:
                    text_widget.delete(1.0, tk.END)
                    text_widget.insert(tk.END, "Logs limpos com sucesso.\n")
                else:
                    messagebox.showinfo("Sucesso", "Logs limpos com sucesso!")

            except Exception as e:
                messagebox.showerror("Erro", f"Erro ao limpar logs: {str(e)}")

    def show_system_stats(self):
        """Mostra estatísticas do sistema"""
        try:
            # Criar janela de estatísticas
            stats_window = tk.Toplevel(self.root)
            stats_window.title("Estatísticas do Sistema")
            stats_window.geometry("600x500")
            stats_window.transient(self.root)
            stats_window.grab_set()

            # Frame principal
            main_frame = ttk.Frame(stats_window, padding="20")
            main_frame.pack(fill=tk.BOTH, expand=True)

            # Título
            ttk.Label(main_frame, text="Estatísticas do Sistema",
                     font=("Arial", 14, "bold")).pack(pady=(0, 20))

            # Buscar estatísticas
            stats = self.get_system_statistics()

            # Exibir estatísticas
            stats_frame = ttk.Frame(main_frame)
            stats_frame.pack(fill=tk.BOTH, expand=True)

            row = 0
            for label, value in stats.items():
                ttk.Label(stats_frame, text=f"{label}:", font=("Arial", 10, "bold")).grid(
                    row=row, column=0, sticky=tk.W, padx=(0, 10), pady=5)
                ttk.Label(stats_frame, text=str(value)).grid(
                    row=row, column=1, sticky=tk.W, pady=5)
                row += 1

            # Botão atualizar
            ttk.Button(main_frame, text="Atualizar",
                      command=lambda: self.refresh_system_stats(stats_frame)).pack(pady=(20, 10))

            # Botão fechar
            ttk.Button(main_frame, text="Fechar",
                      command=stats_window.destroy).pack()

        except Exception as e:
            messagebox.showerror("Erro", f"Erro ao mostrar estatísticas: {str(e)}")

    def get_system_statistics(self):
        """Obtém estatísticas do sistema"""
        try:
            stats = {}

            # Estatísticas de usuários
            users_count = self.db_manager.execute_query("SELECT COUNT(*) FROM users")[0][0]
            active_users = self.db_manager.execute_query("SELECT COUNT(*) FROM users WHERE is_active = TRUE")[0][0]
            admin_users = self.db_manager.execute_query("SELECT COUNT(*) FROM users WHERE is_admin = TRUE")[0][0]

            stats["Total de Usuários"] = users_count
            stats["Usuários Ativos"] = active_users
            stats["Administradores"] = admin_users

            # Estatísticas de transações
            transactions_count = self.db_manager.execute_query("SELECT COUNT(*) FROM transactions")[0][0]
            income_count = self.db_manager.execute_query("SELECT COUNT(*) FROM transactions WHERE transaction_type = 'income'")[0][0]
            expense_count = self.db_manager.execute_query("SELECT COUNT(*) FROM transactions WHERE transaction_type = 'expense'")[0][0]

            stats["Total de Transações"] = transactions_count
            stats["Receitas"] = income_count
            stats["Despesas"] = expense_count

            # Estatísticas de carteiras
            wallets_count = self.db_manager.execute_query("SELECT COUNT(*) FROM wallets")[0][0]
            stats["Total de Carteiras"] = wallets_count

            # Estatísticas de categorias
            categories_count = self.db_manager.execute_query("SELECT COUNT(*) FROM categories")[0][0]
            stats["Total de Categorias"] = categories_count

            # Tamanho do banco de dados
            import os
            if os.path.exists("data/gestao_contas.db"):
                db_size = os.path.getsize("data/gestao_contas.db")
                stats["Tamanho do Banco"] = f"{db_size / 1024:.1f} KB"

            return stats

        except Exception as e:
            return {"Erro": str(e)}

    def refresh_system_stats(self, stats_frame):
        """Atualiza estatísticas na tela"""
        try:
            # Limpar frame
            for widget in stats_frame.winfo_children():
                widget.destroy()

            # Buscar novas estatísticas
            stats = self.get_system_statistics()

            # Exibir estatísticas atualizadas
            row = 0
            for label, value in stats.items():
                ttk.Label(stats_frame, text=f"{label}:", font=("Arial", 10, "bold")).grid(
                    row=row, column=0, sticky=tk.W, padx=(0, 10), pady=5)
                ttk.Label(stats_frame, text=str(value)).grid(
                    row=row, column=1, sticky=tk.W, pady=5)
                row += 1

        except Exception as e:
            messagebox.showerror("Erro", f"Erro ao atualizar estatísticas: {str(e)}")

    def show_database_maintenance(self):
        """Mostra opções de manutenção do banco"""
        try:
            # Criar janela de manutenção
            maintenance_window = tk.Toplevel(self.root)
            maintenance_window.title("Manutenção do Banco de Dados")
            maintenance_window.geometry("500x400")
            maintenance_window.transient(self.root)
            maintenance_window.grab_set()

            # Frame principal
            main_frame = ttk.Frame(maintenance_window, padding="20")
            main_frame.pack(fill=tk.BOTH, expand=True)

            # Título
            ttk.Label(main_frame, text="Manutenção do Banco de Dados",
                     font=("Arial", 14, "bold")).pack(pady=(0, 20))

            # Opções de manutenção
            ttk.Button(main_frame, text="Verificar Integridade",
                      command=self.check_database_integrity).pack(fill=tk.X, pady=5)
            ttk.Button(main_frame, text="Otimizar Banco",
                      command=self.optimize_database).pack(fill=tk.X, pady=5)
            ttk.Button(main_frame, text="Limpar Dados Antigos",
                      command=self.cleanup_old_data).pack(fill=tk.X, pady=5)
            ttk.Button(main_frame, text="Reindexar Tabelas",
                      command=self.reindex_database).pack(fill=tk.X, pady=5)

            # Área de resultado
            ttk.Label(main_frame, text="Resultado:").pack(anchor=tk.W, pady=(20, 5))
            result_text = tk.Text(main_frame, height=10, wrap=tk.WORD)
            result_scrollbar = ttk.Scrollbar(main_frame, orient=tk.VERTICAL, command=result_text.yview)
            result_text.configure(yscrollcommand=result_scrollbar.set)

            result_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
            result_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

            # Armazenar referência para uso nos métodos
            self.maintenance_result_text = result_text

            # Botão fechar
            ttk.Button(main_frame, text="Fechar",
                      command=maintenance_window.destroy).pack(pady=(10, 0))

        except Exception as e:
            messagebox.showerror("Erro", f"Erro ao abrir manutenção: {str(e)}")

    def check_database_integrity(self):
        """Verifica integridade do banco"""
        try:
            self.maintenance_result_text.insert(tk.END, "Verificando integridade do banco...\n")

            # Executar PRAGMA integrity_check
            result = self.db_manager.execute_query("PRAGMA integrity_check")

            if result and result[0][0] == "ok":
                self.maintenance_result_text.insert(tk.END, "✅ Integridade do banco: OK\n")
            else:
                self.maintenance_result_text.insert(tk.END, f"❌ Problemas encontrados: {result}\n")

            self.maintenance_result_text.see(tk.END)

        except Exception as e:
            self.maintenance_result_text.insert(tk.END, f"❌ Erro na verificação: {str(e)}\n")

    def optimize_database(self):
        """Otimiza o banco de dados"""
        try:
            self.maintenance_result_text.insert(tk.END, "Otimizando banco de dados...\n")

            # Executar VACUUM
            self.db_manager.execute_query("VACUUM")

            self.maintenance_result_text.insert(tk.END, "✅ Banco otimizado com sucesso\n")
            self.maintenance_result_text.see(tk.END)

        except Exception as e:
            self.maintenance_result_text.insert(tk.END, f"❌ Erro na otimização: {str(e)}\n")

    def cleanup_old_data(self):
        """Limpa dados antigos"""
        try:
            self.maintenance_result_text.insert(tk.END, "Limpando dados antigos...\n")

            # Exemplo: remover transações muito antigas (mais de 2 anos)
            from datetime import datetime, timedelta
            cutoff_date = (datetime.now() - timedelta(days=730)).strftime('%Y-%m-%d')

            old_transactions = self.db_manager.execute_query(
                "SELECT COUNT(*) FROM transactions WHERE transaction_date < ?", (cutoff_date,))[0][0]

            if old_transactions > 0:
                if messagebox.askyesno("Confirmar",
                    f"Encontradas {old_transactions} transações antigas (mais de 2 anos).\n"
                    "Deseja removê-las?"):

                    self.db_manager.execute_query(
                        "DELETE FROM transactions WHERE transaction_date < ?", (cutoff_date,))

                    self.maintenance_result_text.insert(tk.END,
                        f"✅ {old_transactions} transações antigas removidas\n")
                else:
                    self.maintenance_result_text.insert(tk.END, "Limpeza cancelada pelo usuário\n")
            else:
                self.maintenance_result_text.insert(tk.END, "✅ Nenhum dado antigo encontrado\n")

            self.maintenance_result_text.see(tk.END)

        except Exception as e:
            self.maintenance_result_text.insert(tk.END, f"❌ Erro na limpeza: {str(e)}\n")

    def reindex_database(self):
        """Reindexa o banco de dados"""
        try:
            self.maintenance_result_text.insert(tk.END, "Reindexando banco de dados...\n")

            # Executar REINDEX
            self.db_manager.execute_query("REINDEX")

            self.maintenance_result_text.insert(tk.END, "✅ Banco reindexado com sucesso\n")
            self.maintenance_result_text.see(tk.END)

        except Exception as e:
            self.maintenance_result_text.insert(tk.END, f"❌ Erro na reindexação: {str(e)}\n")

    def clear_system_logs(self):
        """Limpa logs do sistema"""
        if messagebox.askyesno("Confirmar", "Deseja realmente limpar todos os logs?"):
            messagebox.showinfo("Em Desenvolvimento", "Limpeza de logs em desenvolvimento")

    def show_system_stats(self):
        """Mostra estatísticas do sistema"""
        if not self.current_user['is_admin']:
            messagebox.showerror("Erro", "Acesso negado. Apenas administradores podem acessar esta função.")
            return

        try:
            # Criar janela de estatísticas
            stats_window = tk.Toplevel(self.root)
            stats_window.title("Estatísticas do Sistema")
            stats_window.geometry("600x500")
            stats_window.transient(self.root)
            stats_window.grab_set()

            # Frame principal
            main_frame = ttk.Frame(stats_window, padding="20")
            main_frame.pack(fill=tk.BOTH, expand=True)

            # Título
            ttk.Label(main_frame, text="Estatísticas do Sistema",
                     font=("Arial", 14, "bold")).pack(pady=(0, 20))

            # Buscar estatísticas
            stats = self.get_system_statistics()

            # Exibir estatísticas
            stats_frame = ttk.Frame(main_frame)
            stats_frame.pack(fill=tk.BOTH, expand=True)

            row = 0
            for label, value in stats.items():
                ttk.Label(stats_frame, text=f"{label}:", font=("Arial", 10, "bold")).grid(
                    row=row, column=0, sticky=tk.W, padx=(0, 10), pady=5)
                ttk.Label(stats_frame, text=str(value)).grid(
                    row=row, column=1, sticky=tk.W, pady=5)
                row += 1

            # Botão atualizar
            ttk.Button(main_frame, text="Atualizar",
                      command=lambda: self.refresh_system_stats(stats_frame)).pack(pady=(20, 10))

            # Botão fechar
            ttk.Button(main_frame, text="Fechar",
                      command=stats_window.destroy).pack()

        except Exception as e:
            messagebox.showerror("Erro", f"Erro ao mostrar estatísticas: {str(e)}")

    def show_database_maintenance(self):
        """Mostra manutenção do banco"""
        if not self.current_user['is_admin']:
            messagebox.showerror("Erro", "Acesso negado. Apenas administradores podem acessar esta função.")
            return

        try:
            # Criar janela de manutenção
            maintenance_window = tk.Toplevel(self.root)
            maintenance_window.title("Manutenção do Banco de Dados")
            maintenance_window.geometry("600x500")
            maintenance_window.transient(self.root)
            maintenance_window.grab_set()

            # Frame principal
            main_frame = ttk.Frame(maintenance_window, padding="20")
            main_frame.pack(fill=tk.BOTH, expand=True)

            # Título
            ttk.Label(main_frame, text="Manutenção do Banco de Dados",
                     font=("Arial", 14, "bold")).pack(pady=(0, 20))

            # Opções de manutenção
            ttk.Button(main_frame, text="Verificar Integridade",
                      command=lambda: self.check_database_integrity(result_text), width=25).pack(fill=tk.X, pady=5)
            ttk.Button(main_frame, text="Otimizar Banco",
                      command=lambda: self.optimize_database(result_text), width=25).pack(fill=tk.X, pady=5)
            ttk.Button(main_frame, text="Limpar Dados Antigos",
                      command=lambda: self.cleanup_old_data(result_text), width=25).pack(fill=tk.X, pady=5)
            ttk.Button(main_frame, text="Reindexar Tabelas",
                      command=lambda: self.reindex_database(result_text), width=25).pack(fill=tk.X, pady=5)

            # Área de resultado
            ttk.Label(main_frame, text="Resultado:").pack(anchor=tk.W, pady=(20, 5))

            # Frame para texto com scroll
            text_frame = ttk.Frame(main_frame)
            text_frame.pack(fill=tk.BOTH, expand=True)

            result_text = tk.Text(text_frame, height=10, wrap=tk.WORD)
            result_scrollbar = ttk.Scrollbar(text_frame, orient=tk.VERTICAL, command=result_text.yview)
            result_text.configure(yscrollcommand=result_scrollbar.set)

            result_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
            result_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

            # Botão fechar
            ttk.Button(main_frame, text="Fechar",
                      command=maintenance_window.destroy).pack(pady=(10, 0))

        except Exception as e:
            messagebox.showerror("Erro", f"Erro ao abrir manutenção: {str(e)}")

    def get_system_statistics(self):
        """Obtém estatísticas do sistema"""
        try:
            stats = {}

            # Estatísticas de usuários
            users_count = self.db_manager.execute_query("SELECT COUNT(*) FROM users")[0][0]
            active_users = self.db_manager.execute_query("SELECT COUNT(*) FROM users WHERE is_active = TRUE")[0][0]
            admin_users = self.db_manager.execute_query("SELECT COUNT(*) FROM users WHERE is_admin = TRUE")[0][0]

            stats["Total de Usuários"] = users_count
            stats["Usuários Ativos"] = active_users
            stats["Administradores"] = admin_users

            # Estatísticas de transações
            transactions_count = self.db_manager.execute_query("SELECT COUNT(*) FROM transactions")[0][0]
            income_count = self.db_manager.execute_query("SELECT COUNT(*) FROM transactions WHERE transaction_type = 'income'")[0][0]
            expense_count = self.db_manager.execute_query("SELECT COUNT(*) FROM transactions WHERE transaction_type = 'expense'")[0][0]

            stats["Total de Transações"] = transactions_count
            stats["Receitas"] = income_count
            stats["Despesas"] = expense_count

            # Estatísticas de carteiras
            wallets_count = self.db_manager.execute_query("SELECT COUNT(*) FROM wallets")[0][0]
            stats["Total de Carteiras"] = wallets_count

            # Estatísticas de categorias
            categories_count = self.db_manager.execute_query("SELECT COUNT(*) FROM categories")[0][0]
            stats["Total de Categorias"] = categories_count

            # Tamanho do banco de dados
            import os
            if os.path.exists("data/gestao_contas.db"):
                db_size = os.path.getsize("data/gestao_contas.db")
                stats["Tamanho do Banco"] = f"{db_size / 1024:.1f} KB"

            return stats

        except Exception as e:
            return {"Erro": str(e)}

    def refresh_system_stats(self, stats_frame):
        """Atualiza estatísticas na tela"""
        try:
            # Limpar frame
            for widget in stats_frame.winfo_children():
                widget.destroy()

            # Buscar novas estatísticas
            stats = self.get_system_statistics()

            # Exibir estatísticas atualizadas
            row = 0
            for label, value in stats.items():
                ttk.Label(stats_frame, text=f"{label}:", font=("Arial", 10, "bold")).grid(
                    row=row, column=0, sticky=tk.W, padx=(0, 10), pady=5)
                ttk.Label(stats_frame, text=str(value)).grid(
                    row=row, column=1, sticky=tk.W, pady=5)
                row += 1

        except Exception as e:
            messagebox.showerror("Erro", f"Erro ao atualizar estatísticas: {str(e)}")

    def check_database_integrity(self, result_text):
        """Verifica integridade do banco"""
        try:
            result_text.insert(tk.END, "Verificando integridade do banco...\n")
            result_text.update()

            # Executar PRAGMA integrity_check
            result = self.db_manager.execute_query("PRAGMA integrity_check")

            if result and result[0][0] == "ok":
                result_text.insert(tk.END, "✅ Integridade do banco: OK\n")
            else:
                result_text.insert(tk.END, f"❌ Problemas encontrados: {result}\n")

            result_text.see(tk.END)

        except Exception as e:
            result_text.insert(tk.END, f"❌ Erro na verificação: {str(e)}\n")

    def optimize_database(self, result_text):
        """Otimiza o banco de dados"""
        try:
            result_text.insert(tk.END, "Otimizando banco de dados...\n")
            result_text.update()

            # Executar VACUUM
            self.db_manager.execute_query("VACUUM")

            result_text.insert(tk.END, "✅ Banco otimizado com sucesso\n")
            result_text.see(tk.END)

        except Exception as e:
            result_text.insert(tk.END, f"❌ Erro na otimização: {str(e)}\n")

    def cleanup_old_data(self, result_text):
        """Limpa dados antigos"""
        try:
            result_text.insert(tk.END, "Limpando dados antigos...\n")
            result_text.update()

            # Exemplo: remover transações muito antigas (mais de 2 anos)
            from datetime import datetime, timedelta
            cutoff_date = (datetime.now() - timedelta(days=730)).strftime('%Y-%m-%d')

            old_transactions = self.db_manager.execute_query(
                "SELECT COUNT(*) FROM transactions WHERE transaction_date < ?", (cutoff_date,))[0][0]

            if old_transactions > 0:
                if messagebox.askyesno("Confirmar",
                    f"Encontradas {old_transactions} transações antigas (mais de 2 anos).\n"
                    "Deseja removê-las?"):

                    self.db_manager.execute_query(
                        "DELETE FROM transactions WHERE transaction_date < ?", (cutoff_date,))

                    result_text.insert(tk.END,
                        f"✅ {old_transactions} transações antigas removidas\n")
                else:
                    result_text.insert(tk.END, "Limpeza cancelada pelo usuário\n")
            else:
                result_text.insert(tk.END, "✅ Nenhum dado antigo encontrado\n")

            result_text.see(tk.END)

        except Exception as e:
            result_text.insert(tk.END, f"❌ Erro na limpeza: {str(e)}\n")

    def reindex_database(self, result_text):
        """Reindexa o banco de dados"""
        try:
            result_text.insert(tk.END, "Reindexando banco de dados...\n")
            result_text.update()

            # Executar REINDEX
            self.db_manager.execute_query("REINDEX")

            result_text.insert(tk.END, "✅ Banco reindexado com sucesso\n")
            result_text.see(tk.END)

        except Exception as e:
            result_text.insert(tk.END, f"❌ Erro na reindexação: {str(e)}\n")

    def show_reset_options(self):
        """Mostra opções de limpeza de dados de teste"""
        try:
            # Criar janela de reset
            reset_window = tk.Toplevel(self.root)
            reset_window.title("Limpeza de Dados de Teste")
            reset_window.geometry("700x600")
            reset_window.transient(self.root)
            reset_window.grab_set()
            reset_window.configure(bg='#f0f0f0')

            # Frame principal
            main_frame = ttk.Frame(reset_window)
            main_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)

            # Título
            ttk.Label(main_frame, text="🧹 Limpeza de Dados de Teste",
                     font=("Arial", 16, "bold")).pack(pady=(0, 20))

            # Verificar dados de teste
            user_id = self.current_user['id']

            # Contar dados de teste
            test_transactions = self.db_manager.execute_query("""
                SELECT COUNT(*) FROM transactions
                WHERE user_id = ? AND (
                    description LIKE '%teste%' OR
                    description LIKE '%Teste%' OR
                    description LIKE '%TESTE%' OR
                    description LIKE '%Compra à vista%' OR
                    description LIKE '%Compra parcelada%' OR
                    description LIKE '%Compra em 12x%' OR
                    description LIKE '%Teste 15 parcelas%'
                )
            """, (user_id,))[0][0]

            test_categories = self.db_manager.execute_query("""
                SELECT COUNT(*) FROM categories
                WHERE user_id = ? AND (
                    name LIKE '%teste%' OR
                    name LIKE '%Teste%' OR
                    name LIKE '%TESTE%' OR
                    description LIKE '%teste%'
                )
            """, (user_id,))[0][0]

            # Área de informações
            info_frame = ttk.LabelFrame(main_frame, text="Dados de Teste Encontrados", padding=10)
            info_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 20))

            info_text = tk.Text(info_frame, height=15, font=("Consolas", 10),
                               bg='white', relief=tk.SOLID, bd=1)
            info_scrollbar = ttk.Scrollbar(info_frame, orient=tk.VERTICAL, command=info_text.yview)
            info_text.configure(yscrollcommand=info_scrollbar.set)

            info_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
            info_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

            # Adicionar informações
            info_text.insert(tk.END, f"📊 RESUMO DOS DADOS DE TESTE:\n\n")
            info_text.insert(tk.END, f"• Transações de teste: {test_transactions}\n")
            info_text.insert(tk.END, f"• Categorias de teste: {test_categories}\n\n")

            if test_transactions > 0:
                info_text.insert(tk.END, "📋 TRANSAÇÕES QUE SERÃO REMOVIDAS:\n")

                # Listar algumas transações de teste
                test_trans = self.db_manager.execute_query("""
                    SELECT description, amount, transaction_date, installments, installment_number
                    FROM transactions
                    WHERE user_id = ? AND (
                        description LIKE '%teste%' OR
                        description LIKE '%Teste%' OR
                        description LIKE '%TESTE%' OR
                        description LIKE '%Compra à vista%' OR
                        description LIKE '%Compra parcelada%' OR
                        description LIKE '%Compra em 12x%' OR
                        description LIKE '%Teste 15 parcelas%'
                    )
                    ORDER BY description, installment_number
                    LIMIT 20
                """, (user_id,))

                for trans in test_trans:
                    if trans['installments'] > 1:
                        info_text.insert(tk.END,
                            f"  • {trans['description']} - R$ {trans['amount']:.2f} "
                            f"({trans['installment_number']}/{trans['installments']})\n")
                    else:
                        info_text.insert(tk.END,
                            f"  • {trans['description']} - R$ {trans['amount']:.2f}\n")

                if len(test_trans) == 20:
                    info_text.insert(tk.END, "  ... (e mais transações)\n")

            if test_categories > 0:
                info_text.insert(tk.END, "\n🏷️ CATEGORIAS QUE SERÃO REMOVIDAS:\n")

                test_cats = self.db_manager.execute_query("""
                    SELECT name, category_type FROM categories
                    WHERE user_id = ? AND (
                        name LIKE '%teste%' OR
                        name LIKE '%Teste%' OR
                        name LIKE '%TESTE%' OR
                        description LIKE '%teste%'
                    )
                """, (user_id,))

                for cat in test_cats:
                    tipo = "Receita" if cat['category_type'] == 'income' else "Despesa"
                    info_text.insert(tk.END, f"  • {cat['name']} ({tipo})\n")

            if test_transactions == 0 and test_categories == 0:
                info_text.insert(tk.END, "✅ Nenhum dado de teste encontrado!\n")
                info_text.insert(tk.END, "Seu sistema está limpo.")
            else:
                info_text.insert(tk.END, "\n⚠️ ATENÇÃO:\n")
                info_text.insert(tk.END, "• Esta ação não pode ser desfeita\n")
                info_text.insert(tk.END, "• Apenas dados de teste serão removidos\n")
                info_text.insert(tk.END, "• Dados reais serão preservados\n")

            info_text.configure(state=tk.DISABLED)

            # Função para executar limpeza
            def execute_cleanup():
                try:
                    if test_transactions == 0 and test_categories == 0:
                        messagebox.showinfo("Info", "Nenhum dado de teste encontrado para remover.")
                        return

                    if messagebox.askyesno(
                        "Confirmar Limpeza",
                        f"Confirma a remoção de:\n\n"
                        f"• {test_transactions} transações de teste\n"
                        f"• {test_categories} categorias de teste\n\n"
                        f"Esta ação não pode ser desfeita!"
                    ):
                        # Remover transações de teste
                        if test_transactions > 0:
                            self.db_manager.execute_query("""
                                DELETE FROM transactions
                                WHERE user_id = ? AND (
                                    description LIKE '%teste%' OR
                                    description LIKE '%Teste%' OR
                                    description LIKE '%TESTE%' OR
                                    description LIKE '%Compra à vista%' OR
                                    description LIKE '%Compra parcelada%' OR
                                    description LIKE '%Compra em 12x%' OR
                                    description LIKE '%Teste 15 parcelas%'
                                )
                            """, (user_id,))

                        # Remover categorias de teste não utilizadas
                        if test_categories > 0:
                            unused_cats = self.db_manager.execute_query("""
                                SELECT c.id FROM categories c
                                WHERE c.user_id = ? AND (
                                    c.name LIKE '%teste%' OR
                                    c.name LIKE '%Teste%' OR
                                    c.name LIKE '%TESTE%' OR
                                    c.description LIKE '%teste%'
                                ) AND c.id NOT IN (
                                    SELECT DISTINCT category_id FROM transactions WHERE user_id = ?
                                )
                            """, (user_id, user_id))

                            for cat in unused_cats:
                                self.db_manager.execute_query("DELETE FROM categories WHERE id = ?", (cat['id'],))

                        messagebox.showinfo(
                            "Limpeza Concluída",
                            f"Limpeza realizada com sucesso!\n\n"
                            f"• {test_transactions} transações removidas\n"
                            f"• Categorias não utilizadas removidas\n\n"
                            f"Atualize a interface para ver as mudanças."
                        )

                        # Atualizar interface
                        self.load_transactions()
                        self.load_wallets()
                        self.update_summary_cards()

                        reset_window.destroy()

                except Exception as e:
                    messagebox.showerror("Erro", f"Erro durante a limpeza: {str(e)}")

            # Botões
            button_frame = ttk.Frame(main_frame)
            button_frame.pack(fill=tk.X)

            if test_transactions > 0 or test_categories > 0:
                ttk.Button(button_frame, text="🧹 Executar Limpeza",
                          command=execute_cleanup).pack(side=tk.LEFT, padx=(0, 10))

            ttk.Button(button_frame, text="❌ Cancelar",
                      command=reset_window.destroy).pack(side=tk.RIGHT)

        except Exception as e:
            messagebox.showerror("Erro", f"Erro ao abrir limpeza de dados: {str(e)}")

    def show_full_reset(self):
        """Mostra opções de reset completo do sistema"""
        try:
            # Criar janela de reset completo
            reset_window = tk.Toplevel(self.root)
            reset_window.title("Reset Completo do Sistema")
            reset_window.geometry("600x500")
            reset_window.transient(self.root)
            reset_window.grab_set()
            reset_window.configure(bg='#f0f0f0')

            # Frame principal
            main_frame = ttk.Frame(reset_window)
            main_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)

            # Título
            title_label = ttk.Label(main_frame, text="🔄 Reset Completo do Sistema",
                                   font=("Arial", 16, "bold"))
            title_label.pack(pady=(0, 20))

            # Aviso importante
            warning_frame = ttk.LabelFrame(main_frame, text="⚠️ ATENÇÃO", padding=15)
            warning_frame.pack(fill=tk.X, pady=(0, 20))

            warning_text = tk.Text(warning_frame, height=8, font=("Arial", 11),
                                  bg='#fff3cd', relief=tk.SOLID, bd=1, wrap=tk.WORD)
            warning_text.pack(fill=tk.BOTH, expand=True)

            warning_text.insert(tk.END, "🚨 RESET COMPLETO REMOVE TODOS OS DADOS!\n\n")
            warning_text.insert(tk.END, "Esta operação irá remover:\n")
            warning_text.insert(tk.END, "• TODAS as transações (receitas e despesas)\n")
            warning_text.insert(tk.END, "• TODAS as carteiras (exceto uma padrão)\n")
            warning_text.insert(tk.END, "• TODAS as categorias personalizadas\n")
            warning_text.insert(tk.END, "• Configurações personalizadas\n\n")
            warning_text.insert(tk.END, "⚠️ ESTA AÇÃO NÃO PODE SER DESFEITA!\n")
            warning_text.insert(tk.END, "💡 Faça backup antes de continuar!")

            warning_text.configure(state=tk.DISABLED)

            # Opções de reset
            options_frame = ttk.LabelFrame(main_frame, text="Opções de Reset", padding=10)
            options_frame.pack(fill=tk.X, pady=(0, 20))

            # Variáveis para checkboxes
            reset_transactions = tk.BooleanVar(value=True)
            reset_wallets = tk.BooleanVar(value=False)
            reset_categories = tk.BooleanVar(value=False)
            recreate_defaults = tk.BooleanVar(value=True)

            ttk.Checkbutton(options_frame, text="🗑️ Remover todas as transações",
                           variable=reset_transactions).pack(anchor=tk.W, pady=2)
            ttk.Checkbutton(options_frame, text="🗑️ Remover carteiras personalizadas",
                           variable=reset_wallets).pack(anchor=tk.W, pady=2)
            ttk.Checkbutton(options_frame, text="🗑️ Remover categorias personalizadas",
                           variable=reset_categories).pack(anchor=tk.W, pady=2)
            ttk.Checkbutton(options_frame, text="✅ Recriar dados padrão",
                           variable=recreate_defaults).pack(anchor=tk.W, pady=2)

            # Função para executar reset
            def execute_full_reset():
                try:
                    # Verificar se pelo menos uma opção está selecionada
                    if not any([reset_transactions.get(), reset_wallets.get(), reset_categories.get()]):
                        messagebox.showwarning("Aviso", "Selecione pelo menos uma opção de reset.")
                        return

                    # Confirmação dupla
                    if not messagebox.askyesno(
                        "Primeira Confirmação",
                        "Tem certeza que deseja fazer o reset?\n\n"
                        "Esta ação removerá dados permanentemente!"
                    ):
                        return

                    if not messagebox.askyesno(
                        "Confirmação Final",
                        "ÚLTIMA CHANCE!\n\n"
                        "Confirma o RESET COMPLETO do sistema?\n\n"
                        "Todos os dados selecionados serão PERDIDOS!"
                    ):
                        return

                    user_id = self.current_user['id']

                    # Executar reset baseado nas opções
                    if reset_transactions.get():
                        self.db_manager.execute_query("DELETE FROM transactions WHERE user_id = ?", (user_id,))
                        print("✅ Transações removidas")

                    if reset_wallets.get():
                        # Manter apenas uma carteira padrão
                        wallets = self.db_manager.execute_query("SELECT id FROM wallets WHERE user_id = ? LIMIT 1", (user_id,))
                        if wallets:
                            keep_wallet_id = wallets[0]['id']
                            self.db_manager.execute_query(
                                "DELETE FROM wallets WHERE user_id = ? AND id != ?",
                                (user_id, keep_wallet_id)
                            )
                            # Resetar saldo da carteira mantida
                            self.db_manager.execute_query(
                                "UPDATE wallets SET current_balance = initial_balance WHERE id = ?",
                                (keep_wallet_id,)
                            )
                        print("✅ Carteiras resetadas")

                    if reset_categories.get():
                        # Remover categorias personalizadas (manter apenas padrão do sistema)
                        self.db_manager.execute_query(
                            "DELETE FROM categories WHERE user_id = ? AND user_id IS NOT NULL",
                            (user_id,)
                        )
                        print("✅ Categorias personalizadas removidas")

                    if recreate_defaults.get():
                        # Recriar categorias padrão se foram removidas
                        if reset_categories.get():
                            self.create_default_categories(user_id)
                        print("✅ Dados padrão recriados")

                    messagebox.showinfo(
                        "Reset Concluído",
                        "Reset completo realizado com sucesso!\n\n"
                        "O sistema foi restaurado para o estado inicial.\n"
                        "Reinicie a aplicação para ver as mudanças."
                    )

                    # Atualizar interface
                    self.load_transactions()
                    self.load_wallets()
                    self.update_summary_cards()

                    reset_window.destroy()

                except Exception as e:
                    messagebox.showerror("Erro", f"Erro durante o reset: {str(e)}")

            # Botões
            button_frame = ttk.Frame(main_frame)
            button_frame.pack(fill=tk.X)

            ttk.Button(button_frame, text="🔄 Executar Reset",
                      command=execute_full_reset).pack(side=tk.LEFT, padx=(0, 10))
            ttk.Button(button_frame, text="❌ Cancelar",
                      command=reset_window.destroy).pack(side=tk.RIGHT)

        except Exception as e:
            messagebox.showerror("Erro", f"Erro ao abrir reset completo: {str(e)}")

    def create_default_categories(self, user_id):
        """Cria categorias padrão para o usuário"""
        try:
            default_categories = [
                # Receitas
                ('Salário', 'income', '#27ae60', 'Salário mensal'),
                ('Freelance', 'income', '#2ecc71', 'Trabalhos freelance'),
                ('Investimentos', 'income', '#16a085', 'Rendimentos de investimentos'),
                ('Vendas', 'income', '#f39c12', 'Vendas diversas'),

                # Despesas
                ('Alimentação', 'expense', '#e74c3c', 'Gastos com alimentação'),
                ('Transporte', 'expense', '#3498db', 'Gastos com transporte'),
                ('Moradia', 'expense', '#9b59b6', 'Aluguel, condomínio, etc.'),
                ('Saúde', 'expense', '#1abc9c', 'Gastos médicos e farmácia'),
                ('Educação', 'expense', '#f1c40f', 'Cursos, livros, etc.'),
                ('Lazer', 'expense', '#e67e22', 'Entretenimento e diversão'),
                ('Outros', 'expense', '#95a5a6', 'Outros gastos')
            ]

            for name, cat_type, color, description in default_categories:
                # Verificar se categoria já existe
                existing = self.db_manager.execute_query(
                    "SELECT COUNT(*) FROM categories WHERE user_id = ? AND name = ?",
                    (user_id, name)
                )[0][0]

                if existing == 0:
                    self.db_manager.execute_query("""
                        INSERT INTO categories (user_id, name, category_type, color, description)
                        VALUES (?, ?, ?, ?, ?)
                    """, (user_id, name, cat_type, color, description))

        except Exception as e:
            print(f"Erro ao criar categorias padrão: {str(e)}")

    def show_auto_update_config(self):
        """Mostra configurações de atualizações automáticas"""
        try:
            if not self.auto_update_manager:
                messagebox.showwarning("Aviso", "Sistema de atualizações automáticas não está disponível")
                return

            from .auto_update_config import AutoUpdateConfigWindow
            AutoUpdateConfigWindow(self.root, self.auto_update_manager, self.refresh_auto_update_status)

        except Exception as e:
            messagebox.showerror("Erro", f"Erro ao abrir configurações de atualizações: {str(e)}")

    def refresh_auto_update_status(self):
        """Atualiza status das atualizações automáticas na interface"""
        try:
            # Aqui você pode adicionar lógica para atualizar indicadores visuais
            # do status das atualizações automáticas na interface principal
            pass
        except Exception as e:
            print(f"Erro ao atualizar status de atualizações: {e}")

    # Métodos para gerenciamento de veículos
    def show_vehicle_manager(self):
        """Mostra o gerenciador de veículos"""
        try:
            from .vehicle_manager import VehicleManager
            VehicleManager(self.root, self.db_manager, self.current_user['id'])
        except Exception as e:
            messagebox.showerror("Erro", f"Erro ao abrir gerenciador de veículos: {str(e)}")

    def new_vehicle(self):
        """Abre formulário para novo veículo"""
        try:
            from .vehicle_form import VehicleForm
            VehicleForm(self.root, self.db_manager, self.current_user['id'])
        except Exception as e:
            messagebox.showerror("Erro", f"Erro ao abrir formulário de veículo: {str(e)}")

    def new_maintenance(self):
        """Abre formulário para nova manutenção"""
        try:
            from .maintenance_form import MaintenanceForm
            MaintenanceForm(self.root, self.db_manager, self.current_user['id'])
        except Exception as e:
            messagebox.showerror("Erro", f"Erro ao abrir formulário de manutenção: {str(e)}")

    def new_fuel_record(self):
        """Abre formulário para novo abastecimento"""
        try:
            from .fuel_form import FuelForm
            FuelForm(self.root, self.db_manager, self.current_user['id'])
        except Exception as e:
            messagebox.showerror("Erro", f"Erro ao abrir formulário de combustível: {str(e)}")

    def show_vehicle_report(self):
        """Mostra relatório de veículos"""
        try:
            # Criar janela de relatório de veículos
            report_window = tk.Toplevel(self.root)
            report_window.title("Relatório de Veículos")
            report_window.geometry("800x600")
            report_window.transient(self.root)
            report_window.grab_set()

            # Frame principal
            main_frame = ttk.Frame(report_window)
            main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

            # Título
            ttk.Label(main_frame, text="Relatório de Veículos",
                     style='Title.TLabel').pack(pady=(0, 10))

            # Notebook para diferentes relatórios
            notebook = ttk.Notebook(main_frame)
            notebook.pack(fill=tk.BOTH, expand=True)

            # Aba de resumo geral
            summary_frame = ttk.Frame(notebook)
            notebook.add(summary_frame, text="Resumo Geral")

            # Treeview para veículos
            columns = ('Veículo', 'Modelo', 'Ano', 'Combustível Gasto', 'Manutenções', 'Total Gasto')
            vehicle_tree = ttk.Treeview(summary_frame, columns=columns, show='headings')

            for col in columns:
                vehicle_tree.heading(col, text=col)
                vehicle_tree.column(col, width=120)

            # Scrollbar
            v_scrollbar = ttk.Scrollbar(summary_frame, orient=tk.VERTICAL, command=vehicle_tree.yview)
            vehicle_tree.configure(yscrollcommand=v_scrollbar.set)

            # Pack
            vehicle_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=(0, 5))
            v_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

            # Carregar dados dos veículos
            self.load_vehicle_report_data(vehicle_tree)

            # Botão fechar
            ttk.Button(main_frame, text="Fechar",
                      command=report_window.destroy).pack(pady=(10, 0))

        except Exception as e:
            messagebox.showerror("Erro", f"Erro ao abrir relatório de veículos: {str(e)}")

    def load_vehicle_report_data(self, tree):
        """Carrega dados do relatório de veículos"""
        try:
            # Limpar árvore
            for item in tree.get_children():
                tree.delete(item)

            # Buscar veículos do usuário
            vehicles_query = """
                SELECT id, brand, model, year, license_plate
                FROM vehicles
                WHERE user_id = ?
                ORDER BY brand, model
            """
            vehicles = self.db_manager.execute_query(vehicles_query, (self.current_user['id'],))

            for vehicle in vehicles:
                vehicle_name = f"{vehicle['brand']} {vehicle['model']}"

                # Calcular gastos com combustível
                fuel_query = """
                    SELECT COALESCE(SUM(total_cost), 0) as fuel_cost
                    FROM fuel_records
                    WHERE vehicle_id = ?
                """
                fuel_result = self.db_manager.execute_query(fuel_query, (vehicle['id'],))
                fuel_cost = fuel_result[0]['fuel_cost'] if fuel_result else 0

                # Calcular gastos com manutenção
                maintenance_query = """
                    SELECT COALESCE(SUM(cost), 0) as maintenance_cost
                    FROM maintenance_records
                    WHERE vehicle_id = ?
                """
                maintenance_result = self.db_manager.execute_query(maintenance_query, (vehicle['id'],))
                maintenance_cost = maintenance_result[0]['maintenance_cost'] if maintenance_result else 0

                # Contar manutenções
                maintenance_count_query = """
                    SELECT COUNT(*) as count
                    FROM maintenance_records
                    WHERE vehicle_id = ?
                """
                maintenance_count_result = self.db_manager.execute_query(maintenance_count_query, (vehicle['id'],))
                maintenance_count = maintenance_count_result[0]['count'] if maintenance_count_result else 0

                total_cost = fuel_cost + maintenance_cost

                tree.insert('', 'end', values=(
                    vehicle_name,
                    vehicle['model'],
                    vehicle['year'],
                    f"R$ {fuel_cost:,.2f}",
                    f"{maintenance_count} registros",
                    f"R$ {total_cost:,.2f}"
                ))

        except Exception as e:
            print(f"Erro ao carregar dados do relatório de veículos: {str(e)}")
            messagebox.showerror("Erro", f"Erro ao carregar dados: {str(e)}")

    def show_about(self):
        """Mostra informações sobre o sistema"""
        about_text = """
Sistema de Gestão de Contas
Versão 1.0

Desenvolvido para controle financeiro pessoal
com recursos avançados de gerenciamento.

Inclui gerenciamento de veículos, manutenção e combustível.

© 2024 - Todos os direitos reservados
        """
        messagebox.showinfo("Sobre", about_text)
