# 📦 Guia de Instalação - Sistema de Gestão de Contas

## 🚀 Opções de Instalação

### Opção 1: Executável Pronto (Recomendado)
**Mais fácil - <PERSON><PERSON> requer Python instalado**

1. Baixe o arquivo `GestaoContas_v1.0.0.zip`
2. Extraia o conteúdo em uma pasta
3. Execute `GestaoContas.exe`
4. Use o login padrão:
   - **Usuário:** admin
   - **Senha:** admin123

### Opção 2: Executar com Python
**Requer Python 3.7+ instalado**

1. Baixe todos os arquivos do projeto
2. Abra o terminal/prompt na pasta do projeto
3. Execute: `python run.py`
4. Siga as instruções na tela

### Opção 3: Instalação Completa com Dependências
**Para desenvolvedores ou uso avançado**

1. Instale Python 3.7 ou superior
2. Baixe o projeto completo
3. Instale dependências: `pip install -r requirements.txt`
4. Execute: `python main.py`

## 🔧 Requisitos do Sistema

### Mínimos
- **Sistema Operacional:** Windows 7+, macOS 10.12+, Linux (Ubuntu 16.04+)
- **RAM:** 512 MB
- **Espaço em Disco:** 100 MB
- **Resolução:** 1024x768

### Recomendados
- **Sistema Operacional:** Windows 10+, macOS 10.15+, Linux (Ubuntu 20.04+)
- **RAM:** 2 GB
- **Espaço em Disco:** 500 MB
- **Resolução:** 1366x768 ou superior

### Para Executar com Python
- **Python:** 3.7, 3.8, 3.9, 3.10, ou 3.11
- **Tkinter:** Incluído no Python padrão
- **SQLite3:** Incluído no Python padrão

## 📋 Instalação Detalhada

### Windows

#### Opção A: Executável
1. Baixe `GestaoContas_v1.0.0.zip`
2. Clique com botão direito → "Extrair Tudo"
3. Abra a pasta extraída
4. Duplo-clique em `GestaoContas.exe`

#### Opção B: Python
1. Instale Python de [python.org](https://python.org)
   - ✅ Marque "Add Python to PATH"
2. Baixe o projeto
3. Abra Prompt de Comando na pasta
4. Execute: `python run.py`

### macOS

#### Opção A: Python (Recomendado)
1. Instale Python via Homebrew:
   ```bash
   brew install python
   ```
2. Baixe o projeto
3. Abra Terminal na pasta
4. Execute: `python3 run.py`

#### Opção B: Python do Sistema
1. macOS já inclui Python
2. Baixe o projeto
3. Execute: `python3 run.py`

### Linux (Ubuntu/Debian)

#### Instalação Completa
```bash
# Atualizar sistema
sudo apt update

# Instalar Python e Tkinter
sudo apt install python3 python3-pip python3-tk

# Baixar projeto (substitua pela URL real)
git clone <url-do-projeto>
cd Gestao-de-Contas

# Instalar dependências opcionais
pip3 install -r requirements.txt

# Executar
python3 run.py
```

### Linux (CentOS/RHEL/Fedora)

```bash
# CentOS/RHEL
sudo yum install python3 python3-pip tkinter

# Fedora
sudo dnf install python3 python3-pip python3-tkinter

# Continuar como Ubuntu
python3 run.py
```

## 🔍 Verificação da Instalação

### Teste Rápido
Execute um dos comandos:
```bash
# Windows
python run.py

# macOS/Linux
python3 run.py
```

O sistema deve mostrar:
- ✅ Verificações de dependências
- ✅ Configuração do ambiente
- ✅ Teste rápido do banco
- 🚀 Tela de login

### Teste Completo
```bash
# Windows
python test_system.py

# macOS/Linux
python3 test_system.py
```

## 🐛 Solução de Problemas

### Erro: "Python não encontrado"
**Windows:**
- Reinstale Python marcando "Add to PATH"
- Ou use: `py run.py`

**macOS/Linux:**
- Use `python3` em vez de `python`
- Instale via gerenciador de pacotes

### Erro: "tkinter não encontrado"
**Ubuntu/Debian:**
```bash
sudo apt install python3-tk
```

**CentOS/RHEL:**
```bash
sudo yum install tkinter
```

**macOS:**
```bash
brew install python-tk
```

### Erro: "Módulo não encontrado"
```bash
# Instalar dependências
pip install -r requirements.txt

# Ou individualmente
pip install bcrypt pillow
```

### Erro: "Banco de dados corrompido"
1. Feche a aplicação
2. Delete a pasta `data/`
3. Reinicie a aplicação
4. Ou restaure um backup

### Erro: "Permissão negada"
**Linux/macOS:**
```bash
chmod +x run.py
python3 run.py
```

**Windows:**
- Execute como Administrador
- Verifique antivírus

### Interface não aparece
1. Verifique se Tkinter está instalado
2. Teste: `python -c "import tkinter"`
3. Em servidores Linux, instale interface gráfica

## 📁 Estrutura Após Instalação

```
Gestao-de-Contas/
├── GestaoContas.exe      # Executável (se usando)
├── main.py               # Aplicação principal
├── run.py                # Script de inicialização
├── config.py             # Configurações
├── requirements.txt      # Dependências
├── README.md             # Documentação
├── INSTALACAO.md         # Este arquivo
├── data/                 # Banco de dados
│   └── gestao_contas.db
├── backups/              # Backups automáticos
├── logs/                 # Logs do sistema
└── src/                  # Código fonte
    ├── database.py
    ├── auth.py
    ├── gui/
    └── modules/
```

## 🔄 Atualizações

### Atualizar Sistema
1. Faça backup dos dados (Menu → Arquivo → Backup)
2. Baixe a nova versão
3. Substitua os arquivos (mantenha pasta `data/`)
4. Execute normalmente

### Migração de Dados
- Os dados ficam em `data/gestao_contas.db`
- Copie este arquivo para a nova instalação
- Ou use a função de backup/restauração

## 🆘 Suporte

### Logs de Erro
- **Localização:** `logs/` ou console
- **Incluir:** Versão do Python, SO, mensagem de erro

### Diagnóstico
```bash
python test_system.py    # Teste completo
python run.py           # Verificação básica
```

### Informações do Sistema
```bash
python --version        # Versão do Python
python -c "import tkinter; print('Tkinter OK')"
```

## 📝 Notas Importantes

1. **Primeiro Uso:** Login admin/admin123
2. **Segurança:** Altere a senha padrão
3. **Backup:** Configure backups automáticos
4. **Dados:** Ficam em `data/gestao_contas.db`
5. **Portabilidade:** Copie a pasta inteira para mover

## 🎯 Próximos Passos

Após a instalação:
1. ✅ Faça login com admin/admin123
2. ✅ Altere a senha (Menu → Configurações)
3. ✅ Crie suas carteiras
4. ✅ Configure categorias personalizadas
5. ✅ Registre suas primeiras transações
6. ✅ Configure alertas de vencimento
7. ✅ Faça um backup inicial

**Pronto! Seu sistema de gestão financeira está funcionando! 🎉**
