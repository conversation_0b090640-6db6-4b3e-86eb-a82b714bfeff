#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Teste do filtro de parcelas na interface
"""

import sys
import os
import tkinter as tk
from datetime import datetime

# Adicionar o diretório src ao path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from database import DatabaseManager
from auth import AuthManager
from gui.main_window import MainWindow

def test_filtro_parcelas():
    """Testa como o filtro afeta a visualização das parcelas"""
    print("🔍 TESTE DO FILTRO DE PARCELAS")
    print("=" * 50)
    
    try:
        # Inicializar banco de dados
        db_manager = DatabaseManager()
        db_manager.initialize_database()
        
        auth_manager = AuthManager(db_manager)
        
        # Fazer login
        user_data = auth_manager.authenticate_user('admin', 'admin123')
        if not user_data:
            print("❌ Falha no login!")
            return
        
        print(f"✅ Login realizado: {user_data['full_name']}")
        
        # Criar janela principal
        root = tk.Tk()
        root.title("Teste Filtro Parcelas")
        root.geometry("1200x800")
        
        # Função de logout mock
        def mock_logout():
            print("Logout chamado")
        
        # Criar MainWindow
        main_window = MainWindow(root, db_manager, auth_manager, user_data, mock_logout)
        
        print("✅ Janela principal criada com sucesso!")
        
        # Navegar para a aba de transações
        main_window.notebook.select(2)  # Aba de transações
        
        def test_filters():
            try:
                root.update_idletasks()
                
                # Testar diferentes filtros
                filters_to_test = ["Todos", "Este Mês", "Este Ano"]
                
                for filter_name in filters_to_test:
                    print(f"\n🔍 TESTANDO FILTRO: {filter_name}")
                    
                    # Definir filtro
                    main_window.period_combo.set(filter_name)
                    
                    # Carregar transações
                    main_window.load_transactions()
                    
                    # Contar transações na interface
                    children = main_window.transactions_tree.get_children()
                    print(f"   Transações visíveis: {len(children)}")
                    
                    # Contar parcelas específicas
                    parcelas_visiveis = 0
                    for child in children:
                        values = main_window.transactions_tree.item(child)['values']
                        if len(values) > 1 and 'Teste 15 parcelas' in str(values[1]):
                            parcelas_visiveis += 1
                    
                    print(f"   Parcelas de '15 parcelas' visíveis: {parcelas_visiveis}")
                    
                    # Mostrar algumas transações para debug
                    if children:
                        print(f"   Primeiras transações:")
                        for i, child in enumerate(children[:3]):
                            values = main_window.transactions_tree.item(child)['values']
                            if len(values) >= 6:
                                print(f"     {i+1}. {values[0]} | {values[1]} | {values[5]}")
                
                # Verificar no banco quantas parcelas existem realmente
                print(f"\n📊 VERIFICAÇÃO NO BANCO DE DADOS:")
                
                user_id = user_data['id']
                
                # Total de transações
                total_transactions = db_manager.execute_query(
                    "SELECT COUNT(*) FROM transactions WHERE user_id = ?", (user_id,))[0][0]
                print(f"   Total de transações no banco: {total_transactions}")
                
                # Parcelas de teste
                test_parcelas = db_manager.execute_query("""
                    SELECT COUNT(*) FROM transactions 
                    WHERE user_id = ? AND description LIKE '%Teste 15 parcelas%'
                """, (user_id,))[0][0]
                print(f"   Parcelas 'Teste 15 parcelas' no banco: {test_parcelas}")
                
                # Parcelas por mês
                parcelas_por_mes = db_manager.execute_query("""
                    SELECT strftime('%Y-%m', transaction_date) as mes, COUNT(*) as count
                    FROM transactions 
                    WHERE user_id = ? AND description LIKE '%Teste 15 parcelas%'
                    GROUP BY strftime('%Y-%m', transaction_date)
                    ORDER BY mes
                """, (user_id,))
                
                print(f"   Distribuição por mês:")
                for row in parcelas_por_mes:
                    print(f"     {row['mes']}: {row['count']} parcelas")
                
                # Mês atual
                current_month = datetime.now().strftime('%Y-%m')
                print(f"   Mês atual: {current_month}")
                
            except Exception as e:
                print(f"❌ Erro ao testar filtros: {str(e)}")
                import traceback
                traceback.print_exc()
        
        # Aguardar renderização e testar
        root.after(1000, test_filters)
        
        print("\n✅ TESTE DO FILTRO INICIADO!")
        print("🔍 Verificando filtros em 1 segundo...")
        
        # Executar por tempo limitado
        root.after(8000, root.quit)
        root.mainloop()
        root.destroy()
        
    except Exception as e:
        print(f"❌ ERRO GERAL: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_filtro_parcelas()
