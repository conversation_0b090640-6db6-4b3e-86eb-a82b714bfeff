#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Sistema de Gestão de Contas
Aplicação desktop para controle financeiro pessoal
"""

import tkinter as tk
from tkinter import ttk, messagebox
import sqlite3
import os
import sys
from datetime import datetime, date
import json
import shutil
from pathlib import Path

# Verificar se bcrypt está disponível
try:
    import bcrypt
    BCRYPT_AVAILABLE = True
except ImportError:
    BCRYPT_AVAILABLE = False
    print("Aviso: bcrypt não encontrado, usando hashlib como alternativa")

# Importar módulos do sistema
from src.database import DatabaseManager
from src.auth import AuthManager
from src.gui.login_window import LoginWindow
from src.gui.main_window import MainWindow
from src.modules.auto_update_manager import AutoUpdateManager

class GestaoContasApp:
    def __init__(self):
        self.db_manager = DatabaseManager()
        self.auth_manager = AuthManager(self.db_manager)
        self.auto_update_manager = None
        self.current_user = None
        self.root = None
        
    def initialize_database(self):
        """Inicializa o banco de dados e cria usuário admin padrão"""
        self.db_manager.initialize_database()
        
        # Criar usuário admin padrão se não existir
        if not self.auth_manager.user_exists('admin'):
            self.auth_manager.create_user(
                username='admin',
                password='admin123',
                email='<EMAIL>',
                is_admin=True,
                full_name='Administrador do Sistema'
            )
            print("Usuário admin criado com sucesso!")
            print("Login: admin | Senha: admin123")
    
    def start_application(self):
        """Inicia a aplicação"""
        try:
            # Inicializar banco de dados
            self.initialize_database()
            
            # Criar janela principal do Tkinter
            self.root = tk.Tk()
            self.root.withdraw()  # Esconder janela principal inicialmente
            
            # Mostrar tela de login
            self.show_login()
            
            # Iniciar loop principal
            self.root.mainloop()
            
        except Exception as e:
            messagebox.showerror("Erro", f"Erro ao iniciar aplicação: {str(e)}")
            sys.exit(1)
    
    def show_login(self):
        """Mostra a tela de login"""
        login_window = LoginWindow(self.root, self.auth_manager, self.on_login_success)
    
    def on_login_success(self, user_data):
        """Callback executado após login bem-sucedido"""
        self.current_user = user_data
        
        # Fechar janela de login e mostrar aplicação principal
        self.show_main_application()
    
    def show_main_application(self):
        """Mostra a aplicação principal"""
        # Inicializar sistema de atualizações automáticas
        self.initialize_auto_updates()

        # Mostrar janela principal
        self.root.deiconify()

        # Criar interface principal
        main_window = MainWindow(
            self.root,
            self.db_manager,
            self.auth_manager,
            self.current_user,
            self.on_logout,
            self.auto_update_manager
        )
    
    def initialize_auto_updates(self):
        """Inicializa o sistema de atualizações automáticas"""
        try:
            self.auto_update_manager = AutoUpdateManager(self.db_manager)
            self.auto_update_manager.start()
            print("✓ Sistema de atualizações automáticas iniciado")
        except Exception as e:
            print(f"⚠️ Erro ao inicializar atualizações automáticas: {e}")

    def shutdown_auto_updates(self):
        """Para o sistema de atualizações automáticas"""
        if self.auto_update_manager:
            try:
                self.auto_update_manager.stop()
                print("✓ Sistema de atualizações automáticas parado")
            except Exception as e:
                print(f"⚠️ Erro ao parar atualizações automáticas: {e}")

    def on_logout(self):
        """Callback executado no logout"""
        self.shutdown_auto_updates()
        self.current_user = None
        self.root.withdraw()
        self.show_login()

def main():
    """Função principal"""
    app = None
    try:
        app = GestaoContasApp()
        app.start_application()
    except KeyboardInterrupt:
        print("\nAplicação encerrada pelo usuário.")
    except Exception as e:
        print(f"Erro fatal: {str(e)}")
    finally:
        # Garantir que o sistema de atualizações seja parado
        if app:
            app.shutdown_auto_updates()
        sys.exit(0)

if __name__ == "__main__":
    main()
