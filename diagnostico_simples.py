#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Diagnóstico simples para verificar se a aplicação pode ser executada
"""

import sys
import os

def verificar_dependencias():
    """Verifica se todas as dependências estão disponíveis"""
    print("=== DIAGNÓSTICO DO SISTEMA ===\n")
    
    # Verificar Python
    print(f"✓ Python {sys.version}")
    
    # Verificar módulos básicos
    modulos_basicos = ['tkinter', 'sqlite3', 'datetime', 'pathlib']
    
    for modulo in modulos_basicos:
        try:
            __import__(modulo)
            print(f"✓ {modulo} - OK")
        except ImportError as e:
            print(f"✗ {modulo} - ERRO: {e}")
    
    # Verificar módulos opcionais
    modulos_opcionais = ['bcrypt', 'tkcalendar']
    
    print("\nMódulos opcionais:")
    for modulo in modulos_opcionais:
        try:
            __import__(modulo)
            print(f"✓ {modulo} - OK")
        except ImportError:
            print(f"⚠ {modulo} - Não encontrado (opcional)")
    
    # Verificar arquivos do projeto
    print("\nArquivos do projeto:")
    arquivos_importantes = [
        'main.py',
        'src/database.py',
        'src/auth.py',
        'src/gui/main_window.py',
        'src/gui/login_window.py'
    ]
    
    for arquivo in arquivos_importantes:
        if os.path.exists(arquivo):
            print(f"✓ {arquivo} - OK")
        else:
            print(f"✗ {arquivo} - NÃO ENCONTRADO")
    
    # Verificar diretório de dados
    if not os.path.exists('data'):
        print("⚠ Diretório 'data' não existe - será criado automaticamente")
    else:
        print("✓ Diretório 'data' - OK")

def testar_tkinter():
    """Testa se o Tkinter funciona"""
    print("\n=== TESTE DO TKINTER ===")
    
    try:
        import tkinter as tk
        
        # Criar janela de teste
        root = tk.Tk()
        root.title("Teste - Sistema de Gestão de Contas")
        root.geometry("400x200")
        
        # Adicionar conteúdo
        frame = tk.Frame(root, padx=20, pady=20)
        frame.pack(fill=tk.BOTH, expand=True)
        
        tk.Label(frame, text="🎉 TESTE REALIZADO COM SUCESSO!", 
                font=('Arial', 14, 'bold')).pack(pady=10)
        
        tk.Label(frame, text="Se você está vendo esta janela,\no Tkinter está funcionando corretamente!").pack(pady=10)
        
        tk.Button(frame, text="Fechar Teste", command=root.destroy).pack(pady=10)
        
        tk.Button(frame, text="Executar Aplicação Principal", 
                 command=lambda: executar_main(root)).pack(pady=5)
        
        print("✓ Janela de teste criada com sucesso!")
        print("✓ Uma janela deve ter aparecido na sua tela")
        
        # Centralizar janela
        root.update_idletasks()
        x = (root.winfo_screenwidth() // 2) - (400 // 2)
        y = (root.winfo_screenheight() // 2) - (200 // 2)
        root.geometry(f"400x200+{x}+{y}")
        
        root.mainloop()
        
    except Exception as e:
        print(f"✗ Erro no Tkinter: {e}")
        return False
    
    return True

def executar_main(janela_teste):
    """Executa a aplicação principal"""
    try:
        janela_teste.destroy()
        
        print("\n=== EXECUTANDO APLICAÇÃO PRINCIPAL ===")
        
        # Importar e executar
        from main import main
        main()
        
    except Exception as e:
        print(f"✗ Erro ao executar aplicação principal: {e}")
        import traceback
        traceback.print_exc()

def main():
    """Função principal do diagnóstico"""
    verificar_dependencias()
    
    print("\n" + "="*50)
    print("INICIANDO TESTE DO TKINTER...")
    print("Uma janela deve aparecer na sua tela.")
    print("="*50)
    
    testar_tkinter()

if __name__ == "__main__":
    main()
