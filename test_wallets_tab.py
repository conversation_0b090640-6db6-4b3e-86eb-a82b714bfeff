#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Teste da aba de carteiras para verificar botões
"""

import sys
import os
import tkinter as tk

# Adicionar o diretório src ao path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from database import DatabaseManager
from auth import AuthManager
from gui.main_window import MainWindow

def test_wallets_tab():
    """Testa a aba de carteiras"""
    print("💳 TESTE DA ABA DE CARTEIRAS")
    print("=" * 50)
    
    try:
        # Inicializar banco de dados
        db_manager = DatabaseManager()
        db_manager.initialize_database()
        
        auth_manager = AuthManager(db_manager)
        
        # Fazer login
        user_data = auth_manager.authenticate_user('admin', 'admin123')
        if not user_data:
            print("❌ Falha no login!")
            return
        
        print(f"✅ Login realizado: {user_data['full_name']}")
        
        # <PERSON><PERSON>r janela principal
        root = tk.Tk()
        root.title("Teste Aba Carteiras")
        root.geometry("1200x800")
        
        # Função de logout mock
        def mock_logout():
            print("Logout chamado")
        
        # Criar MainWindow
        main_window = MainWindow(root, db_manager, auth_manager, user_data, mock_logout)
        
        print("✅ Janela principal criada com sucesso!")
        
        # Navegar para a aba de carteiras
        main_window.notebook.select(1)  # Aba de carteiras (índice 1)
        
        # Verificar botões após renderização
        def check_wallet_buttons():
            try:
                root.update_idletasks()
                
                # Obter dimensões da janela principal
                window_width = root.winfo_width()
                window_height = root.winfo_height()
                
                print(f"📏 Dimensões da janela principal: {window_width}x{window_height}")
                
                # Procurar botões na aba de carteiras
                buttons = []
                def find_buttons(widget, level=0):
                    indent = "  " * level
                    if hasattr(widget, 'winfo_class') and widget.winfo_class() == 'TButton':
                        try:
                            button_info = {
                                'text': widget['text'],
                                'width': widget.winfo_width(),
                                'height': widget.winfo_height(),
                                'x': widget.winfo_x(),
                                'y': widget.winfo_y(),
                                'widget': widget
                            }
                            buttons.append(button_info)
                            print(f"{indent}🔍 Botão encontrado: '{button_info['text']}'")
                        except Exception as e:
                            print(f"{indent}❌ Erro ao obter info do botão: {e}")
                    
                    try:
                        for child in widget.winfo_children():
                            find_buttons(child, level + 1)
                    except:
                        pass
                
                find_buttons(root)
                
                print(f"\n📊 Resumo dos botões encontrados: {len(buttons)}")
                for i, btn in enumerate(buttons):
                    print(f"   {i+1}. Texto: '{btn['text']}'")
                    print(f"      Dimensões: {btn['width']}x{btn['height']}")
                    print(f"      Posição: ({btn['x']}, {btn['y']})")
                    
                    # Verificar se o texto pode estar cortado
                    if btn['width'] < len(btn['text']) * 8:  # Estimativa grosseira
                        print(f"      ⚠️  Botão pode estar muito estreito para o texto!")
                    else:
                        print(f"      ✅ Botão parece ter largura adequada")
                
                # Verificar especificamente os botões da toolbar de carteiras
                wallet_buttons = [btn for btn in buttons if btn['text'] in ['Nova Carteira', 'Editar', 'Excluir']]
                if wallet_buttons:
                    print(f"\n💳 Botões da aba de carteiras: {len(wallet_buttons)}")
                    for btn in wallet_buttons:
                        print(f"   - '{btn['text']}': {btn['width']}x{btn['height']} em ({btn['x']}, {btn['y']})")
                
            except Exception as e:
                print(f"❌ Erro ao verificar botões: {str(e)}")
                import traceback
                traceback.print_exc()
        
        # Aguardar renderização e verificar
        root.after(1000, check_wallet_buttons)
        
        print("\n✅ TESTE DA ABA DE CARTEIRAS INICIADO!")
        print("🔍 Verificando botões em 1 segundo...")
        
        # Executar por tempo limitado
        root.after(5000, root.quit)
        root.mainloop()
        root.destroy()
        
    except Exception as e:
        print(f"❌ ERRO GERAL: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_wallets_tab()
