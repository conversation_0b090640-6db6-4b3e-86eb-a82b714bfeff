#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Teste específico para verificar se os textos dos botões estão completos
"""

import sys
import os
import tkinter as tk

# Adicionar o diretório src ao path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from database import DatabaseManager
from auth import AuthManager
from gui.main_window import MainWindow

def test_button_text():
    """Testa se os textos dos botões estão completos"""
    print("🔤 TESTE DOS TEXTOS DOS BOTÕES")
    print("=" * 50)
    
    try:
        # Inicializar banco de dados
        db_manager = DatabaseManager()
        db_manager.initialize_database()
        
        auth_manager = AuthManager(db_manager)
        
        # Fazer login
        user_data = auth_manager.authenticate_user('admin', 'admin123')
        if not user_data:
            print("❌ Falha no login!")
            return
        
        print(f"✅ Login realizado: {user_data['full_name']}")
        
        # Criar janela principal
        root = tk.Tk()
        root.title("Teste Textos dos Botões")
        root.geometry("1200x800")
        
        # Função de logout mock
        def mock_logout():
            print("Logout chamado")
        
        # Criar MainWindow
        main_window = MainWindow(root, db_manager, auth_manager, user_data, mock_logout)
        
        print("✅ Janela principal criada com sucesso!")
        
        # Verificar botões após renderização
        def check_button_texts():
            try:
                root.update_idletasks()
                
                # Textos esperados para cada aba
                expected_texts = {
                    'carteiras': ['Nova Carteira', 'Editar', 'Excluir'],
                    'transacoes': ['Nova Receita', 'Nova Despesa', 'Editar', 'Excluir', 'Filtrar']
                }
                
                # Navegar para aba de carteiras
                main_window.notebook.select(1)  # Carteiras
                root.update_idletasks()
                
                print("\n💳 VERIFICANDO ABA DE CARTEIRAS:")
                wallet_buttons = []
                def find_wallet_buttons(widget):
                    if hasattr(widget, 'winfo_class') and widget.winfo_class() == 'TButton':
                        text = widget['text']
                        if text in expected_texts['carteiras']:
                            wallet_buttons.append({
                                'text': text,
                                'width': widget.winfo_width(),
                                'height': widget.winfo_height()
                            })
                    for child in widget.winfo_children():
                        find_wallet_buttons(child)
                
                find_wallet_buttons(root)
                
                for btn in wallet_buttons:
                    text_length = len(btn['text'])
                    estimated_min_width = text_length * 8  # 8 pixels por caractere (estimativa)
                    
                    print(f"   - '{btn['text']}':")
                    print(f"     Largura: {btn['width']}px")
                    print(f"     Caracteres: {text_length}")
                    print(f"     Largura mínima estimada: {estimated_min_width}px")
                    
                    if btn['width'] >= estimated_min_width:
                        print(f"     ✅ Texto deve estar completo!")
                    else:
                        print(f"     ⚠️  Texto pode estar cortado!")
                
                # Navegar para aba de transações
                main_window.notebook.select(2)  # Transações
                root.update_idletasks()
                
                print("\n💰 VERIFICANDO ABA DE TRANSAÇÕES:")
                transaction_buttons = []
                def find_transaction_buttons(widget):
                    if hasattr(widget, 'winfo_class') and widget.winfo_class() == 'TButton':
                        text = widget['text']
                        if text in expected_texts['transacoes']:
                            transaction_buttons.append({
                                'text': text,
                                'width': widget.winfo_width(),
                                'height': widget.winfo_height()
                            })
                    for child in widget.winfo_children():
                        find_transaction_buttons(child)
                
                find_transaction_buttons(root)
                
                for btn in transaction_buttons:
                    text_length = len(btn['text'])
                    estimated_min_width = text_length * 8
                    
                    print(f"   - '{btn['text']}':")
                    print(f"     Largura: {btn['width']}px")
                    print(f"     Caracteres: {text_length}")
                    print(f"     Largura mínima estimada: {estimated_min_width}px")
                    
                    if btn['width'] >= estimated_min_width:
                        print(f"     ✅ Texto deve estar completo!")
                    else:
                        print(f"     ⚠️  Texto pode estar cortado!")
                
                print(f"\n📊 RESUMO:")
                print(f"   Botões de carteiras verificados: {len(wallet_buttons)}")
                print(f"   Botões de transações verificados: {len(transaction_buttons)}")
                
            except Exception as e:
                print(f"❌ Erro ao verificar textos: {str(e)}")
                import traceback
                traceback.print_exc()
        
        # Aguardar renderização e verificar
        root.after(1000, check_button_texts)
        
        print("\n✅ TESTE DOS TEXTOS INICIADO!")
        print("🔍 Verificando textos em 1 segundo...")
        
        # Executar por tempo limitado
        root.after(5000, root.quit)
        root.mainloop()
        root.destroy()
        
    except Exception as e:
        print(f"❌ ERRO GERAL: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_button_text()
