#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Formulário de Usuário Completamente Novo e Melhorado
Criado do zero com design moderno e funcionalidades avançadas
"""

import tkinter as tk
from tkinter import ttk, messagebox
import re

class UserFormNew:
    def __init__(self, parent, db_manager, user_id=None):
        self.parent = parent
        self.db_manager = db_manager
        self.user_id = user_id
        self.result = False
        
        # Configurações visuais
        self.config = {
            'title': '👤 Novo Usuário' if not user_id else '👤 Editar Usuário',
            'color': '#9b59b6',
            'button_text': '👤 Criar Usuário' if not user_id else '👤 Salvar Usuário'
        }
        
        # Criar janela principal
        self.create_main_window()
        
        # Criar interface
        self.create_interface()
        
        # Carregar dados se for edição
        if user_id:
            self.load_user_data()
        
        # Configurar eventos
        self.setup_events()
        
        # Focar no primeiro campo
        self.name_entry.focus()
    
    def create_main_window(self):
        """Cria a janela principal"""
        self.window = tk.Toplevel(self.parent)
        self.window.title(self.config['title'])
        self.window.geometry("700x600")
        self.window.resizable(True, True)
        self.window.configure(bg='#f8f9fa')

        # Centralizar janela
        self.center_window()

        # Configurar janela
        self.window.transient(self.parent)
        self.window.grab_set()
    
    def center_window(self):
        """Centraliza a janela na tela"""
        self.window.update_idletasks()
        width = self.window.winfo_width()
        height = self.window.winfo_height()
        x = (self.window.winfo_screenwidth() // 2) - (width // 2)
        y = (self.window.winfo_screenheight() // 2) - (height // 2)
        self.window.geometry(f'{width}x{height}+{x}+{y}')
    
    def create_interface(self):
        """Cria a interface completa"""
        # Container principal
        main_container = tk.Frame(self.window, bg='#f8f9fa')
        main_container.pack(fill=tk.BOTH, expand=True, padx=15, pady=15)

        # Cabeçalho
        self.create_header(main_container)

        # Área de conteúdo com scroll
        self.create_content_area(main_container)

        # Rodapé com botões
        self.create_footer(main_container)
    
    def create_header(self, parent):
        """Cria o cabeçalho"""
        header_frame = tk.Frame(parent, bg=self.config['color'], height=60)
        header_frame.pack(fill=tk.X, pady=(0, 15))
        header_frame.pack_propagate(False)

        # Título principal
        title_label = tk.Label(
            header_frame,
            text=self.config['title'],
            font=("Arial", 16, "bold"),
            fg='white',
            bg=self.config['color']
        )
        title_label.pack(expand=True)

        # Subtítulo
        subtitle = "Cadastre um novo usuário" if not self.user_id else "Edite as informações"
        subtitle_label = tk.Label(
            header_frame,
            text=subtitle,
            font=("Arial", 10),
            fg='white',
            bg=self.config['color']
        )
        subtitle_label.pack()
    
    def create_content_area(self, parent):
        """Cria a área de conteúdo com scroll"""
        # Frame para scroll
        canvas_frame = tk.Frame(parent, bg='white', relief=tk.RAISED, bd=1)
        canvas_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 15))

        # Canvas e scrollbar
        canvas = tk.Canvas(canvas_frame, bg='white', highlightthickness=0)
        scrollbar = ttk.Scrollbar(canvas_frame, orient="vertical", command=canvas.yview)
        self.scrollable_frame = tk.Frame(canvas, bg='white')

        # Configurar scroll
        self.scrollable_frame.bind(
            "<Configure>",
            lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
        )

        canvas.create_window((0, 0), window=self.scrollable_frame, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar.set)

        # Pack canvas e scrollbar
        canvas.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")

        # Bind mousewheel para scroll
        def _on_mousewheel(event):
            canvas.yview_scroll(int(-1*(event.delta/120)), "units")
        canvas.bind_all("<MouseWheel>", _on_mousewheel)

        # Criar seções do formulário
        self.create_personal_info_section()
        self.create_credentials_section()
        self.create_permissions_section()
    
    def create_personal_info_section(self):
        """Cria seção de informações pessoais"""
        section_frame = self.create_section_frame("👤 Informações Pessoais", "#3498db")

        # Nome completo
        self.create_field(
            section_frame,
            "👤 Nome Completo *",
            "name",
            placeholder="Digite o nome completo do usuário"
        )

        # Email
        self.create_field(
            section_frame,
            "📧 Email *",
            "email",
            placeholder="<EMAIL>"
        )
    
    def create_credentials_section(self):
        """Cria seção de credenciais"""
        section_frame = self.create_section_frame("🔐 Credenciais de Acesso", "#e74c3c")

        # Nome de usuário
        self.create_field(
            section_frame,
            "🔑 Nome de Usuário *",
            "username",
            placeholder="Digite um nome de usuário único"
        )

        # Linha com senhas
        password_row = tk.Frame(section_frame, bg='white')
        password_row.pack(fill=tk.X, pady=(0, 10))

        # Senha (50%)
        password_frame = tk.Frame(password_row, bg='white')
        password_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=(0, 10))

        tk.Label(
            password_frame,
            text="🔒 Senha *",
            font=("Arial", 10, "bold"),
            bg='white',
            fg='#2c3e50'
        ).pack(anchor=tk.W, pady=(0, 5))

        self.password_entry = tk.Entry(
            password_frame,
            font=("Arial", 10),
            relief=tk.SOLID,
            bd=1,
            show="*"
        )
        self.password_entry.pack(fill=tk.X)

        # Confirmar senha (50%)
        confirm_frame = tk.Frame(password_row, bg='white')
        confirm_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=(10, 0))

        tk.Label(
            confirm_frame,
            text="🔒 Confirmar Senha *",
            font=("Arial", 10, "bold"),
            bg='white',
            fg='#2c3e50'
        ).pack(anchor=tk.W, pady=(0, 5))

        self.confirm_password_entry = tk.Entry(
            confirm_frame,
            font=("Arial", 10),
            relief=tk.SOLID,
            bd=1,
            show="*"
        )
        self.confirm_password_entry.pack(fill=tk.X)

        # Indicador de força da senha
        self.password_strength_label = tk.Label(
            section_frame,
            text="💡 Digite uma senha para ver sua força",
            font=("Arial", 9),
            bg='white',
            fg='#7f8c8d'
        )
        self.password_strength_label.pack(anchor=tk.W, pady=(5, 0))

        # Bind para verificar força da senha
        self.password_entry.bind('<KeyRelease>', self.check_password_strength)
    
    def create_permissions_section(self):
        """Cria seção de permissões"""
        section_frame = self.create_section_frame("🛡️ Permissões e Configurações", "#27ae60")

        # Linha com tipo e status
        config_row = tk.Frame(section_frame, bg='white')
        config_row.pack(fill=tk.X, pady=(0, 10))

        # Tipo de usuário (60%)
        type_frame = tk.Frame(config_row, bg='white')
        type_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=(0, 10))

        tk.Label(
            type_frame,
            text="🛡️ Tipo de Usuário *",
            font=("Arial", 10, "bold"),
            bg='white',
            fg='#2c3e50'
        ).pack(anchor=tk.W, pady=(0, 5))

        self.user_type_combo = ttk.Combobox(
            type_frame,
            state="readonly",
            font=("Arial", 10),
            height=4
        )
        self.user_type_combo['values'] = [
            '👤 Usuário Padrão',
            '🛡️ Administrador'
        ]
        self.user_type_combo.current(0)
        self.user_type_combo.pack(fill=tk.X)

        # Status (40%)
        status_frame = tk.Frame(config_row, bg='white')
        status_frame.pack(side=tk.LEFT, fill=tk.Y, padx=(10, 0))

        tk.Label(
            status_frame,
            text="✅ Status",
            font=("Arial", 10, "bold"),
            bg='white',
            fg='#2c3e50'
        ).pack(anchor=tk.W, pady=(0, 5))

        self.is_active_var = tk.BooleanVar(value=True)
        self.is_active_check = tk.Checkbutton(
            status_frame,
            text="✅ Usuário ativo",
            variable=self.is_active_var,
            font=("Arial", 9, "bold"),
            bg='white',
            fg='#27ae60',
            selectcolor='white',
            activebackground='white',
            activeforeground='#27ae60'
        )
        self.is_active_check.pack(anchor=tk.W, pady=(5, 0))

        # Observações
        tk.Label(
            section_frame,
            text="📝 Observações",
            font=("Arial", 10, "bold"),
            bg='white',
            fg='#2c3e50'
        ).pack(anchor=tk.W, pady=(10, 5))

        self.notes_text = tk.Text(
            section_frame,
            height=3,
            font=("Arial", 9),
            relief=tk.SOLID,
            bd=1,
            wrap=tk.WORD
        )
        self.notes_text.pack(fill=tk.X, pady=(0, 0))

        # Placeholder para observações
        self.notes_text.insert('1.0', "Digite observações sobre este usuário...")
        self.notes_text.bind('<FocusIn>', self.clear_notes_placeholder)
        self.notes_text.bind('<FocusOut>', self.restore_notes_placeholder)
    

    
    def create_section_frame(self, title, color):
        """Cria um frame de seção"""
        section_frame = tk.LabelFrame(
            self.scrollable_frame,
            text=title,
            font=("Arial", 11, "bold"),
            bg='white',
            fg=color,
            relief=tk.GROOVE,
            bd=1,
            labelanchor='n'
        )
        section_frame.pack(fill=tk.X, padx=15, pady=(0, 15))
        section_frame.configure(padx=15, pady=15)

        return section_frame
    
    def create_field(self, parent, label_text, field_name, placeholder="", width=None):
        """Cria um campo de entrada"""
        # Label
        label = tk.Label(
            parent,
            text=label_text,
            font=("Arial", 10, "bold"),
            bg='white',
            fg='#2c3e50'
        )
        label.pack(anchor=tk.W, pady=(0, 5))

        # Campo
        field = tk.Entry(
            parent,
            font=("Arial", 10),
            relief=tk.SOLID,
            bd=1,
            width=width
        )
        field.pack(fill=tk.X, pady=(0, 15))

        if placeholder:
            field.insert(0, placeholder)
            field.bind('<FocusIn>', lambda e: self.clear_placeholder(e, placeholder))
            field.bind('<FocusOut>', lambda e: self.restore_placeholder(e, placeholder))

        setattr(self, f"{field_name}_entry", field)
        return field
    
    def create_footer(self, parent):
        """Cria o rodapé com botões"""
        footer_frame = tk.Frame(parent, bg='#ecf0f1', height=70)
        footer_frame.pack(fill=tk.X)
        footer_frame.pack_propagate(False)

        # Container dos botões
        button_container = tk.Frame(footer_frame, bg='#ecf0f1')
        button_container.pack(expand=True)

        # Botão cancelar
        cancel_btn = tk.Button(
            button_container,
            text="❌ Cancelar",
            command=self.cancel,
            font=("Arial", 11, "bold"),
            bg='#e74c3c',
            fg='white',
            relief=tk.RAISED,
            bd=2,
            padx=20,
            pady=8,
            cursor='hand2'
        )
        cancel_btn.pack(side=tk.LEFT, padx=(0, 20))

        # Botão salvar
        save_btn = tk.Button(
            button_container,
            text=self.config['button_text'],
            command=self.save,
            font=("Arial", 11, "bold"),
            bg=self.config['color'],
            fg='white',
            relief=tk.RAISED,
            bd=2,
            padx=20,
            pady=8,
            cursor='hand2'
        )
        save_btn.pack(side=tk.LEFT)

        # Informações de ajuda
        help_label = tk.Label(
            footer_frame,
            text="💡 Dicas: Tab para navegar • Enter para salvar • Esc para cancelar • * = Obrigatório",
            font=("Arial", 9),
            bg='#ecf0f1',
            fg='#7f8c8d'
        )
        help_label.pack(side=tk.BOTTOM, pady=(0, 10))
    
    def check_password_strength(self, event):
        """Verifica a força da senha"""
        password = self.password_entry.get()
        
        if not password:
            self.password_strength_label.config(
                text="💡 Digite uma senha para ver sua força",
                fg='#7f8c8d'
            )
            return
        
        score = 0
        feedback = []
        
        # Critérios de força
        if len(password) >= 8:
            score += 1
        else:
            feedback.append("mín. 8 caracteres")
        
        if re.search(r'[A-Z]', password):
            score += 1
        else:
            feedback.append("letra maiúscula")
        
        if re.search(r'[a-z]', password):
            score += 1
        else:
            feedback.append("letra minúscula")
        
        if re.search(r'\d', password):
            score += 1
        else:
            feedback.append("número")
        
        if re.search(r'[!@#$%^&*(),.?":{}|<>]', password):
            score += 1
        else:
            feedback.append("símbolo")
        
        # Definir força e cor
        if score <= 2:
            strength = "Fraca"
            color = '#e74c3c'
        elif score <= 3:
            strength = "Média"
            color = '#f39c12'
        elif score <= 4:
            strength = "Boa"
            color = '#27ae60'
        else:
            strength = "Excelente"
            color = '#2ecc71'
        
        # Atualizar label
        if feedback:
            text = f"🔒 Força: {strength} (falta: {', '.join(feedback)})"
        else:
            text = f"🔒 Força: {strength} ✅"
        
        self.password_strength_label.config(text=text, fg=color)
    
    def clear_placeholder(self, event, placeholder):
        """Remove placeholder ao focar"""
        if event.widget.get() == placeholder:
            event.widget.delete(0, tk.END)
    
    def restore_placeholder(self, event, placeholder):
        """Restaura placeholder se vazio"""
        if not event.widget.get():
            event.widget.insert(0, placeholder)
    
    def clear_notes_placeholder(self, event):
        """Remove placeholder das observações"""
        if self.notes_text.get('1.0', tk.END).strip() == "Digite observações sobre este usuário...":
            self.notes_text.delete('1.0', tk.END)
    
    def restore_notes_placeholder(self, event):
        """Restaura placeholder das observações"""
        if not self.notes_text.get('1.0', tk.END).strip():
            self.notes_text.insert('1.0', "Digite observações sobre este usuário...")
    
    def load_user_data(self):
        """Carrega dados do usuário para edição"""
        # Implementar carregamento de dados
        pass
    
    def setup_events(self):
        """Configura eventos"""
        self.window.bind('<Return>', lambda e: self.save())
        self.window.bind('<Escape>', lambda e: self.cancel())
    
    def save(self):
        """Salva o usuário"""
        try:
            if self.validate_form():
                messagebox.showinfo("Sucesso", "Usuário salvo com sucesso!")
                self.result = True
                self.window.destroy()
        except Exception as e:
            messagebox.showerror("Erro", str(e))
    
    def validate_form(self):
        """Valida o formulário"""
        # Implementar validações
        return True
    
    def cancel(self):
        """Cancela a operação"""
        self.result = False
        self.window.destroy()
